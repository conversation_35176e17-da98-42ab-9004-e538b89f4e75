import type { <PERSON>ada<PERSON> } from "next"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Breadcrumb } from "@/components/breadcrumb"
import { AcademicIntegrityDashboard } from "@/components/academic-integrity/dashboard"

export const metadata: Metadata = {
  title: "Academic Integrity Monitor - StudentAIDetector",
  description:
    "Monitor and analyze student submissions for potential plagiarism, collusion, and academic integrity issues.",
  keywords: [
    "academic integrity monitor",
    "plagiarism detection",
    "collusion detection",
    "academic dishonesty prevention",
    "student submission analysis",
    "integrity reports",
    "similarity detection",
    "source verification",
    "educational integrity tool",
    "academic misconduct prevention",
  ],
  canonical: "/resources/academic-integrity-monitor",
}

export default function AcademicIntegrityMonitorPage() {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-8">
        <Breadcrumb />
      </div>

      <div className="container flex-grow pb-12">
        <div className="max-w-7xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold tracking-tight mb-2">Academic Integrity Monitor</h1>
            <p className="text-xl text-muted-foreground">
              Comprehensive tools to monitor and analyze student submissions for academic integrity
            </p>
          </div>

          <AcademicIntegrityDashboard />
        </div>
      </div>

      <Footer />
    </main>
  )
}
