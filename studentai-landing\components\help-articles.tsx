interface HelpArticlesProps {
  articles: { id: string; title: string; description: string; link: string }[]
}

export function HelpArticles({ articles }: HelpArticlesProps) {
  return (
    <div className="mt-4">
      <h4 className="text-sm font-medium">Helpful Articles</h4>
      <ul className="space-y-2">
        {articles.map((article) => (
          <li key={article.id} className="text-xs">
            <a href={article.link} className="hover:underline">
              {article.title}
            </a>
          </li>
        ))}
      </ul>
    </div>
  )
}
