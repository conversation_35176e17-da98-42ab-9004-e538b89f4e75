import { getPostsByTag } from "@/lib/blog"
import { notFound } from "next/navigation"
import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { PostCard } from "@/components/blog/post-card"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"

interface TagPageProps {
  params: {
    tag: string
  }
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const tag = params.tag.replace(/-/g, " ")

  return {
    title: `${tag} Articles | StudentAidDetector Blog`,
    description: `Browse our collection of articles and resources tagged with ${tag}.`,
  }
}

export default function TagPage({ params }: TagPageProps) {
  const tagSlug = params.tag
  const tag = tagSlug.replace(/-/g, " ")

  const posts = getPostsByTag(tag)

  if (posts.length === 0) {
    notFound()
  }

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-12 flex-grow">
        <Link href="/blog" className="inline-block mb-8">
          <Button variant="ghost" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to all posts
          </Button>
        </Link>

        <header className="mb-12">
          <h1 className="text-3xl md:text-4xl font-bold mb-4">Tag: {tag}</h1>
          <p className="text-xl text-muted-foreground">
            Browse our collection of articles and resources tagged with "{tag}"
          </p>
        </header>

        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {posts.map((post) => (
            <PostCard key={post.id} post={post} />
          ))}
        </div>
      </div>

      <Footer />
    </main>
  )
}
