import type { Metadata } from "next"
import { generateMetaTags } from "@/lib/seo-utils"
import AIDetectorClientPage from "./AIDetectorClientPage"

export const metadata: Metadata = generateMetaTags({
  title: "AI Detector Tool - Identify AI-Generated Content with High Accuracy",
  description:
    "Detect AI-written content from ChatGPT, GPT-4, <PERSON> and other models with our advanced AI detection tool. Perfect for educators, students, and content professionals.",
  keywords: [
    "AI detector",
    "AI content detector",
    "AI checker",
    "ChatGPT detector",
    "AI text checker",
    "detect AI writing",
    "AI detection tool",
    "AI content identification",
    "AI text analysis",
    "AI writing detector",
  ],
  canonical: "/tools/ai-detector",
})

export default function AIDetectorPage() {
  return <AIDetectorClientPage />
}
