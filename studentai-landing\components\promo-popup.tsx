"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  <PERSON>,
  <PERSON>,
  Sparkles,
  ArrowRight,
  Star,
  Check,
  Gift,
  Users,
  Timer,
  Shield,
  Zap,
  Layers,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

export function PromoPopup() {
  const [isVisible, setIsVisible] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [days, setDays] = useState(3);
  const [hours, setHours] = useState(14);
  const [minutes, setMinutes] = useState(22);

  // Set discount amount based on pricing strategy
  const discountPercent = 40;
  const bonusCredits = 10000;
  const originalPrice = 29;
  const discountedPrice = 17;

  useEffect(() => {
    // Show popup after delay
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  // Handle countdown timer
  useEffect(() => {
    const countdownInterval = setInterval(() => {
      if (minutes > 0) {
        setMinutes(minutes - 1);
      } else {
        if (hours > 0) {
          setHours(hours - 1);
          setMinutes(59);
        } else {
          if (days > 0) {
            setDays(days - 1);
            setHours(23);
            setMinutes(59);
          } else {
            clearInterval(countdownInterval);
          }
        }
      }
    }, 60000); // Update every minute

    return () => clearInterval(countdownInterval);
  }, [days, hours, minutes]);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsVisible(false);
      setIsClosing(false);
    }, 400);
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/70 backdrop-blur-md">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: isClosing ? 0.8 : 1, opacity: isClosing ? 0 : 1 }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 30,
          }}
          className="relative w-full max-w-5xl overflow-hidden"
        >
          {/* Banner container */}
          <div className="relative rounded-2xl shadow-2xl overflow-hidden">
            {/* Background gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/90 via-indigo-900/90 to-purple-900/90 z-10"></div>

            {/* Animated background patterns */}
            <div className="absolute inset-0 z-0">
              <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_25%_25%,rgba(79,70,229,0.15)_0%,transparent_50%)]"></div>
              <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_75%_75%,rgba(147,51,234,0.15)_0%,transparent_50%)]"></div>
              <div
                className="absolute inset-0 opacity-20"
                style={{
                  backgroundImage:
                    "linear-gradient(#4f46e5 1px, transparent 1px), linear-gradient(90deg, #4f46e5 1px, transparent 1px)",
                  backgroundSize: "20px 20px",
                }}
              ></div>
            </div>

            {/* Content container */}
            <div className="relative z-30 flex flex-col md:flex-row items-center">
              {/* Left side - Image */}
              <div className="md:w-2/5 relative overflow-hidden">
                <motion.div
                  initial={{ x: -100, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, type: "spring", bounce: 0.3 }}
                  className="p-4 md:p-8"
                >
                  <div className="relative h-[250px] md:h-[400px] w-full">
                    <Image
                      src="/aidetector.webp"
                      alt="AI Detector Robot"
                      fill
                      className="object-contain"
                      priority
                    />
                  </div>

                  {/* Animated glow effect */}
                  <motion.div
                    className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-blue-500/30 blur-3xl -z-10"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />

                  {/* Price badge */}
                  <motion.div
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.6, duration: 0.5 }}
                    className="absolute top-10 right-10 md:top-6 md:right-6 w-20 h-20 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex flex-col items-center justify-center text-white shadow-lg"
                  >
                    <div className="text-xs font-medium">SAVE</div>
                    <div className="text-xl font-bold">{discountPercent}%</div>
                    <div className="text-xs">TODAY</div>
                  </motion.div>
                </motion.div>
              </div>

              {/* Right side - Content */}
              <div className="md:w-3/5 p-6 md:p-10 text-white">
                {/* Limited Time Badge */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  className="flex items-center mb-4"
                >
                  <div className="p-1 bg-white/20 rounded-full mr-3 backdrop-blur-sm">
                    <Clock className="w-5 h-5 text-yellow-300" />
                  </div>
                  <span className="font-medium uppercase tracking-wider text-yellow-300 text-sm">
                    Limited Time Offer
                  </span>
                </motion.div>

                {/* Heading */}
                <motion.div
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-2 leading-tight">
                    <span className="bg-gradient-to-r from-blue-300 to-purple-200 text-transparent bg-clip-text">
                      40% OFF Premium
                    </span>
                  </h2>
                  <p className="text-2xl md:text-3xl font-bold mb-3">
                    + {bonusCredits.toLocaleString()} Extra Credits
                  </p>
                </motion.div>

                {/* Countdown timer */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  className="mb-4"
                >
                  <p className="text-sm text-blue-200 mb-1">Offer ends in:</p>
                  <div className="flex space-x-3">
                    <div className="bg-white/10 backdrop-blur-sm px-3 py-2 rounded-md">
                      <span className="text-xl font-mono font-bold">
                        {days}
                      </span>
                      <span className="text-xs ml-1">days</span>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm px-3 py-2 rounded-md">
                      <span className="text-xl font-mono font-bold">
                        {hours}
                      </span>
                      <span className="text-xs ml-1">hrs</span>
                    </div>
                    <div className="bg-white/10 backdrop-blur-sm px-3 py-2 rounded-md">
                      <span className="text-xl font-mono font-bold">
                        {minutes}
                      </span>
                      <span className="text-xs ml-1">min</span>
                    </div>
                  </div>
                </motion.div>

                {/* Price comparison */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="mb-6"
                >
                  <div className="flex items-baseline">
                    <span className="text-gray-400 line-through text-lg">
                      ${originalPrice}
                    </span>
                    <span className="text-3xl font-bold ml-2">
                      ${discountedPrice}
                    </span>
                    <span className="text-sm ml-1 text-gray-300">/month</span>
                  </div>
                  <p className="text-blue-200 text-sm">
                    Billed annually. Cancel anytime.
                  </p>
                </motion.div>

                {/* Feature highlights */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="mb-6 space-y-2"
                >
                  <div className="flex items-start">
                    <div className="bg-green-500/20 p-1 rounded mr-3 text-green-300">
                      <Check className="h-4 w-4" />
                    </div>
                    <div>
                      <span className="text-sm">
                        Unlimited AI detection with higher accuracy
                      </span>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-500/20 p-1 rounded mr-3 text-green-300">
                      <Check className="h-4 w-4" />
                    </div>
                    <div>
                      <span className="text-sm">
                        {bonusCredits.toLocaleString()} bonus credits for bulk
                        analysis
                      </span>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-500/20 p-1 rounded mr-3 text-green-300">
                      <Check className="h-4 w-4" />
                    </div>
                    <div>
                      <span className="text-sm">
                        Advanced analytics and reporting
                      </span>
                    </div>
                  </div>
                </motion.div>

                {/* Bonus referral section */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="relative mb-6 bg-white/10 backdrop-blur-sm p-3 rounded-lg border border-white/20"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-yellow-500/20 text-yellow-300 shrink-0">
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{
                          duration: 10,
                          repeat: Infinity,
                          ease: "linear",
                        }}
                      >
                        <Gift className="w-5 h-5" />
                      </motion.div>
                    </div>
                    <div>
                      <p className="font-medium text-sm">
                        BONUS: Refer & Earn More
                      </p>
                      <p className="text-xs text-blue-200">
                        Get +5,000 free credits for each friend who signs up
                      </p>
                    </div>
                  </div>
                </motion.div>

                {/* CTA Button */}
                <motion.div
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="space-y-3"
                >
                  <Link href="/pricing?promo=FALL40" className="block">
                    <motion.div
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      <Button className="w-full py-6 md:py-7 text-lg md:text-xl bg-gradient-to-r from-blue-400 to-indigo-500 hover:from-blue-500 hover:to-indigo-600 text-white shadow-lg shadow-blue-600/30 transition-all hover:shadow-indigo-500/40 border border-blue-400/20">
                        <motion.span
                          animate={{ x: [0, 5, 0] }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            repeatDelay: 2,
                          }}
                          className="flex items-center"
                        >
                          Get 40% Off Now
                          <ArrowRight className="ml-2 h-5 w-5" />
                        </motion.span>
                      </Button>
                    </motion.div>
                  </Link>
                  <p className="text-xs text-center text-blue-200">
                    Offer valid for new and existing users. No coupon needed.
                  </p>
                </motion.div>
              </div>
            </div>

            {/* Close button */}
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors z-40 backdrop-blur-sm border border-white/10"
            >
              <X className="w-5 h-5 text-white" />
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
