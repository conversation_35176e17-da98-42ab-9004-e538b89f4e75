"use client";

import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  CheckCircle,
  ArrowRight,
  Shield,
  Zap,
  Lock,
  ChevronRight,
  Search,
  FileText,
} from "lucide-react";
import AiDetectionTool from "@/components/ai-detection-tool";
import TestimonialSection from "@/components/testimonial-section";
import FaqSection from "@/components/faq-section";
import PricingSection from "@/components/pricing-section";
import HowItWorksSection from "@/components/how-it-works-section";
import TransformationSection from "@/components/transformation-section";
import ProblemSection from "@/components/problem-section";
import VisualProof from "@/components/visual-proof";
import { Navbar } from "@/components/navbar";
import AnimatedParticles from "@/components/animated-particles";
import { Footer } from "@/components/footer";
import { motion } from "framer-motion";
import AudienceSection from "@/components/audience-section";
import AIDetectionProcess from "@/components/ai-detection-process";
import HowDetectionWorks from "@/components/how-detection-works";
import AdvancedDetectionFeatures from "@/components/advanced-detection-features";

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center">
      {/* Header with Logo and Navigation */}
      <Navbar />
      {/* Reimagined Hero Section with enhanced visuals */}
      <section className="w-full relative overflow-hidden pt-20 md:pt-24 lg:pt-28">
        {/* Dynamic animated background */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Main gradient background */}
          <div className="absolute inset-0 bg-gradient-to-b from-white via-blue-50/30 to-white dark:from-gray-900 dark:via-blue-900/10 dark:to-gray-900"></div>

          {/* Animated floating shapes */}
          <div className="absolute top-0 left-0 w-full h-full">
            {/* Animated circles */}
            <div
              className="absolute top-1/4 right-[10%] w-[300px] h-[300px] rounded-full bg-gradient-to-br from-blue-400/20 to-indigo-500/20 dark:from-blue-400/10 dark:to-indigo-500/10 blur-3xl animate-pulse"
              style={{ animationDuration: "8s" }}
            ></div>
            <div
              className="absolute bottom-[20%] left-[5%] w-[250px] h-[250px] rounded-full bg-gradient-to-tr from-purple-500/20 to-pink-500/20 dark:from-purple-500/10 dark:to-pink-500/10 blur-3xl animate-pulse"
              style={{ animationDuration: "10s", animationDelay: "1s" }}
            ></div>
            <div
              className="absolute top-[10%] left-[15%] w-[200px] h-[200px] rounded-full bg-gradient-to-r from-cyan-400/20 to-teal-500/20 dark:from-cyan-400/10 dark:to-teal-500/10 blur-3xl animate-pulse"
              style={{ animationDuration: "12s", animationDelay: "2s" }}
            ></div>

            {/* Replace the inline particles with the client component */}
            <AnimatedParticles />
          </div>

          {/* Enhanced subtle grid with animation */}
          <div className="absolute inset-0 bg-[linear-gradient(to_right,#4444440a_1px,transparent_1px),linear-gradient(to_bottom,#4444440a_1px,transparent_1px)] bg-[size:64px_64px] dark:bg-[linear-gradient(to_right,#ffffff0a_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0a_1px,transparent_1px)]">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-blue-900/5 animate-scan"></div>
          </div>
        </div>

        <div className="container mx-auto px-4">
          {/* Hero content */}
          <div className="max-w-screen-xl mx-auto relative z-10">
            {/* Two-column layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-8 items-center">
              {/* Left column: Core message */}
              <div className="space-y-8 pt-8 lg:pt-16">
                {/* Enhanced status bar */}
                <div className="inline-flex items-center gap-2 px-4 py-1.5 rounded-full bg-white/90 dark:bg-gray-800/90 shadow-lg backdrop-blur-sm border border-gray-100/50 dark:border-gray-700/50 animate-fade-in">
                  <span className="relative flex h-2 w-2">
                    <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                    <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                  </span>
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                    Industry-Leading AI Detection Technology
                  </span>
                </div>

                {/* Enhanced headline with animated reveal */}
                <div
                  className="space-y-4 animate-reveal"
                  style={{ animationDuration: "0.7s" }}
                >
                  <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-6xl font-extrabold tracking-tight leading-[1.1]">
                    <span className=" bg-clip-text text-transparent bg-gradient-to-r from-gray-800 via-gray-900 to-black dark:from-gray-100 dark:via-gray-200 dark:to-white">
                      Identify AI-Generated Content
                    </span>{" "}
                    <span className=" mt-1 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-indigo-600 to-violet-600 dark:from-blue-400 dark:via-indigo-400 dark:to-violet-400 animate-gradient-x">
                      With <br /> 99% Accuracy
                    </span>
                  </h1>
                  <p
                    className="text-sm md:text-base text-gray-600 dark:text-gray-400 max-w-xl animate-reveal"
                    style={{
                      animationDelay: "0.2s",
                      animationDuration: "0.7s",
                    }}
                  >
                    Our advanced AI detection tool helps educators verify
                    student work and content professionals maintain authenticity
                  </p>{" "}
                  <p
                    className="text-sm md:text-base text-gray-600 dark:text-gray-400 max-w-xl animate-reveal"
                    style={{
                      animationDelay: "0.2s",
                      animationDuration: "0.7s",
                    }}
                  >
                    StudentAIDetector uses proprietary multi-layered detection
                    technology to identify content created by ChatGPT, GPT-4,
                    Claude, and other AI models with unmatched precision.
                    Perfect for educators verifying academic integrity and
                    content managers ensuring quality.
                  </p>
                </div>

                {/* Enhanced CTA section with animations */}
                <div
                  className="flex flex-col sm:flex-row gap-4 md:gap-6 animate-reveal"
                  style={{ animationDelay: "0.4s", animationDuration: "0.7s" }}
                >
                  <Button
                    onClick={() =>
                      window.open(
                        "https://app.studentaidetector.com/signup",
                        "_blank"
                      )
                    }
                    size="lg"
                    className="text-base px-8 py-6 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800 text-white shadow-lg hover:shadow-blue-500/25 group transition-all duration-300 ease-out transform hover:-translate-y-1 hover:shadow-xl rounded-lg"
                  >
                    Try AI Detector Now
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                  <Button
                    onClick={() =>
                      window.open(
                        "https://app.studentaidetector.com/signup",
                        "_blank"
                      )
                    }
                    size="lg"
                    variant="outline"
                    className="text-base px-8 py-6 border-gray-300 text-gray-700 hover:bg-gray-50/80 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-800/80 shadow-sm hover:shadow-md transition-all duration-300 ease-out transform hover:-translate-y-1 rounded-lg"
                  >
                    Learn more
                    <ChevronRight className="ml-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-0.5" />
                  </Button>
                </div>

                {/* Enhanced trust proof points with animations */}
                <div className="flex flex-wrap gap-4 mt-4">
                  <div className="flex items-center gap-2 py-1 px-3 rounded-full bg-white/70 dark:bg-gray-800/70 shadow-sm backdrop-blur-sm border border-gray-100/50 dark:border-gray-700/50 animate-fade-in-delay-1">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                      GPT-4 Detection
                    </span>
                  </div>
                  <div className="flex items-center gap-2 py-1 px-3 rounded-full bg-white/70 dark:bg-gray-800/70 shadow-sm backdrop-blur-sm border border-gray-100/50 dark:border-gray-700/50 animate-fade-in-delay-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                      SOC 2 Compliant
                    </span>
                  </div>
                  <div className="flex items-center gap-2 py-1 px-3 rounded-full bg-white/70 dark:bg-gray-800/70 shadow-sm backdrop-blur-sm border border-gray-100/50 dark:border-gray-700/50 animate-fade-in-delay-3">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                      FERPA Compliant
                    </span>
                  </div>
                </div>
              </div>

              {/* Right column: Clean, elegant demo with wow factor */}
              <div className="relative">
                {/* Subtle accent gradient - reduced to just one elegant element */}
                <div className="absolute -z-10 w-full h-full rounded-3xl bg-gradient-to-br from-blue-500/10 via-indigo-500/10 to-transparent dark:from-blue-500/15 dark:via-indigo-500/15 blur-2xl transform -translate-y-8 translate-x-4"></div>

                {/* Main demo card with elegant reveal animation */}
                <div
                  className="relative z-10 overflow-hidden rounded-2xl border border-gray-100/80 dark:border-gray-700/80 shadow-xl bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm transform transition duration-700 hover:translate-y-[-4px]"
                  style={{
                    animation: "reveal-card 1s ease forwards",
                    transformOrigin: "center",
                  }}
                >
                  {/* Card header */}
                  <div className="px-6 py-5 flex items-center justify-between border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900/80">
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 rounded-full bg-blue-100 dark:bg-blue-900/30">
                        <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <h2 className="font-semibold text-lg text-gray-800 dark:text-gray-100">
                        AI Content Detector
                      </h2>
                    </div>
                    <div className="flex items-center">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/40 dark:text-green-400">
                        Live Demo
                      </span>
                    </div>
                  </div>

                  {/* Actual tool */}
                  <div className="p-6">
                    <AiDetectionTool />
                  </div>

                  {/* Simplified stats bar */}
                  <div className="border-t border-gray-100 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900/80 px-6 py-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                          99.4%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Accuracy
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                          3M+
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Documents
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-xl font-bold text-blue-600 dark:text-blue-400">
                          2.5s
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Avg. Time
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Three feature badges in a cleaner, more elegant arrangement */}
                <div className="mt-8 flex justify-center gap-4">
                  {[
                    {
                      icon: Search,
                      text: "Multi-LLM Detection",
                      color: "blue",
                    },
                    { icon: Zap, text: "Results in Seconds", color: "green" },
                    { icon: Lock, text: "Secure Analysis", color: "indigo" },
                  ].map((feature, i) => (
                    <div
                      key={i}
                      className="bg-white/90 dark:bg-gray-800/90 px-4 py-2.5 rounded-full shadow-md border border-gray-100/50 dark:border-gray-700/50 flex items-center gap-2 transform transition duration-500"
                      style={{
                        animation: `feature-badge 0.6s forwards ease-out ${
                          0.2 + i * 0.2
                        }s`,
                        opacity: 0,
                        transform: "translateY(20px)",
                      }}
                    >
                      <div
                        className={`p-1.5 rounded-full ${
                          feature.color === "blue"
                            ? "bg-blue-100 dark:bg-blue-900/40"
                            : feature.color === "green"
                            ? "bg-green-100 dark:bg-green-900/40"
                            : feature.color === "indigo"
                            ? "bg-indigo-100 dark:bg-indigo-900/40"
                            : "bg-gray-100 dark:bg-gray-900/40"
                        }`}
                      >
                        <feature.icon
                          className={`h-4 w-4 ${
                            feature.color === "blue"
                              ? "text-blue-600 dark:text-blue-400"
                              : feature.color === "green"
                              ? "text-green-600 dark:text-green-400"
                              : feature.color === "indigo"
                              ? "text-indigo-600 dark:text-indigo-400"
                              : "text-gray-600 dark:text-gray-400"
                          }`}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
                        {feature.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* AI Detection Intelligence Dashboard - innovative replacement */}
          </div>
        </div>

        {/* Custom styles for animations */}
        <style jsx global>{`
          @keyframes float-vertical {
            0% {
              transform: translateY(0);
            }
            100% {
              transform: translateY(-20px);
            }
          }

          @keyframes scan {
            0% {
              transform: translateX(-100%);
            }
            100% {
              transform: translateX(100%);
            }
          }

          .animate-scan {
            animation: scan 8s linear infinite;
          }

          .animate-gradient-x {
            background-size: 200% 100%;
            animation: gradientBackground 8s ease infinite;
          }

          @keyframes gradientBackground {
            0% {
              background-position: 0% 50%;
            }
            50% {
              background-position: 100% 50%;
            }
            100% {
              background-position: 0% 50%;
            }
          }

          .animate-float {
            animation: float 6s ease-in-out infinite;
          }

          .animate-float-slow {
            animation: float 8s ease-in-out infinite;
          }

          @keyframes float {
            0% {
              transform: translateY(0);
            }
            50% {
              transform: translateY(-12px);
            }
            100% {
              transform: translateY(0);
            }
          }

          .animate-reveal {
            opacity: 0;
            animation: reveal 0.8s forwards;
          }

          @keyframes reveal {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .animate-pulse-slow {
            animation: pulse-slow 3s infinite;
          }

          @keyframes pulse-slow {
            0%,
            100% {
              opacity: 1;
            }
            50% {
              opacity: 0.8;
            }
          }

          .animate-fade-in {
            animation: fadeIn 1s forwards;
          }

          @keyframes fadeIn {
            from {
              opacity: 0;
            }
            to {
              opacity: 1;
            }
          }

          .animate-fade-in-delay-1 {
            opacity: 0;
            animation: fadeIn 1s forwards 0.2s;
          }

          .animate-fade-in-delay-2 {
            opacity: 0;
            animation: fadeIn 1s forwards 0.4s;
          }

          .animate-fade-in-delay-3 {
            opacity: 0;
            animation: fadeIn 1s forwards 0.6s;
          }

          .animate-fade-in-up {
            opacity: 0;
            animation: fadeInUp 0.8s forwards;
          }

          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}</style>
      </section>
      {/* Rest of the page sections */}
      <ProblemSection />
      <AudienceSection />
      <HowDetectionWorks />
      {/* <AdvancedDetectionFeatures />  */}
      {/* <TransformationSection /> */}
      <TestimonialSection />
      <HowItWorksSection />
      <PricingSection />
      <VisualProof />
      <FaqSection />
      <Footer />
      {/* Rest of your page */}
    </main>
  );
}
