"use client"

import type React from "react"

import { useEffect } from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { FileUploader } from "@/components/file-uploader"
import { AnalysisResults } from "@/components/analysis-results"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Loader2, AlertCircle, ThumbsUp, ThumbsDown, Volume, Video } from "lucide-react"
import { HumanizingTools } from "@/components/humanizing-tools"
import { UsageLimit } from "@/components/usage-limit"
import { useToast } from "@/hooks/use-toast"
import { PLAN_LIMITS } from "@/lib/usage-tracking"
import { <PERSON><PERSON>, AlertDescription, AlertTit<PERSON> } from "@/components/ui/alert"
import Link from "next/link"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

type AnalysisResult = {
  score: number
  highlightedText: string
  suspiciousSections: {
    text: string
    score: number
    startIndex: number
    endIndex: number
  }[]
}

const feedbackReasons = ["False positive", "False negative", "Hallucination", "Bias", "Other"]

export function TextAnalyzer() {
  const [activeTab, setActiveTab] = useState("text-input")
  const [text, setText] = useState("")
  const [sensitivity, setSensitivity] = useState([0.5])
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [result, setResult] = useState<AnalysisResult | null>(null)
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isOverWordLimit, setIsOverWordLimit] = useState(false)
  const { toast } = useToast()
  const [showFeedbackForm, setShowFeedbackForm] = useState(false)
  const [selectedFeedbackReason, setSelectedFeedbackReason] = useState<string | null>(null)

  useEffect(() => {
    const storedUser = localStorage.getItem("user")
    if (storedUser) {
      const userData = JSON.parse(storedUser)
      setUser(userData)
    }
    setIsLoading(false)
  }, [])

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value
    setText(newText)

    const wordCount = newText.trim().split(/\s+/).length
    setIsOverWordLimit(wordCount > PLAN_LIMITS[user?.plan || "free"].wordLimit)
    setResult(null)
  }

  const handleSensitivityChange = (value: number[]) => {
    setSensitivity(value)
  }

  const handleAnalyze = async () => {
    if (!text.trim()) return

    const wordCount = text.trim().split(/\s+/).length
    if (user && wordCount > PLAN_LIMITS[user?.plan || "free"].wordLimit) {
      toast({
        title: "Word limit exceeded",
        description: `Your current plan allows up to ${PLAN_LIMITS[user?.plan || "free"].wordLimit} words per analysis. This text has ${wordCount} words.`,
        variant: "destructive",
      })
      return
    }

    if (user && user.credits <= 0) {
      toast({
        title: "Insufficient credits",
        description: "You don't have enough credits to perform this action. Please purchase more credits.",
        variant: "destructive",
      })
      return
    }

    setIsAnalyzing(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 2000))

      const mockResult: AnalysisResult = {
        score: Math.random() * 0.7 + 0.2,
        highlightedText: text,
        suspiciousSections: [
          {
            text: text.substring(
              Math.floor(Math.random() * (text.length / 2)),
              Math.floor(Math.random() * (text.length / 2)) + text.length / 3,
            ),
            score: Math.random() * 0.5 + 0.5,
            startIndex: Math.floor(Math.random() * (text.length / 2)),
            endIndex: Math.floor(Math.random() * (text.length / 2)) + text.length / 3,
          },
        ],
      }

      setResult(mockResult)

      const updatedCredits = user.credits - 1
      const updatedUser = { ...user, credits: updatedCredits }
      setUser(updatedUser)
      localStorage.setItem("user", JSON.stringify(updatedUser))
    } catch (error) {
      console.error("Error analyzing text:", error)
      toast({
        title: "Analysis failed",
        description: "There was an error analyzing your text. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleFileContent = (content: string) => {
    setText(content)

    const wordCount = content.trim().split(/\s+/).length
    setIsOverWordLimit(wordCount > PLAN_LIMITS[user?.plan || "free"].wordLimit)
  }

  const handleThumbsDown = () => {
    setShowFeedbackForm(true)
  }

  const handleFeedbackSubmit = () => {
    // Handle feedback submission logic here
    console.log("Feedback submitted:", selectedFeedbackReason)
    toast({
      title: "Feedback submitted",
      description: "Thank you for your feedback!",
    })
    setShowFeedbackForm(false)
    setSelectedFeedbackReason(null)
  }

  if (isLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader className="px-4 sm:px-6">
          <CardTitle>Analyze Text</CardTitle>
          <CardDescription>Loading...</CardDescription>
        </CardHeader>
        <CardContent className="px-4 sm:px-6">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  return (
    <section className="container py-8 md:py-12">
      <Card className="w-full mx-auto">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg font-medium">Quick AI Detection</CardTitle>
          <CardDescription>Paste text to check if it was written by AI</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="text-input">Text Input</TabsTrigger>
              <TabsTrigger value="file-upload">File Upload</TabsTrigger>
            </TabsList>
            <TabsContent value="text-input" className="space-y-4 mt-4">
              <Textarea
                placeholder="Paste or type the text you want to analyze..."
                className="min-h-[200px]"
                value={text}
                onChange={handleTextChange}
                disabled={isAnalyzing}
              />
              {isOverWordLimit && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Word limit exceeded</AlertTitle>
                  <AlertDescription>
                    Your current plan allows up to {PLAN_LIMITS[user?.plan || "free"].wordLimit} words per analysis.
                    This text has {text.trim().split(/\s+/).length} words.
                    <Link href="/pricing" className="block mt-2 underline">
                      Upgrade your plan for higher word limits
                    </Link>
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>
            <TabsContent value="file-upload" className="space-y-4 mt-4">
              <FileUploader
                onFileContent={handleFileContent}
                allowedFileTypes={user ? PLAN_LIMITS[user?.plan || "free"].fileFormats : [".txt"]}
                maxSizeInBytes={10 * 1024 * 1024} // 10MB max
              />
              {isOverWordLimit && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Word limit exceeded</AlertTitle>
                  <AlertDescription>
                    Your current plan allows up to {PLAN_LIMITS[user?.plan || "free"].wordLimit} words per analysis.
                    This text has {text.trim().split(/\s+/).length} words.
                    <Link href="/pricing" className="block mt-2 underline">
                      Upgrade your plan for higher word limits
                    </Link>
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>
          </Tabs>

          <div className="mt-6 space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="sensitivity">Detection Sensitivity</Label>
                <span className="text-sm text-muted-foreground">{Math.round(sensitivity[0] * 100)}%</span>
              </div>
              <Slider
                id="sensitivity"
                min={0}
                max={1}
                step={0.01}
                value={sensitivity}
                onValueChange={handleSensitivityChange}
              />
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <Button
                onClick={handleAnalyze}
                disabled={!text.trim() || isAnalyzing || isOverWordLimit || !user || user.credits <= 0}
                className="w-full sm:flex-1"
              >
                {isAnalyzing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <span>Analyze Text</span>
                    <span className="text-sm text-muted-foreground ml-2">Cost: 1 Credit</span>
                  </>
                )}
              </Button>

              {user && <UsageLimit current={user.credits} limit={100} plan={user.plan} />}
            </div>
          </div>

          {result && (
            <div className="mt-8">
              <AnalysisResults result={result} />
              <div className="flex justify-end mt-4">
                <Button variant="ghost" onClick={handleThumbsDown}>
                  <ThumbsDown className="h-4 w-4 mr-2" />
                  Not Accurate
                </Button>
                <Button variant="ghost">
                  <ThumbsUp className="h-4 w-4 mr-2" />
                  Accurate
                </Button>
              </div>
            </div>
          )}

          {result && (
            <div className="mt-8">
              <HumanizingTools text={text} result={result} userPlan={user.plan} />
            </div>
          )}

          <div className="mt-8 flex justify-between">
            <div>
              <Button variant="secondary" disabled>
                <Volume className="h-4 w-4 mr-2" />
                Audio Test (Coming Soon)
              </Button>
            </div>
            <div>
              <Button variant="secondary" disabled>
                <Video className="h-4 w-4 mr-2" />
                Video Test (Coming Soon) - Google SynthID
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={showFeedbackForm} onOpenChange={setShowFeedbackForm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Why wasn't this accurate?</AlertDialogTitle>
            <AlertDialogDescription>
              Please tell us why the analysis wasn't accurate so we can improve our tool.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="grid gap-2">
            {feedbackReasons.map((reason) => (
              <Button
                key={reason}
                variant="outline"
                className={`justify-start ${selectedFeedbackReason === reason ? "bg-secondary" : ""}`}
                onClick={() => setSelectedFeedbackReason(reason)}
              >
                {reason}
              </Button>
            ))}
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSelectedFeedbackReason(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleFeedbackSubmit} disabled={!selectedFeedbackReason}>
              Submit Feedback
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </section>
  )
}
