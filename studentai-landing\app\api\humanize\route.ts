import { NextResponse } from "next/server";
import { headers } from "next/headers";

// Simple in-memory store for rate limiting (would use Redis/DB in production)
const ipRequests = new Map<string, { count: number; timestamp: number }>();

// Rate limit config
const RATE_LIMIT = 2; // requests per day for free users
const RATE_WINDOW = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export async function POST(request: Request) {
  try {
    // Get client IP
    const headersList = headers();
    const forwardedFor = headersList.get("x-forwarded-for") || "";
    const ip = forwardedFor.split(",")[0] || "unknown-ip";

    // Check rate limit
    const now = Date.now();
    const ipData = ipRequests.get(ip) || { count: 0, timestamp: now };

    // Clear count if 24 hours have passed
    if (now - ipData.timestamp > RATE_WINDOW) {
      ipData.count = 0;
      ipData.timestamp = now;
    }

    // Check if rate limit is reached
    if (ipData.count >= RATE_LIMIT) {
      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          message: "You've reached the limit of 2 free humanizations per day",
          remainingTime: RATE_WINDOW - (now - ipData.timestamp),
          requiresLogin: true,
        },
        { status: 429 }
      );
    }

    // Extract data from request
    const {
      text,
      style = "professional",
      complexity = "moderate",
    } = await request.json();

    if (!text || text.length < 50) {
      return NextResponse.json(
        { error: "Text must be at least 50 characters" },
        { status: 400 }
      );
    }

    if (text.length > 5000) {
      return NextResponse.json(
        { error: "Text cannot exceed 5000 characters" },
        { status: 400 }
      );
    }

    // Call the external humanizer API
    try {
      const apiResponse = await fetch(
        "https://student-ai-detector.uc.r.appspot.com/api/v1/humanize",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ text, style, complexity }),
        }
      );

      if (!apiResponse.ok) {
        throw new Error(`External API error: ${apiResponse.status}`);
      }

      const apiData = await apiResponse.json();

      // Increment request count for this IP
      ipData.count++;
      ipRequests.set(ip, ipData);

      // Return the result with additional metadata
      const result = {
        ...apiData,
        remainingCredits: RATE_LIMIT - ipData.count,
        isPremiumResult: false, // Flag indicating this is a free tier result
      };

      return NextResponse.json({ result });
    } catch (error) {
      console.error("Error from external API:", error);
      throw new Error("Failed to humanize with external service");
    }
  } catch (error) {
    console.error("Error processing humanization:", error);
    return NextResponse.json(
      { error: "Failed to process text" },
      { status: 500 }
    );
  }
}

// GET endpoint to fetch available options
export async function GET() {
  return NextResponse.json({
    styles: [
      {
        id: "academic",
        name: "Academic",
        description: "Formal scholarly writing style",
      },
      {
        id: "professional",
        name: "Professional",
        description: "Business and workplace communication",
      },
      {
        id: "casual",
        name: "Casual",
        description: "Conversational and relaxed tone",
      },
    ],
    complexities: [
      {
        id: "simple",
        name: "Simple",
        description: "Clear and straightforward language",
      },
      {
        id: "moderate",
        name: "Moderate",
        description: "Balanced complexity for general audiences",
      },
      {
        id: "advanced",
        name: "Advanced",
        description: "Sophisticated vocabulary and structures",
      },
    ],
  });
}
