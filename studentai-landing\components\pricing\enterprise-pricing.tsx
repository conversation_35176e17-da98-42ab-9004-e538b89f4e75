import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Check, Users, Building, BarChart, Shield } from "lucide-react"
import Link from "next/link"

export function EnterprisePricing() {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Enterprise Solutions</h2>
        <p className="text-muted-foreground mt-2">Tailored solutions for educational institutions and organizations</p>
      </div>

      <Card className="overflow-hidden">
        <div className="md:grid md:grid-cols-3">
          <CardHeader className="md:col-span-1 bg-muted/30">
            <CardTitle className="text-2xl">Enterprise</CardTitle>
            <CardDescription className="mt-2">Custom solutions for institutions with advanced needs</CardDescription>
            <div className="mt-6">
              <p className="text-2xl font-bold">Custom Pricing</p>
              <p className="text-sm text-muted-foreground mt-1">Based on your specific requirements</p>
            </div>
            <div className="mt-6">
              <Link href="/contact?subject=Enterprise">
                <Button className="w-full">Contact Sales</Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent className="md:col-span-2 p-6">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-primary/10">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-medium">Unlimited Users</h3>
                </div>
                <ul className="space-y-2 pl-9">
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Role-based access control</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">User activity monitoring</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">SSO integration (SAML, OAuth)</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-primary/10">
                    <Building className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-medium">Dedicated Support</h3>
                </div>
                <ul className="space-y-2 pl-9">
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Dedicated account manager</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Priority 24/7 technical support</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Onboarding and training sessions</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-primary/10">
                    <BarChart className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-medium">Advanced Analytics</h3>
                </div>
                <ul className="space-y-2 pl-9">
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Custom reporting dashboards</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Trend analysis and insights</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Data export capabilities</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <div className="p-2 rounded-full bg-primary/10">
                    <Shield className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-medium">Enterprise Security</h3>
                </div>
                <ul className="space-y-2 pl-9">
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Custom SLA with 99.9% uptime</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">FERPA/GDPR compliance</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">Data residency options</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <h3 className="font-medium mb-4">Volume-Based Discounts</h3>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="p-4 border rounded-lg">
                  <p className="font-medium">Educational</p>
                  <p className="text-sm text-muted-foreground mt-1">For schools and universities</p>
                  <p className="text-sm mt-2">Up to 30% discount based on student population</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="font-medium">Multi-Year</p>
                  <p className="text-sm text-muted-foreground mt-1">For long-term commitments</p>
                  <p className="text-sm mt-2">Up to 25% discount for 3-year contracts</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="font-medium">High Volume</p>
                  <p className="text-sm text-muted-foreground mt-1">For intensive usage</p>
                  <p className="text-sm mt-2">Custom pricing for 10,000+ analyses/month</p>
                </div>
              </div>
            </div>
          </CardContent>
        </div>
      </Card>
    </div>
  )
}
