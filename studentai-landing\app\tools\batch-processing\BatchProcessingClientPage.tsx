"use client"

import { ToolPageLayout } from "@/components/layouts/tool-page-layout"
import { useState, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Upload, type File, X, FileText, FileIcon as FilePdf, FileIcon as FileWord, Loader2 } from "lucide-react"
import { useDropzone } from "react-dropzone"
import { Progress } from "@/components/ui/progress"

const SUPPORTED_FILE_TYPES = [
  { type: "text/plain", extension: ".txt", icon: <FileText className="h-4 w-4" /> },
  { type: "application/pdf", extension: ".pdf", icon: <FilePdf className="h-4 w-4" /> },
  {
    type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    extension: ".docx",
    icon: <FileWord className="h-4 w-4" />,
  },
]

export default function BatchProcessingClientPage() {
  const [files, setFiles] = useState<File[]>([])
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setFiles((prevFiles) => [...prevFiles, ...acceptedFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: SUPPORTED_FILE_TYPES.map((fileType) => fileType.type).join(", "),
    maxFiles: 10, // Example limit
  })

  const handleRemoveFile = (fileToRemove: File) => {
    setFiles((prevFiles) => prevFiles.filter((file) => file !== fileToRemove))
  }

  const handleAnalyze = async () => {
    setIsProcessing(true)
    setUploadProgress(0)

    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 95) {
          clearInterval(interval)
          return 95
        }
        return prev + 5
      })
    }, 100)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 3000))
    } finally {
      clearInterval(interval)
      setIsProcessing(false)
    }
  }

  const totalFileSize = files.reduce((acc, file) => acc + file.size, 0)
  const fileSizeLimit = 50 * 1024 * 1024 // 50MB

  const isOverLimit = totalFileSize > fileSizeLimit

  return (
    <ToolPageLayout
      title="Batch Processing Tool"
      description="Analyze multiple documents simultaneously to save time and increase efficiency."
    >
      <div className="space-y-6">
        <div {...getRootProps()} className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer">
          <input {...getInputProps()} />
          {isDragActive ? (
            <p className="text-muted-foreground">Drop the files here ...</p>
          ) : (
            <>
              <Upload className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Drag 'n' drop some files here, or click to select files</p>
              <p className="text-xs text-muted-foreground mt-2">
                Supports {SUPPORTED_FILE_TYPES.map((fileType) => fileType.extension).join(", ")}
              </p>
            </>
          )}
        </div>

        {files.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Uploaded Files</h3>
            <div className="max-h-[200px] overflow-y-auto space-y-2">
              {files.map((file) => (
                <Card key={file.name} className="flex items-center justify-between p-3">
                  <div className="flex items-center gap-2">
                    {SUPPORTED_FILE_TYPES.find((fileType) => fileType.type === file.type)?.icon || (
                      <FileText className="h-4 w-4" />
                    )}
                    <span className="truncate">{file.name}</span>
                  </div>
                  <Button variant="ghost" size="icon" onClick={() => handleRemoveFile(file)}>
                    <X className="h-4 w-4" />
                  </Button>
                </Card>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">
              File Upload Limit: 50MB | Current Volume: {(totalFileSize / (1024 * 1024)).toFixed(2)}MB
            </p>
            {isOverLimit && (
              <p className="text-sm text-red-500">File size exceeds the limit. Please remove some files.</p>
            )}
          </div>
          <Button onClick={handleAnalyze} disabled={files.length === 0 || isOverLimit || isProcessing}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              "Analyze Files"
            )}
          </Button>
        </div>

        {isProcessing && (
          <div className="space-y-2">
            <Progress value={uploadProgress} />
            <p className="text-sm text-muted-foreground text-right">{uploadProgress}%</p>
          </div>
        )}
      </div>
    </ToolPageLayout>
  )
}
