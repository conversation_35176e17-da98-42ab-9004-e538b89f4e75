"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Upload,
  Search,
  Sparkles,
  FileText,
  FileCheck,
  AlertCircle,
  CheckCircle,
  BarChart2,
  FileBarChart,
  ChevronRight,
  ArrowRight,
  CircleOff,
  Zap,
  RotateCw,
  Computer,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

export default function HowItWorksSection() {
  const [activeTab, setActiveTab] = useState("upload");
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Animation progress states
  const [uploadProgress, setUploadProgress] = useState(0);
  const [analyzeProgress, setAnalyzeProgress] = useState(0);
  const [transformProgress, setTransformProgress] = useState(0);

  // Demo text for interactive elements
  const [demoText, setDemoText] = useState(
    "This essay discusses the impact of artificial intelligence on modern society, exploring both the benefits and challenges of this transformative technology."
  );
  const [analyzedText, setAnalyzedText] = useState("");
  const [transformedText, setTransformedText] = useState("");

  // Handle demo animations based on active tab
  useEffect(() => {
    if (activeTab === "upload") {
      setUploadProgress(0);
      const timer = setTimeout(() => {
        const interval = setInterval(() => {
          setUploadProgress((prev) => {
            if (prev >= 100) {
              clearInterval(interval);
              return 100;
            }
            return prev + 4;
          });
        }, 50);
        return () => clearInterval(interval);
      }, 500);
      return () => clearTimeout(timer);
    }

    if (activeTab === "analyze") {
      setAnalyzeProgress(0);
      setAnalyzedText("");
      const timer = setTimeout(() => {
        const interval = setInterval(() => {
          setAnalyzeProgress((prev) => {
            if (prev >= 100) {
              clearInterval(interval);
              setAnalyzedText(demoText);
              return 100;
            }
            return prev + 2;
          });
        }, 30);
        return () => clearInterval(interval);
      }, 500);
      return () => clearTimeout(timer);
    }

    if (activeTab === "transform") {
      setTransformProgress(0);
      setTransformedText("");
      const timer = setTimeout(() => {
        const interval = setInterval(() => {
          setTransformProgress((prev) => {
            if (prev >= 100) {
              clearInterval(interval);
              setTransformedText(
                "In my original analysis, I explore how artificial intelligence is reshaping our world today. I personally consider both the positive opportunities and difficult challenges that this revolutionary technology brings to our society."
              );
              return 100;
            }
            return prev + 1;
          });
        }, 20);
        return () => clearInterval(interval);
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [activeTab, demoText]);

  // Intersection Observer to trigger animations when section is visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = sectionRef.current;
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  const steps = [
    {
      id: "upload",
      title: "Upload",
      icon: <Upload className="h-6 w-6" />,
      description:
        "Simply paste text or upload documents in various formats including Word, PDF, or plain text.",
      details: [
        "Supports multiple file formats",
        "Batch upload for multiple documents",
        "Secure and private processing",
      ],
      color: "blue",
    },
    {
      id: "analyze",
      title: "Analyze",
      icon: <Search className="h-6 w-6" />,
      description:
        "Our advanced AI algorithms scan the content and identify patterns indicative of AI-generated text.",
      details: [
        "Processes content in seconds",
        "Detects content from all major AI tools",
        "Provides confidence scores",
      ],
      color: "purple",
    },
    {
      id: "transform",
      title: "Transform",
      icon: <Sparkles className="h-6 w-6" />,
      description:
        "For educational purposes, transform AI-generated content into authentic human writing with our humanization tools.",
      details: [
        "Suggests human-like alternatives",
        "Preserves original meaning",
        "Educational feedback for better writing",
      ],
      color: "amber",
    },
    {
      id: "report",
      title: "Report",
      icon: <FileText className="h-6 w-6" />,
      description:
        "Get comprehensive reports with highlighted sections, confidence scores, and specific patterns identified.",
      details: [
        "Detailed visual reports",
        "Exportable PDF format",
        "Historical tracking of submissions",
      ],
      color: "green",
    },
  ];

  // Custom visualizations for each step instead of images
  const renderVisualization = (step: string) => {
    switch (step) {
      case "upload":
        return (
          <div className="relative h-[300px] bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg border border-blue-200 dark:border-blue-700/30 shadow-lg overflow-hidden">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4/5 max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 transform transition duration-500">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex space-x-1.5">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    AI Content Detector
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded p-3 mb-4">
                  <div className="text-sm text-gray-700 dark:text-gray-300 mb-1">
                    Upload Text or Document
                  </div>
                  <textarea
                    className="w-full h-20 p-2 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded"
                    value={demoText}
                    onChange={(e) => setDemoText(e.target.value)}
                    placeholder="Paste your text here..."
                  />
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {uploadProgress === 100
                      ? "Ready for analysis"
                      : "Preparing..."}
                  </div>
                  <Button
                    size="sm"
                    className={`bg-blue-500 hover:bg-blue-600 text-white ${
                      uploadProgress === 100 ? "animate-pulse" : ""
                    }`}
                    disabled={uploadProgress < 100}
                  >
                    {uploadProgress < 100 ? (
                      <>
                        <RotateCw className="mr-1 h-3 w-3 animate-spin" />
                        <span>{uploadProgress}%</span>
                      </>
                    ) : (
                      <>
                        Analyze
                        <ArrowRight className="ml-1 h-3 w-3" />
                      </>
                    )}
                  </Button>
                </div>

                {uploadProgress > 0 && (
                  <Progress value={uploadProgress} className="mt-3 h-1" />
                )}
              </div>
            </div>

            {/* Floating document icons */}
            <div className="absolute top-6 left-6 opacity-30 dark:opacity-20">
              <FileText className="h-16 w-16 text-blue-500/80 dark:text-blue-400/80" />
            </div>
            <div className="absolute bottom-10 right-8 opacity-30 dark:opacity-20">
              <FileCheck className="h-12 w-12 text-blue-500/80 dark:text-blue-400/80" />
            </div>

            {/* Animated particles */}
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className="absolute rounded-md bg-blue-500/20 dark:bg-blue-400/20"
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  width: `${Math.random() * 20 + 10}px`,
                  height: `${Math.random() * 20 + 10}px`,
                  animation: `float-vertical ${
                    Math.random() * 10 + 15
                  }s infinite alternate ease-in-out`,
                  animationDelay: `${Math.random() * 5}s`,
                }}
              ></div>
            ))}
          </div>
        );

      case "analyze":
        return (
          <div className="relative h-[300px] bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg border border-purple-200 dark:border-purple-700/30 shadow-lg overflow-hidden">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4/5 max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-lg p-5 transform transition duration-500">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Search className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                    <div className="text-sm font-medium">AI Analysis</div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {analyzeProgress}% Complete
                  </div>
                </div>

                <div className="space-y-3">
                  <Progress value={analyzeProgress} className="h-2" />

                  <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded p-3">
                    {analyzedText ? (
                      <div className="text-sm">
                        <span className="bg-red-100 dark:bg-red-900/30 px-1 rounded">
                          This essay discusses the impact of artificial
                          intelligence
                        </span>{" "}
                        <span className="bg-amber-100 dark:bg-amber-900/30 px-1 rounded">
                          on modern society
                        </span>
                        , exploring both the benefits and challenges of{" "}
                        <span className="bg-red-100 dark:bg-red-900/30 px-1 rounded">
                          this transformative technology.
                        </span>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center justify-center py-2">
                        <RotateCw className="mr-2 h-4 w-4 animate-spin" />
                        Analyzing text patterns...
                      </div>
                    )}
                  </div>

                  {analyzedText && (
                    <div className="flex justify-between items-center text-sm">
                      <div className="flex items-center gap-2">
                        <div className="bg-red-100 dark:bg-red-900/30 w-3 h-3 rounded"></div>
                        <span className="text-xs">High AI probability</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-amber-100 dark:bg-amber-900/30 w-3 h-3 rounded"></div>
                        <span className="text-xs">Medium AI probability</span>
                      </div>
                    </div>
                  )}
                </div>

                {analyzedText && (
                  <div className="mt-4 flex justify-between items-center">
                    <div className="bg-red-500 text-white text-xs px-2 py-1 rounded">
                      78% AI Probability
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-xs border-purple-200 dark:border-purple-800"
                    >
                      View Full Report
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Animated scanning beam */}
            {analyzeProgress > 0 && analyzeProgress < 100 && (
              <div
                className="absolute left-0 w-full h-1 bg-purple-500/50 dark:bg-purple-400/50 blur-[2px]"
                style={{
                  top: `${(analyzeProgress / 100) * 80 + 10}%`,
                  animation: "pulse 1s infinite",
                }}
              ></div>
            )}

            {/* Decorative elements */}
            <div className="absolute top-8 right-8 opacity-30 dark:opacity-20">
              <BarChart2 className="h-12 w-12 text-purple-500 dark:text-purple-400" />
            </div>
          </div>
        );

      case "transform":
        return (
          <div className="relative h-[300px] bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 rounded-lg border border-amber-200 dark:border-amber-700/30 shadow-lg overflow-hidden">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4/5 max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-lg p-5 transform transition duration-500">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-amber-500 dark:text-amber-400" />
                    <div className="text-sm font-medium">AI Humanization</div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {transformProgress === 100
                      ? "Transformation Complete"
                      : `${transformProgress}%`}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 rounded p-3">
                    <div className="flex items-center mb-1">
                      <CircleOff className="h-3 w-3 text-red-500 mr-1" />
                      <span className="text-xs font-medium text-red-700 dark:text-red-400">
                        AI Generated
                      </span>
                    </div>
                    <p className="text-xs text-gray-700 dark:text-gray-300">
                      This essay discusses the impact of artificial intelligence
                      on modern society, exploring both the benefits and
                      challenges of this transformative technology.
                    </p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 rounded p-3">
                    <div className="flex items-center mb-1">
                      <CheckCircle className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-xs font-medium text-green-700 dark:text-green-400">
                        Human-Like
                      </span>
                    </div>
                    {transformedText ? (
                      <p className="text-xs text-gray-700 dark:text-gray-300">
                        {transformedText}
                      </p>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-3">
                        <div className="relative h-4 w-full overflow-hidden bg-gray-200 dark:bg-gray-700 rounded-full">
                          <div
                            className="absolute top-0 h-full bg-gradient-to-r from-amber-500 to-green-500 rounded-full transition-all duration-500"
                            style={{ width: `${transformProgress}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                          Transforming content...
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {transformedText && (
                  <div className="mt-4 grid grid-cols-4 gap-2 text-xs">
                    {[
                      "Paraphrased: 23%",
                      "Structure: 18%",
                      "Vocabulary: 35%",
                      "Style: 24%",
                    ].map((stat, i) => (
                      <div
                        key={i}
                        className="bg-amber-100/50 dark:bg-amber-900/20 p-1 rounded text-center"
                      >
                        {stat}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Animated transformation particles */}
            {transformProgress > 50 &&
              [...Array(10)].map((_, i) => (
                <div
                  key={i}
                  className="absolute rounded-full bg-gradient-to-r from-amber-500 to-green-500"
                  style={{
                    top: `${30 + Math.random() * 40}%`,
                    left: `${Math.random() * 100}%`,
                    width: `${Math.random() * 6 + 2}px`,
                    height: `${Math.random() * 6 + 2}px`,
                    opacity: 0.4,
                    animation: `float-vertical ${
                      Math.random() * 5 + 2
                    }s infinite alternate ease-in-out`,
                    animationDelay: `${Math.random() * 2}s`,
                  }}
                ></div>
              ))}

            {/* Decorative elements */}
            <div className="absolute bottom-8 left-8 opacity-30 dark:opacity-20">
              <Zap className="h-12 w-12 text-amber-500 dark:text-amber-400" />
            </div>
          </div>
        );

      case "report":
        return (
          <div className="relative h-[300px] bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg border border-green-200 dark:border-green-700/30 shadow-lg overflow-hidden">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-4/5 max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-lg p-5 transform transition duration-500">
                <div className="flex items-center justify-between mb-3 border-b border-gray-100 dark:border-gray-700 pb-2">
                  <div className="flex items-center gap-2">
                    <FileBarChart className="h-4 w-4 text-green-500 dark:text-green-400" />
                    <div className="text-sm font-medium">Analysis Report</div>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    April 30, 2025
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-3 mb-4">
                  <div className="bg-gray-50 dark:bg-gray-900/50 p-2 rounded text-center">
                    <div className="text-xl font-bold text-green-600 dark:text-green-400">
                      78%
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      AI Probability
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-900/50 p-2 rounded text-center">
                    <div className="text-xl font-bold text-green-600 dark:text-green-400">
                      3
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      AI Sections
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-900/50 p-2 rounded text-center">
                    <div className="text-xl font-bold text-green-600 dark:text-green-400">
                      GPT-4
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Model
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="bg-gray-50 dark:bg-gray-900/50 p-2 rounded text-xs text-gray-700 dark:text-gray-300 flex items-start gap-2">
                    <AlertCircle className="h-3 w-3 text-red-500 mt-0.5 shrink-0" />
                    <span>
                      High probability of AI-generated content in introduction
                      and conclusion.
                    </span>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-900/50 p-2 rounded text-xs text-gray-700 dark:text-gray-300 flex items-start gap-2">
                    <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 shrink-0" />
                    <span>
                      Middle section shows more human-like characteristics and
                      originality.
                    </span>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-900/50 p-2 rounded flex items-center justify-between">
                    <span className="text-xs font-medium">
                      View detailed report
                    </span>
                    <ChevronRight className="h-4 w-4 text-gray-400" />
                  </div>
                </div>

                <div className="mt-3 flex justify-end gap-2">
                  <Button size="sm" variant="outline" className="text-xs">
                    <Computer className="mr-1 h-3 w-3" />
                    Compare
                  </Button>
                  <Button
                    size="sm"
                    className="text-xs bg-green-500 hover:bg-green-600 text-white"
                  >
                    <FileText className="mr-1 h-3 w-3" />
                    Download PDF
                  </Button>
                </div>
              </div>
            </div>

            {/* Graph bars in the background */}
            <div className="absolute bottom-5 right-5 flex items-end h-24 gap-2 opacity-30 dark:opacity-20">
              {[60, 85, 40, 70, 90].map((height, i) => (
                <div
                  key={i}
                  className="w-4 bg-green-500/70 dark:bg-green-400/70 rounded-t"
                  style={{ height: `${height}%` }}
                ></div>
              ))}
            </div>

            {/* Decorative elements */}
            <div className="absolute top-8 left-8 opacity-30 dark:opacity-20">
              <FileCheck className="h-12 w-12 text-green-500 dark:text-green-400" />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <section
      ref={sectionRef}
      className="w-full py-16 md:py-28 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 overflow-hidden relative"
    >
      {/* Background decoration */}
      <div className="absolute left-0 top-1/4 w-64 h-64 bg-blue-100/20 dark:bg-blue-900/10 rounded-full blur-3xl -z-10" />
      <div className="absolute right-1/4 bottom-1/4 w-72 h-72 bg-purple-100/30 dark:bg-purple-900/10 rounded-full blur-3xl -z-10" />
      <div className="absolute right-0 top-20 w-48 h-48 bg-amber-100/20 dark:bg-amber-900/10 rounded-full blur-3xl -z-10" />

      <div className="container mx-auto px-4 md:px-6 relative">
        <div className="text-center mb-16">
          <div
            className={`inline-flex items-center gap-2 mb-2 px-4 py-1.5 bg-blue-100/90 dark:bg-blue-900/40 backdrop-blur-sm rounded-full border border-blue-200/40 dark:border-blue-700/40 shadow-sm ${
              isVisible ? "animate-fade-in" : "opacity-0"
            }`}
          >
            <Zap className="h-4 w-4 text-blue-500 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Simple Process
            </span>
          </div>

          <h2
            className={`text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white max-w-3xl mx-auto ${
              isVisible ? "animate-reveal" : "opacity-0"
            }`}
            style={{ animationDelay: "0.2s" }}
          >
            How It Works
          </h2>

          <p
            className={`max-w-[800px] mx-auto mt-4 text-gray-600 dark:text-gray-300 md:text-xl/relaxed ${
              isVisible ? "animate-reveal" : "opacity-0"
            }`}
            style={{ animationDelay: "0.4s" }}
          >
            Our user-friendly interface makes it easy to detect AI-generated
            content in just a few simple steps.
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          {/* Connected Step Indicators */}
          <div
            className={`relative flex justify-between mb-12 px-4 ${
              isVisible ? "animate-fade-in" : "opacity-0"
            }`}
          >
            {steps.map((step, i) => (
              <div key={i} className="flex flex-col items-center relative z-10">
                <button
                  onClick={() => setActiveTab(step.id)}
                  className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                    activeTab === step.id
                      ? `bg-${step.color}-500 text-white shadow-lg shadow-${step.color}-500/20`
                      : "bg-white dark:bg-gray-800 text-gray-400 border border-gray-200 dark:border-gray-700"
                  }`}
                >
                  {step.icon}
                </button>
                <div
                  className={`mt-2 text-sm font-medium ${
                    activeTab === step.id
                      ? `text-${step.color}-600 dark:text-${step.color}-400`
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {step.title}
                </div>

                {/* Step number indicator */}
                <div
                  className={`absolute -top-2 -right-2 w-5 h-5 rounded-full text-xs flex items-center justify-center ${
                    activeTab === step.id
                      ? `bg-${step.color}-200 text-${step.color}-700 dark:bg-${step.color}-900/50 dark:text-${step.color}-300`
                      : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                  }`}
                >
                  {i + 1}
                </div>
              </div>
            ))}

            {/* Connector lines */}
            <div className="absolute top-6 left-0 w-full h-px bg-gray-200 dark:bg-gray-700 -z-10"></div>

            {/* Animated progress indicator */}
            <div
              className={`absolute top-6 left-0 h-px bg-gradient-to-r from-blue-500 via-purple-500 to-green-500 transition-all duration-500 -z-5`}
              style={{
                width: `${
                  activeTab === "upload"
                    ? "12%"
                    : activeTab === "analyze"
                    ? "37%"
                    : activeTab === "transform"
                    ? "62%"
                    : "87%"
                }`,
              }}
            ></div>
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="grid md:grid-cols-2 gap-8 items-center"
            >
              <div className="p-6 bg-white dark:bg-gray-800/90 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700/50">
                <div
                  className={`inline-flex p-2 rounded-lg bg-${
                    steps.find((s) => s.id === activeTab)?.color
                  }-100/90 dark:bg-${
                    steps.find((s) => s.id === activeTab)?.color
                  }-900/30 mb-4`}
                >
                  {steps.find((s) => s.id === activeTab)?.icon}
                </div>
                <h3 className="text-xl font-bold mb-3">
                  {steps.find((s) => s.id === activeTab)?.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {steps.find((s) => s.id === activeTab)?.description}
                </p>
                <div className="space-y-3">
                  {steps
                    .find((s) => s.id === activeTab)
                    ?.details.map((detail, i) => (
                      <div key={i} className="flex items-start">
                        <div
                          className={`shrink-0 p-1 rounded-full bg-${
                            steps.find((s) => s.id === activeTab)?.color
                          }-100 dark:bg-${
                            steps.find((s) => s.id === activeTab)?.color
                          }-900/30 mr-2`}
                        >
                          <CheckCircle
                            className={`h-3 w-3 text-${
                              steps.find((s) => s.id === activeTab)?.color
                            }-600 dark:text-${
                              steps.find((s) => s.id === activeTab)?.color
                            }-400`}
                          />
                        </div>
                        <span className="text-gray-700 dark:text-gray-300">
                          {detail}
                        </span>
                      </div>
                    ))}
                </div>

                <div className="mt-8 flex">
                  {activeTab !== "upload" && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        const currentIndex = steps.findIndex(
                          (s) => s.id === activeTab
                        );
                        if (currentIndex > 0) {
                          setActiveTab(steps[currentIndex - 1].id);
                        }
                      }}
                      className="mr-2"
                    >
                      Previous Step
                    </Button>
                  )}

                  {activeTab !== "report" && (
                    <Button
                      onClick={() => {
                        const currentIndex = steps.findIndex(
                          (s) => s.id === activeTab
                        );
                        if (currentIndex < steps.length - 1) {
                          setActiveTab(steps[currentIndex + 1].id);
                        }
                      }}
                      className={`bg-${
                        steps.find((s) => s.id === activeTab)?.color
                      }-500 hover:bg-${
                        steps.find((s) => s.id === activeTab)?.color
                      }-600 text-white`}
                    >
                      Next Step
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Interactive visualization */}
              <div className="order-first md:order-last">
                {renderVisualization(activeTab)}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Animation styles */}
      <style jsx global>{`
        @keyframes pulse {
          0%,
          100% {
            opacity: 0.6;
          }
          50% {
            opacity: 1;
          }
        }

        .animate-pulse-slow {
          animation: pulse 3s infinite;
        }

        @keyframes float-vertical {
          from {
            transform: translateY(0px);
          }
          to {
            transform: translateY(-10px);
          }
        }

        @keyframes reveal {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-reveal {
          animation: reveal 0.8s forwards;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .animate-fade-in {
          animation: fadeIn 1s forwards;
        }
      `}</style>
    </section>
  );
}
