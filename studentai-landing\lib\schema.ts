export function generateWebsiteSchema(url: string) {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "StudentAIDetector",
    url: url,
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: `${url}/search?q={search_term_string}`,
      },
      "query-input": "required name=search_term_string",
    },
  }
}

export function generateSoftwareApplicationSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: "StudentAIDetector",
    applicationCategory: "EducationalApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "AggregateOffer",
      priceCurrency: "USD",
      lowPrice: "0",
      highPrice: "19.99",
      offerCount: "3",
    },
    description:
      "AI-powered text analysis tool that detects AI-generated content in student submissions and helps preserve academic integrity.",
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.8",
      ratingCount: "256",
    },
  }
}

export function generateOrganizationSchema(url: string) {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "StudentAIDetector",
    url: url,
    logo: `${url}/logo.png`,
    sameAs: [
      "https://twitter.com/studentaidetector",
      "https://facebook.com/studentaidetector",
      "https://linkedin.com/company/studentaidetector",
    ],
  }
}

export function generateFAQSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: [
      {
        "@type": "Question",
        name: "How accurate is StudentAIDetector?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Our AI detection algorithms are continuously improved and currently achieve over 90% accuracy in identifying AI-generated content from major models.",
        },
      },
      {
        "@type": "Question",
        name: "Can I use StudentAIDetector for free?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Yes, we offer a free plan that includes basic AI detection features and allows up to 3 analyses per month.",
        },
      },
      {
        "@type": "Question",
        name: "What file formats does StudentAIDetector support?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Our basic and pro plans support .txt, .pdf, .doc, and .docx file formats. The free plan supports text input only.",
        },
      },
      {
        "@type": "Question",
        name: "How does StudentAIDetector detect AI-generated content?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "StudentAIDetector uses a sophisticated multi-layered approach including pattern recognition algorithms, linguistic analysis, contextual evaluation, and comparative benchmarking against known AI models.",
        },
      },
    ],
  }
}

export function generateProductSchema(url: string, plan: "free" | "basic" | "pro") {
  const planDetails = {
    free: {
      name: "Free Plan",
      price: "0",
      description: "Basic AI detection for occasional use with 3 analyses per month",
    },
    basic: {
      name: "Basic Plan",
      price: "9.99",
      description: "Enhanced detection and basic humanizing tools with 50 analyses per month",
    },
    pro: {
      name: "Pro Plan",
      price: "19.99",
      description: "Complete suite of detection and humanizing tools with unlimited analyses",
    },
  }

  return {
    "@context": "https://schema.org",
    "@type": "Product",
    name: `StudentAIDetector ${planDetails[plan].name}`,
    description: planDetails[plan].description,
    image: `${url}/images/${plan}-plan.jpg`,
    offers: {
      "@type": "Offer",
      price: planDetails[plan].price,
      priceCurrency: "USD",
      priceValidUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split("T")[0],
      availability: "https://schema.org/InStock",
    },
  }
}
