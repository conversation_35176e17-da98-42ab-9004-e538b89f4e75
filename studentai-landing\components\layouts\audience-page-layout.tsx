import type React from "react"
import { Breadcrumb } from "@/components/breadcrumb"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

interface AudiencePageLayoutProps {
  children: React.ReactNode
  title: string
  description: string
  audience: "educators" | "students" | "writers" | "marketers"
  recommendedTools?: Array<{
    name: string
    href: string
    description: string
  }>
  relatedResources?: Array<{
    name: string
    href: string
    type: "blog" | "guide" | "case-study"
  }>
}

export function AudiencePageLayout({
  children,
  title,
  description,
  audience,
  recommendedTools = [],
  relatedResources = [],
}: AudiencePageLayoutProps) {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-8 md:py-12">
        <Breadcrumb />

        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{title}</h1>
            <p className="text-xl text-muted-foreground">{description}</p>
          </div>

          {/* Main content */}
          <div className="mb-12">{children}</div>

          {/* Recommended tools section */}
          {recommendedTools.length > 0 && (
            <div className="border-t pt-8 mb-12">
              <h2 className="text-2xl font-bold mb-6">Recommended Tools</h2>
              <div className="grid gap-6 md:grid-cols-2">
                {recommendedTools.map((tool) => (
                  <div key={tool.href} className="border rounded-lg p-6">
                    <h3 className="text-xl font-bold mb-2">{tool.name}</h3>
                    <p className="text-muted-foreground mb-4">{tool.description}</p>
                    <Link href={tool.href}>
                      <Button variant="outline" className="w-full">
                        Learn More
                      </Button>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Related resources */}
          {relatedResources.length > 0 && (
            <div className="border-t pt-8 mb-12">
              <h2 className="text-2xl font-bold mb-6">Related Resources</h2>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {relatedResources.map((resource) => (
                  <Link
                    key={resource.href}
                    href={resource.href}
                    className="border rounded-lg p-6 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center mb-2">
                      <span className="text-xs px-2 py-0.5 bg-muted rounded-full mr-2">
                        {resource.type === "blog" ? "Blog" : resource.type === "guide" ? "Guide" : "Case Study"}
                      </span>
                    </div>
                    <h3 className="font-medium mb-2">{resource.name}</h3>
                    <div className="flex items-center text-sm text-primary">
                      <span>Read more</span>
                      <ArrowRight className="ml-1 h-3 w-3" />
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* CTA section */}
          <div className="border-t pt-8 text-center">
            <h2 className="text-xl font-bold mb-2">Ready to get started?</h2>
            <p className="text-muted-foreground mb-4">Try our tools today or explore our pricing plans</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg">Try For Free</Button>
              <Link href="/pricing">
                <Button variant="outline" size="lg">
                  View Pricing
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
