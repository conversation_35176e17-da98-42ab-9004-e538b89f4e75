import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export function Testimonials() {
  const testimonials = [
    {
      quote:
        "StudentAIDetector has been invaluable for our English department. We can quickly verify the authenticity of student submissions and help them improve their writing.",
      author: "Dr. <PERSON>",
      role: "English Professor, State University",
      avatar: "/testimonials/avatar1.jpg",
      initials: "<PERSON><PERSON>",
    },
    {
      quote:
        "As a content manager, I need to ensure our AI-assisted content reads naturally. This tool helps us humanize our drafts while maintaining our brand voice.",
      author: "<PERSON>",
      role: "Content Marketing Manager, TechCorp",
      avatar: "/testimonials/avatar2.jpg",
      initials: "<PERSON>",
    },
    {
      quote:
        "The humanization tools helped me improve my writing style. I use AI for drafting, but StudentAIDetector helps me make it sound like my own voice.",
      author: "<PERSON>",
      role: "Graduate Student",
      avatar: "/testimonials/avatar3.jpg",
      initials: "<PERSON>",
    },
  ]

  return (
    <section className="container py-12 md:py-16">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold tracking-tight mb-2">What Our Users Say</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Trusted by educators, students, and content professionals
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {testimonials.map((testimonial) => (
          <Card key={testimonial.author} className="bg-muted/30">
            <CardContent className="p-6">
              <div className="flex flex-col h-full">
                <blockquote className="text-lg italic mb-6 flex-1">"{testimonial.quote}"</blockquote>
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={testimonial.avatar} alt={testimonial.author} />
                    <AvatarFallback>{testimonial.initials}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{testimonial.author}</p>
                    <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  )
}
