"use client";

import { useState, useRef } from "react";
import {
  Mail,
  Send,
  MessageCircle,
  HelpCircle,
  ShieldCheck,
} from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { PageHeader } from "@/components/page-header";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";

// Import Firebase
import { initializeApp } from "firebase/app";
import {
  getFirestore,
  collection,
  addDoc,
  serverTimestamp,
} from "firebase/firestore";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB3kgomydBidn62mwIwLt0X_FcbRdNZmMA",
  authDomain: "student-ai-detector-b46d6.firebaseapp.com",
  projectId: "student-ai-detector-b46d6",
  storageBucket: "student-ai-detector-b46d6.firebasestorage.app",
  messagingSenderId: "566944962422",
  appId: "1:566944962422:web:59f09fcd4b9e7ad3484c57",
};

// Initialize Firebase (use lazy initialization to avoid multiple instances)
let app;
let db;

try {
  // Check if Firebase is already initialized
  app = initializeApp(firebaseConfig);
  db = getFirestore(app);
} catch (error) {
  if (!/already exists/.test(error.message)) {
    console.error("Firebase initialization error", error.stack);
  }
}

// Define support categories for contact
const supportCategories = [
  {
    name: "Customer Support",
    description: "General inquiries and assistance",
    icon: MessageCircle,
    email: "<EMAIL>",
    color: "blue",
  },
  {
    name: "Sales Team",
    description: "Pricing, bulk orders, and enterprise solutions",
    icon: ShieldCheck,
    email: "<EMAIL>",
    color: "green",
  },
  {
    name: "Technical Help",
    description: "API integration and technical troubleshooting",
    icon: HelpCircle,
    email: "<EMAIL>",
    color: "purple",
  },
];

export default function ContactPage() {
  const [formStatus, setFormStatus] = useState<
    "idle" | "sending" | "success" | "error"
  >("idle");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [subjectText, setSubjectText] = useState<string>("");

  // Form refs to get values
  const nameRef = useRef<HTMLInputElement>(null);
  const emailRef = useRef<HTMLInputElement>(null);
  const subjectRef = useRef<HTMLInputElement>(null);
  const messageRef = useRef<HTMLTextAreaElement>(null);
  const formRef = useRef<HTMLFormElement>(null);

  const handleCategorySelect = (categoryName: string) => {
    setSelectedCategory(categoryName);
    setSubjectText(`${categoryName} Inquiry`);
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setFormStatus("sending");

    if (!db) {
      console.error("Firestore not initialized");
      setFormStatus("error");
      return;
    }

    try {
      // Get form data
      const name = nameRef.current?.value;
      const email = emailRef.current?.value;
      const subject = subjectRef.current?.value;
      const message = messageRef.current?.value;
      const category = selectedCategory || "General Inquiry";

      // Save to Firestore
      await addDoc(collection(db, "contact"), {
        name,
        email,
        subject,
        message,
        category,
        timestamp: serverTimestamp(),
        status: "new",
      });

      // Success state
      setFormStatus("success");

      // Reset form after success
      formRef.current?.reset();
      setSelectedCategory(null);
    } catch (error) {
      console.error("Error submitting contact form:", error);
      setFormStatus("error");
    }
  };

  return (
    <>
      <Navbar />
      <div className="relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-b from-blue-50/50 via-white to-white dark:from-blue-950/20 dark:via-gray-900 dark:to-gray-900"></div>
          <div className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-blue-100/30 dark:bg-blue-900/10 blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-80 h-80 rounded-full bg-purple-100/30 dark:bg-purple-900/10 blur-3xl"></div>
        </div>

        <div className="container mx-auto py-16 space-y-16 pt-24 px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <PageHeader
              title="Contact Us"
              description="Have questions or feedback? We'd love to hear from you. Our team is ready to help with any inquiries you might have."
            />
          </motion.div>

          <div className="max-w-4xl mx-auto">
            {/* Contact form card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              <Card className="border-0 shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl">Send Us a Message</CardTitle>
                  <CardDescription>
                    We'll get back to you as soon as possible.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form
                    ref={formRef}
                    className="space-y-6"
                    onSubmit={handleSubmit}
                  >
                    {/* Message category selection */}
                    <div className="space-y-3">
                      <Label>I'd like to contact:</Label>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        {supportCategories.map((category) => (
                          <div
                            key={category.name}
                            onClick={() => handleCategorySelect(category.name)}
                            className={`cursor-pointer rounded-lg border p-3 transition-all ${
                              selectedCategory === category.name
                                ? "border-blue-500 dark:border-blue-400 bg-blue-50 dark:bg-blue-900/20"
                                : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
                            }`}
                          >
                            <div className="flex items-start space-x-3">
                              <div
                                className={`p-1.5 rounded-md ${
                                  selectedCategory === category.name
                                    ? "bg-blue-100 dark:bg-blue-900/30"
                                    : "bg-gray-100 dark:bg-gray-800"
                                }`}
                              >
                                <category.icon
                                  className={`h-5 w-5 ${
                                    selectedCategory === category.name
                                      ? "text-blue-600 dark:text-blue-400"
                                      : "text-gray-500 dark:text-gray-400"
                                  }`}
                                />
                              </div>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-gray-100">
                                  {category.name}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {category.description}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="name">Name</Label>
                        <Input
                          id="name"
                          ref={nameRef}
                          placeholder="Your name"
                          className="border-gray-200 dark:border-gray-700"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          ref={emailRef}
                          type="email"
                          placeholder="Your email address"
                          className="border-gray-200 dark:border-gray-700"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="subject">Subject</Label>
                      <Input
                        id="subject"
                        ref={subjectRef}
                        placeholder="What is this regarding?"
                        className="border-gray-200 dark:border-gray-700"
                        value={subjectText}
                        onChange={(e) => setSubjectText(e.target.value)}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">Message</Label>
                      <Textarea
                        id="message"
                        ref={messageRef}
                        placeholder="How can we help you?"
                        className="min-h-[180px] resize-none border-gray-200 dark:border-gray-700"
                        required
                      />
                    </div>

                    <Button
                      type="submit"
                      className="w-full flex items-center justify-center gap-2 transition-all bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white"
                      disabled={formStatus === "sending"}
                    >
                      {formStatus === "sending" ? (
                        <>
                          <span className="inline-block h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></span>
                          Sending...
                        </>
                      ) : formStatus === "success" ? (
                        <>
                          Message Sent!
                          <svg
                            className="h-5 w-5"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </>
                      ) : (
                        <>
                          Send Message
                          <Send className="h-4 w-4" />
                        </>
                      )}
                    </Button>

                    {formStatus === "success" && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        className="p-4 bg-green-50 dark:bg-green-900/20 rounded-md text-green-700 dark:text-green-300 text-center"
                      >
                        Thank you for reaching out! We've received your message
                        and will respond shortly.
                      </motion.div>
                    )}

                    {formStatus === "error" && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        className="p-4 bg-red-50 dark:bg-red-900/20 rounded-md text-red-700 dark:text-red-300 text-center"
                      >
                        There was an error sending your message. Please try
                        again.
                      </motion.div>
                    )}
                  </form>
                </CardContent>
              </Card>
            </motion.div>

            {/* Direct email options */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.5 }}
              className="mt-12"
            >
              <div className="text-center mb-8">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Or contact us directly
                </h3>
                <div className="h-1 w-20 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-2 rounded-full"></div>
              </div>

              <div className="flex flex-wrap justify-center gap-8">
                {supportCategories.map((category) => (
                  <a
                    key={category.name}
                    href={`mailto:${category.email}`}
                    className="flex items-center gap-3 px-6 py-2 rounded-full bg-white dark:bg-gray-800 shadow-md hover:shadow-lg transition-all group"
                  >
                    <div className="rounded-full p-2 bg-gray-100 dark:bg-gray-700 group-hover:bg-gray-200 dark:group-hover:bg-gray-600 transition-colors">
                      <Mail className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                    </div>
                    <span className="text-gray-700 dark:text-gray-300">
                      {category.email}
                    </span>
                  </a>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}
