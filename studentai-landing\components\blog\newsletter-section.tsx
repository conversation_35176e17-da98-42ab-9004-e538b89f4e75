import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"

interface NewsletterSectionProps {
  newsletterImage?: string
}

export function NewsletterSection({
  newsletterImage = "/placeholder.svg?height=400&width=600",
}: NewsletterSectionProps) {
  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="grid md:grid-cols-2 gap-0">
          <div className="bg-primary p-8 md:p-12 flex flex-col justify-center">
            <h3 className="text-2xl md:text-3xl font-bold text-primary-foreground mb-4">Subscribe to Our Newsletter</h3>
            <p className="text-primary-foreground/90 mb-6">
              Get the latest articles, resources, and updates on AI detection and content creation delivered straight to
              your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Input
                type="email"
                placeholder="Your email address"
                className="bg-primary-foreground/10 border-primary-foreground/20 text-primary-foreground placeholder:text-primary-foreground/70"
              />
              <Button variant="secondary" className="sm:w-auto">
                Subscribe
              </Button>
            </div>
          </div>
          <div className="aspect-video md:aspect-auto relative overflow-hidden">
            <img
              src={newsletterImage || "/placeholder.svg"}
              alt="Subscribe to our newsletter for AI detection and content creation updates"
              className="object-cover w-full h-full"
              width={600}
              height={400}
              loading="lazy"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
