"use client";

import { useState, useEffect, useRef } from "react";
import { motion, useAnimation, useInView } from "framer-motion";
import {
  Zap,
  Brain,
  FileText,
  Database,
  BarChart2,
  ChevronRight,
  CheckCircle,
  AlertTriangle,
  ArrowRight,
} from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

export default function HowDetectionWorks() {
  const [activeStep, setActiveStep] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: false, amount: 0.3 });
  const controls = useAnimation();

  // Example text for analysis visualization
  const exampleText =
    "The impact of artificial intelligence on modern society extends beyond technological innovation. " +
    "It transforms how we work, communicate, and solve problems across various domains. " +
    "AI systems continue to evolve, presenting both opportunities and challenges for human advancement.";

  useEffect(() => {
    if (isInView) {
      controls.start("visible");

      if (isAutoPlaying && !isHovering) {
        const interval = setInterval(() => {
          setActiveStep((prev) => (prev === 4 ? 0 : prev + 1));
        }, 4000);

        return () => clearInterval(interval);
      }
    }
  }, [isInView, isAutoPlaying, isHovering, controls]);

  const steps = [
    {
      title: "Text Ingestion",
      icon: FileText,
      description:
        "Our system processes text from various sources, tokenizing and preparing it for deep analysis.",
      color: "blue",
    },
    {
      title: "Pattern Recognition",
      icon: Database,
      description:
        "Our proprietary algorithms identify linguistic patterns common in AI-generated content.",
      color: "indigo",
    },
    {
      title: "Statistical Analysis",
      icon: BarChart2,
      description:
        "Advanced statistical models evaluate word distributions, entropy variations, and semantic coherence.",
      color: "purple",
    },
    {
      title: "Neural Processing",
      icon: Brain,
      description:
        "Our neural networks have been trained on millions of AI vs. human text samples to detect subtle differences.",
      color: "fuchsia",
    },
    {
      title: "Accuracy Verification",
      icon: Zap,
      description:
        "Multiple detection layers cross-verify results, ensuring 99.4% accuracy across all major AI models.",
      color: "pink",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  return (
    <section className="w-full py-24 overflow-hidden relative bg-gradient-to-b from-white via-gray-50 to-white dark:from-gray-900 dark:via-gray-800/50 dark:to-gray-900">
      {/* Decorative background elements */}
      <div className="absolute inset-0 overflow-hidden" style={{ zIndex: -1 }}>
        <div className="absolute top-40 right-[5%] w-[500px] h-[500px] opacity-20 dark:opacity-10">
          <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <path
              fill="#4F46E5"
              d="M39.9,-65.7C51.1,-58.5,59.3,-46.6,64.6,-34C69.8,-21.4,72,-8.1,70.8,4.8C69.5,17.7,64.8,30.1,56.8,39.9C48.8,49.7,37.5,56.8,25.6,60.4C13.6,64,1.1,64,-11.6,62.6C-24.2,61.2,-36.9,58.3,-46.9,51.1C-56.8,43.9,-63.9,32.4,-67.4,20.1C-70.9,7.8,-70.8,-5.3,-68.5,-18.4C-66.2,-31.4,-61.8,-44.4,-52.8,-52.4C-43.7,-60.3,-30.1,-63.3,-17.1,-64.7C-4.1,-66.2,8.4,-66.1,20.2,-67.2C31.9,-68.3,43,-68.6,52.1,-63.7C61.2,-58.9,68.5,-49.1,67.6,-38.9C66.7,-28.7,57.6,-18.2,52.8,-8.2C48,1.8,47.3,11.2,42.1,16.6C37,22,27.4,23.4,22.1,31.3C16.9,39.2,16,53.6,10.3,61.9C4.6,70.1,-5.9,72.3,-14.5,67.7C-23.1,63,-29.8,51.6,-39.6,44.1C-49.5,36.5,-62.6,32.9,-69.3,24.1C-76,15.3,-76.3,1.2,-74.7,-12.9C-73.1,-27.1,-69.5,-41.3,-60.5,-48.8C-51.4,-56.3,-36.9,-57,-24,-61.5C-11.2,-66.1,0.1,-74.4,12.4,-77.8C24.7,-81.2,37.9,-79.6,46.8,-73.1C55.6,-66.6,60,-55.2,58.4,-44.6C56.7,-34,49,-24.1,48.3,-15.1C47.7,-6.1,54.1,2,52,6.1C49.9,10.1,39.3,10,33.3,16C27.3,22,26,34.1,21,41.8C16,49.4,7.3,52.7,-2.2,55.9C-11.7,59.1,-22,62.3,-31.8,60.1C-41.6,58,-50.8,50.6,-57.3,41.2C-63.7,31.8,-67.4,20.4,-71.3,8.2C-75.2,-4.1,-79.2,-17.2,-75.2,-27.2"
            />
          </svg>
        </div>

        <div className="absolute bottom-[5%] left-[5%] w-[400px] h-[400px] opacity-20 dark:opacity-10">
          <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <path
              fill="#4338CA"
              d="M38.7,-65.7C48.9,-60.5,55.4,-47.5,60.8,-34.8C66.2,-22.2,70.4,-9.9,70.2,2.3C70,14.5,65.3,26.5,58.5,37.8C51.6,49.1,42.5,59.6,30.7,65.5C18.9,71.5,4.5,73,-8.4,70.5C-21.3,68.1,-32.8,61.7,-41.5,53C-50.1,44.2,-55.9,33,-60.7,21.2C-65.5,9.4,-69.3,-3,-68.5,-15.6C-67.6,-28.3,-62.1,-41.3,-52.5,-46.9C-43,-52.6,-29.5,-51,-17.9,-56.5C-6.3,-61.9,3.4,-74.5,16.2,-78.3C29,-82,44.9,-77,56.7,-68"
            />
          </svg>
        </div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-indigo-100/80 dark:bg-indigo-900/30 mb-6">
            <span className="flex h-2 w-2 rounded-full bg-indigo-500"></span>
            <span className="text-xs font-medium text-indigo-800 dark:text-indigo-300">
              Proprietary Technology
            </span>
          </div>

          <h2 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-400">
            How Our AI Detection Works
          </h2>
          <div className="h-1 w-40 bg-gradient-to-r from-blue-500 to-indigo-500 mx-auto mt-6 rounded-full"></div>
        </motion.div>

        {/* Interactive Text Analysis Visualization */}
        <div ref={containerRef} className="mb-16">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={controls}
            className="grid md:grid-cols-12 gap-6"
          >
            {/* Process steps sidebar */}
            <motion.div variants={itemVariants} className="md:col-span-4">
              <div className="space-y-1">
                {steps.map((step, index) => (
                  <div
                    key={index}
                    className={`relative p-4 rounded-lg transition-all duration-300 cursor-pointer ${
                      activeStep === index
                        ? `bg-${
                            step.color === "blue"
                              ? "blue"
                              : step.color === "indigo"
                              ? "indigo"
                              : step.color === "purple"
                              ? "purple"
                              : step.color === "fuchsia"
                              ? "fuchsia"
                              : "pink"
                          }-50 dark:bg-${
                            step.color === "blue"
                              ? "blue"
                              : step.color === "indigo"
                              ? "indigo"
                              : step.color === "purple"
                              ? "purple"
                              : step.color === "fuchsia"
                              ? "fuchsia"
                              : "pink"
                          }-900/20 border border-${
                            step.color === "blue"
                              ? "blue"
                              : step.color === "indigo"
                              ? "indigo"
                              : step.color === "purple"
                              ? "purple"
                              : step.color === "fuchsia"
                              ? "fuchsia"
                              : "pink"
                          }-200 dark:border-${
                            step.color === "blue"
                              ? "blue"
                              : step.color === "indigo"
                              ? "indigo"
                              : step.color === "purple"
                              ? "purple"
                              : step.color === "fuchsia"
                              ? "fuchsia"
                              : "pink"
                          }-700/40 shadow-md`
                        : "bg-white/50 dark:bg-gray-800/50 hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-transparent"
                    }`}
                    onMouseEnter={() => {
                      setActiveStep(index);
                      setIsHovering(true);
                      setIsAutoPlaying(false);
                    }}
                    onMouseLeave={() => {
                      setIsHovering(false);
                      setIsAutoPlaying(true);
                    }}
                  >
                    <div className="flex items-start gap-3">
                      <div
                        className={`p-2 rounded-lg ${
                          activeStep === index
                            ? `bg-${
                                step.color === "blue"
                                  ? "blue"
                                  : step.color === "indigo"
                                  ? "indigo"
                                  : step.color === "purple"
                                  ? "purple"
                                  : step.color === "fuchsia"
                                  ? "fuchsia"
                                  : "pink"
                              }-100 dark:bg-${
                                step.color === "blue"
                                  ? "blue"
                                  : step.color === "indigo"
                                  ? "indigo"
                                  : step.color === "purple"
                                  ? "purple"
                                  : step.color === "fuchsia"
                                  ? "fuchsia"
                                  : "pink"
                              }-900/30 text-${
                                step.color === "blue"
                                  ? "blue"
                                  : step.color === "indigo"
                                  ? "indigo"
                                  : step.color === "purple"
                                  ? "purple"
                                  : step.color === "fuchsia"
                                  ? "fuchsia"
                                  : "pink"
                              }-600 dark:text-${
                                step.color === "blue"
                                  ? "blue"
                                  : step.color === "indigo"
                                  ? "indigo"
                                  : step.color === "purple"
                                  ? "purple"
                                  : step.color === "fuchsia"
                                  ? "fuchsia"
                                  : "pink"
                              }-400`
                            : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
                        }`}
                      >
                        <step.icon className="h-5 w-5" />
                      </div>

                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 dark:text-white flex items-center">
                          {step.title}
                          {activeStep === index && (
                            <ChevronRight className="ml-1 h-4 w-4 text-blue-500" />
                          )}
                        </h3>

                        <div
                          className={`mt-1 text-sm text-gray-600 dark:text-gray-300 transition-all duration-300 ${
                            activeStep === index
                              ? "opacity-100 max-h-20"
                              : "opacity-0 max-h-0 overflow-hidden"
                          }`}
                        >
                          {step.description}
                        </div>
                      </div>
                    </div>

                    {/* Progress indicator */}
                    {index < steps.length - 1 && (
                      <div
                        className={`ml-5 mt-1 w-0.5 h-6 ${
                          activeStep > index
                            ? "bg-blue-400 dark:bg-blue-500"
                            : "bg-gray-200 dark:bg-gray-700"
                        }`}
                      ></div>
                    )}
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Interactive visualization screen */}
            <motion.div variants={itemVariants} className="md:col-span-8">
              <div className="h-full min-h-[400px] bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-xl overflow-hidden">
                {/* Terminal-like header */}
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900/80">
                  <div className="flex items-center">
                    <div className="flex space-x-1.5">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="ml-4 text-sm font-medium text-gray-600 dark:text-gray-300">
                      AI Detection Engine
                    </div>
                  </div>
                </div>

                {/* Content area with step-specific visualizations */}
                <div className="p-6 h-[350px] relative">
                  {/* Text Ingestion Visualization */}
                  {activeStep === 0 && (
                    <div className="h-full flex flex-col">
                      <div className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                        Text Processing
                      </div>
                      <div className="flex-1 bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-600 rounded-lg p-4 overflow-hidden">
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5 }}
                        >
                          {exampleText.split(" ").map((word, i) => (
                            <motion.span
                              key={i}
                              className="inline-block mr-1.5 mb-1.5 px-1.5 py-0.5 rounded-md text-sm"
                              initial={{ opacity: 0, y: 10 }}
                              animate={{
                                opacity: 1,
                                y: 0,
                                backgroundColor: [
                                  "rgba(255, 255, 255, 0)",
                                  i % 3 === 0
                                    ? "rgba(59, 130, 246, 0.1)"
                                    : i % 4 === 0
                                    ? "rgba(99, 102, 241, 0.1)"
                                    : i % 5 === 0
                                    ? "rgba(139, 92, 246, 0.1)"
                                    : "rgba(236, 72, 153, 0.1)",
                                  "rgba(255, 255, 255, 0)",
                                ],
                                color: [
                                  "rgba(75, 85, 99, 1)",
                                  i % 3 === 0
                                    ? "rgba(37, 99, 235, 1)"
                                    : i % 4 === 0
                                    ? "rgba(79, 70, 229, 1)"
                                    : i % 5 === 0
                                    ? "rgba(124, 58, 237, 1)"
                                    : "rgba(219, 39, 119, 1)",
                                  "rgba(75, 85, 99, 1)",
                                ],
                              }}
                              transition={{
                                duration: 0.5,
                                delay: i * 0.03,
                                backgroundColor: {
                                  repeat: Infinity,
                                  repeatType: "reverse",
                                  duration: 3,
                                  delay: i * 0.08,
                                },
                                color: {
                                  repeat: Infinity,
                                  repeatType: "reverse",
                                  duration: 3,
                                  delay: i * 0.08,
                                },
                              }}
                            >
                              {word}
                            </motion.span>
                          ))}
                          <div className="mt-8 flex items-center border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
                              Processing:
                            </div>
                            <div className="relative h-2 bg-gray-200 dark:bg-gray-700 rounded-full flex-grow overflow-hidden">
                              <motion.div
                                className="absolute top-0 left-0 h-full bg-blue-500 dark:bg-blue-400 rounded-full"
                                initial={{ width: "0%" }}
                                animate={{ width: "100%" }}
                                transition={{ duration: 3, repeat: Infinity }}
                              ></motion.div>
                            </div>
                            <div className="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                              Tokenizing
                            </div>
                          </div>
                        </motion.div>
                      </div>
                    </div>
                  )}

                  {/* Pattern Recognition Visualization */}
                  {activeStep === 1 && (
                    <div className="h-full flex flex-col">
                      <div className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                        Pattern Analysis
                      </div>
                      <div className="flex-1 bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-600 rounded-lg p-4 overflow-hidden">
                        <div className="grid grid-cols-2 gap-4 h-full">
                          <div className="flex flex-col">
                            <div className="text-xs text-center font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Human Text Patterns
                            </div>
                            <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-800/50 p-3 relative overflow-hidden">
                              <div className="space-y-2">
                                {[
                                  0.3, 0.7, 0.5, 0.8, 0.4, 0.6, 0.9, 0.5, 0.3,
                                  0.7,
                                ].map((value, i) => (
                                  <motion.div
                                    key={i}
                                    className="h-3 rounded-full bg-green-200 dark:bg-green-900/60"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${value * 100}%` }}
                                    transition={{
                                      duration: 0.5,
                                      delay: i * 0.1,
                                    }}
                                  />
                                ))}
                              </div>
                              <motion.div
                                className="absolute bottom-3 right-3 text-xs font-medium text-green-600 dark:text-green-400"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 1.5 }}
                              >
                                Variable Pattern
                              </motion.div>
                            </div>
                          </div>
                          <div className="flex flex-col">
                            <div className="text-xs text-center font-medium text-gray-500 dark:text-gray-400 mb-2">
                              AI Text Patterns
                            </div>
                            <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-800/50 p-3 relative overflow-hidden">
                              <div className="space-y-2">
                                {[
                                  0.6, 0.6, 0.5, 0.7, 0.6, 0.6, 0.7, 0.5, 0.6,
                                  0.6,
                                ].map((value, i) => (
                                  <motion.div
                                    key={i}
                                    className="h-3 rounded-full bg-indigo-200 dark:bg-indigo-900/60"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${value * 100}%` }}
                                    transition={{
                                      duration: 0.5,
                                      delay: i * 0.1,
                                    }}
                                  />
                                ))}
                              </div>
                              <motion.div
                                className="absolute bottom-3 right-3 text-xs font-medium text-indigo-600 dark:text-indigo-400"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 1.5 }}
                              >
                                Consistent Pattern
                              </motion.div>
                            </div>
                          </div>
                        </div>
                        <div className="mt-4 text-sm text-gray-600 dark:text-gray-300">
                          <div className="flex items-center justify-between">
                            <div>Pattern Matching:</div>
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 2 }}
                              className="text-indigo-600 dark:text-indigo-400 font-medium"
                            >
                              AI Detection: 76%
                            </motion.div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Statistical Analysis */}
                  {activeStep === 2 && (
                    <div className="h-full flex flex-col">
                      <div className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                        Statistical Analysis
                      </div>
                      <div className="flex-1 bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-600 rounded-lg p-4 overflow-hidden">
                        <div className="h-full flex flex-col">
                          <div className="grid grid-cols-2 gap-4 flex-1">
                            <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-800/50 p-3">
                              <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                                Entropy Distribution
                              </div>
                              <div className="h-[120px] relative">
                                {/* Entropy chart */}
                                <svg
                                  className="w-full h-full"
                                  viewBox="0 0 100 50"
                                >
                                  <motion.path
                                    d="M0,50 C10,10 20,40 30,25 C40,10 50,20 60,30 C70,40 80,15 90,25 L90,50 L0,50 Z"
                                    fill="rgba(139, 92, 246, 0.2)"
                                    strokeWidth="2"
                                    stroke="rgba(139, 92, 246, 0.8)"
                                    initial={{ pathLength: 0, opacity: 0 }}
                                    animate={{ pathLength: 1, opacity: 1 }}
                                    transition={{
                                      duration: 1.5,
                                      ease: "easeInOut",
                                    }}
                                  />
                                </svg>
                                <motion.div
                                  className="absolute top-1 right-1 text-xs font-medium text-purple-600 dark:text-purple-400"
                                  initial={{ opacity: 0 }}
                                  animate={{ opacity: 1 }}
                                  transition={{ delay: 1.5 }}
                                >
                                  Variance: High
                                </motion.div>
                              </div>
                            </div>
                            <div className="border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-800/50 p-3">
                              <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                                Perplexity Score
                              </div>
                              <div className="h-[120px] flex items-center justify-center">
                                <div className="relative w-28 h-28">
                                  <svg
                                    className="w-full h-full"
                                    viewBox="0 0 100 100"
                                  >
                                    <motion.circle
                                      cx="50"
                                      cy="50"
                                      r="45"
                                      fill="none"
                                      stroke="#e2e8f0"
                                      strokeWidth="10"
                                    />
                                    <motion.circle
                                      cx="50"
                                      cy="50"
                                      r="45"
                                      fill="none"
                                      stroke="#8b5cf6"
                                      strokeWidth="10"
                                      strokeDasharray="282.7"
                                      initial={{ strokeDashoffset: 282.7 }}
                                      animate={{
                                        strokeDashoffset: 282.7 * (1 - 0.82),
                                      }}
                                      transition={{
                                        duration: 1.5,
                                        ease: "easeInOut",
                                      }}
                                    />
                                  </svg>
                                  <motion.div
                                    className="absolute inset-0 flex items-center justify-center text-xl font-bold text-purple-600 dark:text-purple-400"
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    transition={{ delay: 1, duration: 0.5 }}
                                  >
                                    82%
                                  </motion.div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="mt-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-800/50 p-3">
                            <div className="flex items-center justify-between text-xs font-medium">
                              <div className="text-gray-500 dark:text-gray-400">
                                Analysis Results
                              </div>
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 2 }}
                                className="text-purple-600 dark:text-purple-400"
                              >
                                AI Detection: 82%
                              </motion.div>
                            </div>
                            <motion.div
                              className="mt-2 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
                              initial={{ width: "100%" }}
                              animate={{ width: "100%" }}
                            >
                              <motion.div
                                className="h-full bg-purple-500 rounded-full"
                                initial={{ width: "0%" }}
                                animate={{ width: "82%" }}
                                transition={{ duration: 1.5, delay: 0.5 }}
                              />
                            </motion.div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Neural Network Visualization */}
                  {activeStep === 3 && (
                    <div className="h-full flex flex-col">
                      <div className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                        Neural Network Processing
                      </div>
                      <div className="relative flex-1 bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-600 rounded-lg p-4 overflow-hidden">
                        <div className="absolute inset-0 flex items-center justify-between p-10">
                          {/* Input layer */}
                          <div className="flex flex-col space-y-4 relative">
                            {[1, 2, 3, 4, 5, 6].map((i) => (
                              <motion.div
                                key={i}
                                className="w-4 h-4 bg-blue-500 dark:bg-blue-600 rounded-full"
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{ delay: 0.1 * i, duration: 0.3 }}
                              />
                            ))}
                            <div className="absolute left-6 top-1/2 -translate-y-1/2 text-xs font-medium text-gray-500 dark:text-gray-400">
                              Input
                            </div>
                          </div>

                          {/* Hidden layers */}
                          {[1, 2].map((layer) => (
                            <div
                              key={layer}
                              className="flex flex-col space-y-3 relative"
                            >
                              {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
                                <motion.div
                                  key={i}
                                  className="w-4 h-4 bg-purple-500 dark:bg-purple-600 rounded-full"
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  transition={{
                                    delay: 0.5 + 0.05 * i + layer * 0.2,
                                    duration: 0.3,
                                  }}
                                />
                              ))}
                              <div className="absolute -top-6 left-1/2 -translate-x-1/2 text-xs font-medium text-gray-500 dark:text-gray-400">
                                Hidden {layer}
                              </div>
                            </div>
                          ))}

                          {/* Output layer */}
                          <div className="flex flex-col space-y-8 items-center relative">
                            <motion.div
                              className="w-5 h-5 bg-green-500 dark:bg-green-600 rounded-full"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 1.8, duration: 0.3 }}
                            />
                            <motion.div
                              className="w-5 h-5 bg-red-500 dark:bg-red-600 rounded-full"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: 1.8, duration: 0.3 }}
                            />
                            <div className="absolute -top-6 text-xs font-medium text-gray-500 dark:text-gray-400">
                              Output
                            </div>
                          </div>
                        </div>

                        {/* Neural network connections */}
                        <motion.svg
                          className="absolute inset-0 w-full h-full"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5 }}
                        >
                          {/* Generate connections for first layer */}
                          {[...Array(6)].map((_, i) =>
                            [...Array(8)].map((_, j) => (
                              <motion.path
                                key={`1-${i}-${j}`}
                                d={`M 24 ${28 + i * 24} L ${150} ${
                                  22 + j * 16
                                }`}
                                stroke="rgba(168, 85, 247, 0.2)"
                                strokeWidth="1"
                                fill="none"
                                initial={{ pathLength: 0, opacity: 0 }}
                                animate={{ pathLength: 1, opacity: 1 }}
                                transition={{
                                  delay: 0.3 + i * 0.05,
                                  duration: 0.4,
                                }}
                              />
                            ))
                          )}

                          {/* Generate connections for second layer */}
                          {[...Array(8)].map((_, i) =>
                            [...Array(8)].map((_, j) => (
                              <motion.path
                                key={`2-${i}-${j}`}
                                d={`M ${150} ${22 + i * 16} L ${280} ${
                                  22 + j * 16
                                }`}
                                stroke="rgba(168, 85, 247, 0.2)"
                                strokeWidth="1"
                                fill="none"
                                initial={{ pathLength: 0, opacity: 0 }}
                                animate={{ pathLength: 1, opacity: 1 }}
                                transition={{
                                  delay: 0.7 + i * 0.03,
                                  duration: 0.4,
                                }}
                              />
                            ))
                          )}

                          {/* Generate connections to output layer */}
                          {[...Array(8)].map((_, i) => (
                            <>
                              <motion.path
                                key={`3-${i}-1`}
                                d={`M ${280} ${22 + i * 16} L ${400} ${85}`}
                                stroke="rgba(168, 85, 247, 0.2)"
                                strokeWidth="1"
                                fill="none"
                                initial={{ pathLength: 0, opacity: 0 }}
                                animate={{ pathLength: 1, opacity: 1 }}
                                transition={{
                                  delay: 1.1 + i * 0.03,
                                  duration: 0.5,
                                }}
                              />
                              <motion.path
                                key={`3-${i}-2`}
                                d={`M ${280} ${22 + i * 16} L ${400} ${180}`}
                                stroke="rgba(168, 85, 247, 0.2)"
                                strokeWidth="1"
                                fill="none"
                                initial={{ pathLength: 0, opacity: 0 }}
                                animate={{ pathLength: 1, opacity: 1 }}
                                transition={{
                                  delay: 1.1 + i * 0.03,
                                  duration: 0.5,
                                }}
                              />
                            </>
                          ))}

                          {/* Animated data pulses */}
                          {[...Array(3)].map((_, i) => (
                            <motion.circle
                              key={i}
                              cx="12"
                              cy={28 + i * 48}
                              r="3"
                              fill="#3B82F6"
                              initial={{ opacity: 0 }}
                              animate={{
                                opacity: [0, 1, 1, 0],
                                cx: [24, 150, 280, 400],
                                cy: [
                                  28 + i * 48,
                                  22 + ((i * 30) % 128),
                                  22 + ((i * 20) % 128),
                                  85,
                                ],
                              }}
                              transition={{
                                duration: 2,
                                delay: 1.5 + i * 0.2,
                                ease: "easeInOut",
                                repeat: Infinity,
                                repeatDelay: 1,
                              }}
                            />
                          ))}
                        </motion.svg>

                        {/* Results overlay */}
                        <motion.div
                          className="absolute bottom-4 right-4 bg-white/90 dark:bg-gray-800/90 border border-gray-200 dark:border-gray-700 rounded-lg p-3 shadow-lg"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 2.2, duration: 0.4 }}
                        >
                          <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                            Neural Network Result
                          </div>
                          <div className="text-lg font-bold text-fuchsia-600 dark:text-fuchsia-400">
                            AI Detection: 91%
                          </div>
                        </motion.div>
                      </div>
                    </div>
                  )}

                  {/* Accuracy Verification */}
                  {activeStep === 4 && (
                    <div className="h-full flex flex-col">
                      <div className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                        Final Verification
                      </div>
                      <div className="flex-1 bg-gray-50 dark:bg-gray-900/30 border border-gray-200 dark:border-gray-600 rounded-lg p-4 overflow-hidden">
                        <div className="grid grid-cols-2 gap-4 h-full">
                          <div className="flex flex-col">
                            <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Detection Results
                            </div>
                            <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-800/50 p-3">
                              <div className="space-y-4">
                                {[
                                  {
                                    name: "Pattern Recognition",
                                    value: 76,
                                    color: "indigo",
                                  },
                                  {
                                    name: "Statistical Analysis",
                                    value: 82,
                                    color: "purple",
                                  },
                                  {
                                    name: "Neural Network",
                                    value: 91,
                                    color: "fuchsia",
                                  },
                                ].map((detector, i) => (
                                  <div key={i} className="space-y-1">
                                    <div className="flex justify-between text-xs">
                                      <span className="text-gray-600 dark:text-gray-300">
                                        {detector.name}
                                      </span>
                                      <motion.span
                                        className={`font-medium text-${
                                          detector.color === "indigo"
                                            ? "indigo"
                                            : detector.color === "purple"
                                            ? "purple"
                                            : "fuchsia"
                                        }-600 dark:text-${
                                          detector.color === "indigo"
                                            ? "indigo"
                                            : detector.color === "purple"
                                            ? "purple"
                                            : "fuchsia"
                                        }-400`}
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        transition={{ delay: 0.5 + i * 0.2 }}
                                      >
                                        {detector.value}%
                                      </motion.span>
                                    </div>
                                    <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                      <motion.div
                                        className={`h-full bg-${
                                          detector.color === "indigo"
                                            ? "indigo"
                                            : detector.color === "purple"
                                            ? "purple"
                                            : "fuchsia"
                                        }-500 rounded-full`}
                                        initial={{ width: "0%" }}
                                        animate={{
                                          width: `${detector.value}%`,
                                        }}
                                        transition={{
                                          duration: 0.8,
                                          delay: 0.5 + i * 0.2,
                                        }}
                                      />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                          <div className="flex flex-col">
                            <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Final Verdict
                            </div>
                            <div className="flex-1 border border-gray-200 dark:border-gray-700 rounded-lg bg-white/50 dark:bg-gray-800/50 p-3 flex flex-col items-center justify-center">
                              <motion.div
                                className="w-28 h-28 rounded-full border-8 border-red-200 dark:border-red-900/30 flex items-center justify-center mb-4"
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                transition={{
                                  type: "spring",
                                  stiffness: 200,
                                  damping: 15,
                                  delay: 1,
                                }}
                              >
                                <motion.div
                                  className="text-3xl font-bold text-red-600 dark:text-red-500"
                                  initial={{ opacity: 0 }}
                                  animate={{ opacity: 1 }}
                                  transition={{ delay: 1.3 }}
                                >
                                  89%
                                </motion.div>
                              </motion.div>
                              <motion.div
                                className="flex items-center gap-2 text-red-600 dark:text-red-500 font-medium"
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                transition={{ delay: 1.6 }}
                              >
                                <AlertTriangle className="h-4 w-4" />
                                <span>AI-Generated Content</span>
                              </motion.div>
                            </div>
                          </div>
                        </div>
                        <motion.div
                          className="mt-4 p-2 rounded-lg bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-900/30 text-sm text-red-800 dark:text-red-300"
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 1.9 }}
                        >
                          <div className="flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                            <span>
                              This text was likely generated by an AI language
                              model. Our analysis detected statistical patterns,
                              linguistic markers, and semantic structures
                              consistent with AI-generated content.
                            </span>
                          </div>
                        </motion.div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Key features */}
        <div className="grid md:grid-cols-3 gap-8 mt-14">
          {[
            {
              title: "Multi-Model Detection",
              description:
                "Trained on all major AI models including GPT-4, Claude, Bard, and others to ensure comprehensive detection.",
              color: "blue",
            },
            {
              title: "99.4% Accuracy Rate",
              description:
                "Our detection algorithms have been verified with over 3 million documents, achieving industry-leading accuracy.",
              color: "indigo",
            },
            {
              title: "Real-time Analysis",
              description:
                "Get results in seconds with our highly optimized detection engine, no matter how long the content.",
              color: "violet",
            },
          ].map((feature, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.2 + i * 0.1, duration: 0.5 }}
              className="p-6 rounded-xl border border-blue-100 dark:border-blue-900/30 bg-blue-50/50 dark:bg-blue-900/20 hover:shadow-md transition-shadow"
            >
              <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Interactive call to action */}
        <motion.div
          className="mt-20 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ delay: 0.7, duration: 0.5 }}
        >
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Experience our AI detection technology for yourself
          </h3>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            Try our advanced AI content detector with your own text and see how
            our multi-layered detection approach delivers industry-leading
            accuracy.
          </p>
          <Button size="lg" asChild>
            <Link href="/detector" className="flex items-center gap-2">
              Try the AI Detector
              <ArrowRight className="h-5 w-5" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
}
