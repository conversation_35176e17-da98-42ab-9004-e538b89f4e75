import type { Metadata } from "next"
import { ToolPageLayout } from "@/components/layouts/tool-page-layout"
import { generateMetaTags } from "@/lib/seo-utils"

export const metadata: Metadata = generateMetaTags({
  title: "API Access - Integrate AI Detection into Your Applications",
  description:
    "Integrate our AI detection and humanization capabilities into your applications with our API. Customize your workflow and automate content analysis.",
  keywords: ["API access", "AI detection API", "content analysis API", "integrate AI detection", "custom AI workflow"],
  canonical: "/tools/api",
})

export default function ApiPage() {
  return (
    <ToolPageLayout
      title="API Access"
      description="Integrate our AI detection and humanization capabilities into your applications."
    >
      <div>
        <p>
          Our API allows you to integrate our AI detection and humanization capabilities directly into your
          applications, automating content analysis and customizing your workflow.
        </p>
      </div>
    </ToolPageLayout>
  )
}
