"use client"

import type React from "react"

import { useState, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Upload, File, X } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface FileUploaderProps {
  onFileContent: (content: string) => void
  allowedFileTypes?: string[]
  maxSizeInBytes?: number
}

export function FileUploader({
  onFileContent,
  allowedFileTypes = [".txt", ".pdf", ".doc", ".docx"],
  maxSizeInBytes = 10 * 1024 * 1024, // 10MB default
}: FileUploaderProps) {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Map file extensions to MIME types
  const fileTypeMap: Record<string, string[]> = {
    ".txt": ["text/plain"],
    ".pdf": ["application/pdf"],
    ".doc": ["application/msword"],
    ".docx": ["application/vnd.openxmlformats-officedocument.wordprocessingml.document"],
    all: ["*/*"],
  }

  // Convert allowed file types to MIME types
  const getAllowedMimeTypes = () => {
    if (allowedFileTypes.includes("all")) return null // Allow all types

    return allowedFileTypes.flatMap((ext) => fileTypeMap[ext] || [])
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (!selectedFile) return

    // Check file type
    const allowedMimeTypes = getAllowedMimeTypes()
    if (allowedMimeTypes && !allowedMimeTypes.includes(selectedFile.type)) {
      setError(`Invalid file type. Please upload one of these formats: ${allowedFileTypes.join(", ")}`)
      return
    }

    // Check file size
    if (selectedFile.size > maxSizeInBytes) {
      setError(`File is too large. Maximum size is ${maxSizeInBytes / (1024 * 1024)}MB.`)
      return
    }

    setFile(selectedFile)
    setError(null)
    processFile(selectedFile)
  }

  const processFile = async (file: File) => {
    setIsUploading(true)
    setUploadProgress(0)

    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress((prev) => {
        if (prev >= 95) {
          clearInterval(interval)
          return 95
        }
        return prev + 5
      })
    }, 100)

    try {
      // In a real app, you would process different file types differently
      // For this demo, we'll just read text files
      if (file.type === "text/plain") {
        const text = await readTextFile(file)
        onFileContent(text)
      } else {
        // For other file types, we'd use appropriate libraries or APIs
        // For this demo, we'll simulate extracting text
        await new Promise((resolve) => setTimeout(resolve, 1500))
        onFileContent(
          `Content extracted from ${file.name}. In a real application, we would use appropriate libraries to extract text from ${file.type} files.`,
        )
      }

      setUploadProgress(100)
      setTimeout(() => {
        setIsUploading(false)
      }, 500)
    } catch (error) {
      console.error("Error processing file:", error)
      setError("Error processing file. Please try again.")
      setIsUploading(false)
    } finally {
      clearInterval(interval)
    }
  }

  const readTextFile = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.onerror = (e) => reject(e)
      reader.readAsText(file)
    })
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    const droppedFile = e.dataTransfer.files[0]
    if (droppedFile) {
      setFile(droppedFile)
      processFile(droppedFile)
    }
  }

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
  }

  const handleRemoveFile = () => {
    setFile(null)
    setError(null)
    setUploadProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
    onFileContent("")
  }

  // Format allowed file types for display
  const formatAllowedTypes = () => {
    if (allowedFileTypes.includes("all")) return "all file formats"
    return allowedFileTypes.join(", ")
  }

  return (
    <div className="space-y-4">
      {!file ? (
        <div
          className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:bg-muted/50 transition-colors"
          onClick={() => fileInputRef.current?.click()}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <div className="flex flex-col items-center gap-2">
            <Upload className="h-10 w-10 text-muted-foreground" />
            <h3 className="font-medium text-lg">Upload a file</h3>
            <p className="text-sm text-muted-foreground">Drag and drop or click to upload</p>
            <p className="text-xs text-muted-foreground">
              Supports {formatAllowedTypes()} (max {maxSizeInBytes / (1024 * 1024)}MB)
            </p>
          </div>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            accept={allowedFileTypes.includes("all") ? undefined : allowedFileTypes.join(",")}
          />
        </div>
      ) : (
        <div className="border rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <File className="h-6 w-6 text-primary" />
              <div>
                <p className="font-medium">{file.name}</p>
                <p className="text-xs text-muted-foreground">{(file.size / 1024).toFixed(2)} KB</p>
              </div>
            </div>
            <Button variant="ghost" size="icon" onClick={handleRemoveFile} disabled={isUploading}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {isUploading && (
            <div className="mt-4 space-y-2">
              <Progress value={uploadProgress} className="h-2" />
              <p className="text-xs text-muted-foreground text-right">{uploadProgress}%</p>
            </div>
          )}
        </div>
      )}

      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  )
}
