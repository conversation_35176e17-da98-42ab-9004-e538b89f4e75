"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, Card<PERSON>eader } from "@/components/ui/card"
import { Check, X } from "lucide-react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

export function PricingTable() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly")

  const handleBillingCycleChange = () => {
    setBillingCycle(billingCycle === "monthly" ? "annual" : "monthly")
  }

  const plans = [
    {
      name: "Free",
      description: "For individuals with occasional needs",
      price: {
        monthly: 0,
        annual: 0,
      },
      features: [
        { name: "AI Detection Queries", value: "10/month" },
        { name: "Text Volume", value: "Up to 1,500 words per query" },
        { name: "Basic Humanization Tools", value: "3/month" },
        { name: "File Upload", value: "Text only (.txt)" },
        { name: "Detection Accuracy", value: "Standard" },
        { name: "History Retention", value: "7 days" },
        { name: "Email Support", value: false },
      ],
      cta: "Get Started",
      ctaLink: "/signup",
      popular: false,
    },
    {
      name: "Basic",
      description: "For educators and small institutions",
      price: {
        monthly: 9.99,
        annual: 7.99,
      },
      features: [
        { name: "AI Detection Queries", value: "100/month" },
        { name: "Text Volume", value: "Up to 5,000 words per query" },
        { name: "Humanization Tools", value: "50/month" },
        { name: "File Upload", value: ".txt, .pdf, .docx" },
        { name: "Detection Accuracy", value: "Enhanced" },
        { name: "History Retention", value: "30 days" },
        { name: "Email Support", value: true },
        { name: "Style Adjustment Tool", value: true },
        { name: "Batch Processing", value: "Up to 5 files" },
      ],
      cta: "Subscribe",
      ctaLink: "/signup?plan=basic",
      popular: true,
    },
    {
      name: "Pro",
      description: "For departments and institutions",
      price: {
        monthly: 19.99,
        annual: 15.99,
      },
      features: [
        { name: "AI Detection Queries", value: "500/month" },
        { name: "Text Volume", value: "Up to 15,000 words per query" },
        { name: "Humanization Tools", value: "Unlimited" },
        { name: "File Upload", value: "All formats" },
        { name: "Detection Accuracy", value: "Premium" },
        { name: "History Retention", value: "90 days" },
        { name: "Priority Support", value: true },
        { name: "All Humanization Tools", value: true },
        { name: "Batch Processing", value: "Up to 20 files" },
        { name: "Advanced Analytics", value: true },
        { name: "API Access", value: "100 calls/month" },
      ],
      cta: "Subscribe",
      ctaLink: "/signup?plan=pro",
      popular: false,
    },
  ]

  return (
    <div className="space-y-8">
      <div className="flex justify-center">
        <div className="flex items-center space-x-2">
          <Label htmlFor="billing-toggle" className={billingCycle === "monthly" ? "font-medium" : ""}>
            Monthly
          </Label>
          <Switch id="billing-toggle" checked={billingCycle === "annual"} onCheckedChange={handleBillingCycleChange} />
          <div className="flex items-center gap-1.5">
            <Label htmlFor="billing-toggle" className={billingCycle === "annual" ? "font-medium" : ""}>
              Annual
            </Label>
            <Badge variant="outline" className="text-primary border-primary">
              Save 20%
            </Badge>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {plans.map((plan) => (
          <Card key={plan.name} className={`flex flex-col ${plan.popular ? "border-primary shadow-md relative" : ""}`}>
            {plan.popular && (
              <div className="absolute -top-4 left-0 right-0 flex justify-center">
                <Badge className="bg-primary hover:bg-primary">Most Popular</Badge>
              </div>
            )}
            <CardHeader className="flex flex-col space-y-1.5 pb-4">
              <h3 className="text-2xl font-bold">{plan.name}</h3>
              <p className="text-sm text-muted-foreground">{plan.description}</p>
              <div className="mt-4 flex items-baseline text-4xl font-bold">
                ${plan.price[billingCycle].toFixed(2)}
                <span className="ml-1 text-base font-medium text-muted-foreground">/month</span>
              </div>
              {billingCycle === "annual" && plan.price.annual > 0 && (
                <p className="text-sm text-muted-foreground">
                  Billed annually (${(plan.price.annual * 12).toFixed(2)})
                </p>
              )}
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-3">
                {plan.features.map((feature) => (
                  <li key={feature.name} className="flex items-start">
                    {feature.value === true ? (
                      <Check className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                    ) : feature.value === false ? (
                      <X className="mr-2 mt-0.5 h-4 w-4 text-muted-foreground/70" />
                    ) : (
                      <Check className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                    )}
                    <div>
                      <span className="text-sm">{feature.name}</span>
                      {feature.value !== true && feature.value !== false && (
                        <span className="ml-1 text-xs text-muted-foreground">({feature.value})</span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Link href={plan.ctaLink} className="w-full">
                <Button variant={plan.popular ? "default" : "outline"} className="w-full">
                  {plan.cta}
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
