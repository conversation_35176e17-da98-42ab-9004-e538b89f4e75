"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Check, X } from "lucide-react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"

export function CreditPricingTable() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly")
  const [isStudent, setIsStudent] = useState(false)
  const { toast } = useToast()

  const handleBillingCycleChange = () => {
    setBillingCycle(billingCycle === "monthly" ? "annual" : "monthly")
  }

  const handleStudentVerification = () => {
    toast({
      title: "Student Verification",
      description: "Student verification is not implemented in this demo.",
    })
  }

  const plans = [
    {
      name: "Free",
      description: "For individuals with occasional needs",
      price: {
        monthly: 0,
        annual: 0,
      },
      indiaPrice: {
        monthly: 0,
        annual: 0,
      },
      credits: 15,
      features: [
        { name: "AI Detection Credits", value: "15/month" },
        { name: "Text Volume", value: "Up to 2,000 words" },
        { name: "Basic Detection Only", value: true },
      ],
      cta: "Get Started",
      ctaLink: "/signup",
      popular: false,
    },
    {
      name: "Standard",
      description: "For educators and small institutions",
      price: {
        monthly: 14.99,
        annual: 143.9,
      },
      indiaPrice: {
        monthly: 399,
        annual: 3830,
      },
      credits: 150,
      features: [
        { name: "AI Detection Credits", value: "150/month" },
        { name: "Text Volume", value: "Up to 10,000 words" },
        { name: "Basic Humanization", value: "75/month" },
        { name: "File Uploads", value: ".txt, .pdf, .docx" },
        { name: "7-day History", value: true },
      ],
      cta: "Subscribe",
      ctaLink: "/signup?plan=standard",
      popular: true,
    },
    {
      name: "Professional",
      description: "For departments and institutions",
      price: {
        monthly: 29.99,
        annual: 287.9,
      },
      indiaPrice: {
        monthly: 799,
        annual: 7670,
      },
      credits: 600,
      features: [
        { name: "AI Detection Credits", value: "600/month" },
        { name: "Text Volume", value: "Up to 25,000 words" },
        { name: "All Humanization Tools", value: true },
        { name: "Batch Processing", value: "Up to 30 files" },
        { name: "30-day History", value: true },
      ],
      cta: "Subscribe",
      ctaLink: "/signup?plan=professional",
      popular: false,
    },
  ]

  const indiaPricing = {
    standardMonthly: 399,
    standardAnnual: 3830,
    professionalMonthly: 799,
    professionalAnnual: 7670,
  }

  return (
    <div className="space-y-8">
      <div className="flex justify-center">
        <div className="flex items-center space-x-2">
          <Label htmlFor="billing-toggle" className={billingCycle === "monthly" ? "font-medium" : ""}>
            Monthly
          </Label>
          <Switch id="billing-toggle" checked={billingCycle === "annual"} onCheckedChange={handleBillingCycleChange} />
          <div className="flex items-center gap-1.5">
            <Label htmlFor="billing-toggle" className={billingCycle === "annual" ? "font-medium" : ""}>
              Annual
            </Label>
            <Badge variant="outline" className="text-primary border-primary">
              Save 20%
            </Badge>
          </div>
        </div>
      </div>

      <div className="flex justify-center">
        <Button variant="outline" size="sm" onClick={handleStudentVerification}>
          Verify Student Status
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {plans.map((plan) => (
          <Card key={plan.name} className={`flex flex-col ${plan.popular ? "border-primary shadow-md relative" : ""}`}>
            {plan.popular && (
              <div className="absolute -top-4 left-0 right-0 flex justify-center">
                <Badge className="bg-primary hover:bg-primary">Most Popular</Badge>
              </div>
            )}
            <CardHeader className="flex flex-col space-y-1.5 pb-4">
              <h3 className="text-2xl font-bold">{plan.name}</h3>
              <p className="text-sm text-muted-foreground">{plan.description}</p>
              <div className="mt-4 flex items-baseline text-4xl font-bold">
                ${plan.price[billingCycle].toFixed(2)}
                <span className="ml-1 text-base font-medium text-muted-foreground">/month</span>
              </div>
              {billingCycle === "annual" && plan.price.annual > 0 && (
                <p className="text-sm text-muted-foreground">
                  Billed annually (${(plan.price.annual * 12).toFixed(2)})
                </p>
              )}
              {plan.indiaPrice && (
                <p className="text-sm text-muted-foreground">
                  India: ₹{plan.indiaPrice[billingCycle === "monthly" ? "monthly" : "annual"]}
                </p>
              )}
              <p className="text-sm text-muted-foreground">Includes {plan.credits} monthly credits</p>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-3">
                {plan.features.map((feature) => (
                  <li key={feature.name} className="flex items-start">
                    {feature.value === true ? (
                      <Check className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                    ) : feature.value === false ? (
                      <X className="mr-2 mt-0.5 h-4 w-4 text-muted-foreground/70" />
                    ) : (
                      <Check className="mr-2 mt-0.5 h-4 w-4 text-primary" />
                    )}
                    <div>
                      <span className="text-sm">{feature.name}</span>
                      {feature.value !== true && feature.value !== false && (
                        <span className="ml-1 text-xs text-muted-foreground">({feature.value})</span>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Link href={plan.ctaLink} className="w-full">
                <Button variant={plan.popular ? "default" : "outline"} className="w-full">
                  {plan.cta}
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  )
}
