import type React from "react";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import {
  defaultMetadata,
  generateHomePageSchema,
  generateFaqSchema,
} from "@/lib/seo-utils";
import { CreditDisplay } from "@/components/credit-display";
import { ChatWidget } from "@/components/chat-widget";
import { PromoPopup } from "@/components/promo-popup";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: defaultMetadata.title,
    template: "%s | StudentAIDetector",
  },
  description: defaultMetadata.description,
  keywords: defaultMetadata.keywords,
  authors: [{ name: "StudentAIDetector Team" }],
  creator: "StudentAIDetector",
  publisher: "StudentAIDetector",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(defaultMetadata.siteUrl),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: defaultMetadata.title,
    description: defaultMetadata.description,
    url: defaultMetadata.siteUrl,
    siteName: defaultMetadata.siteName,
    locale: "en_US",
    type: "website",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "StudentAIDetector - AI Text Detection & Humanization Tool",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: defaultMetadata.title,
    description: defaultMetadata.description,
    images: ["/og-image.jpg"],
  },
  icons: {
    icon: [
      {
        url: "/icon.png",
        href: "/icon.png",
      },
    ],
    shortcut: "/icon.png",
    apple: "/apple-icon.png",
  },
  manifest: "/site.webmanifest",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const homePageSchema = generateHomePageSchema();

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(homePageSchema),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(generateFaqSchema()),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "StudentAIDetector",
              url: defaultMetadata.siteUrl,
              logo: `${defaultMetadata.siteUrl}/logo.png`,
              sameAs: [
                "https://twitter.com/studentaidetector",
                "https://facebook.com/studentaidetector",
                "https://linkedin.com/company/studentaidetector",
              ],
            }),
          }}
        />
      </head>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {/* <CreditDisplay /> */}
          {children}
          <ChatWidget />
          <PromoPopup />
        </ThemeProvider>
      </body>
    </html>
  );
}

import "./globals.css";
