"use client";

import { useState, useEffect, useRef } from "react";
import {
  Check,
  Info,
  Sparkles,
  ArrowRight,
  ChevronRight,
  Award,
  Zap,
  Ruler,
  DollarSign,
  BarChart4,
  ChevronDown,
  Star,
  TrendingDown,
  Users,
  CreditCard,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import Image from "next/image";

export function PricingComparison() {
  const [annualBilling, setAnnualBilling] = useState(true);
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState("standard");
  const [selectedCompetitor, setSelectedCompetitor] = useState("gptzero");
  const [isCompetitorMenuOpen, setIsCompetitorMenuOpen] = useState(false);
  const comparisonRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const section = comparisonRef.current;
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  // Handle clicks outside the competitor menu to close it
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsCompetitorMenuOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuRef]);

  // Data for StudentAIDetector pricing
  const studentaiData = {
    free: {
      name: "Free",
      monthlyPrice: 0,
      yearlyPrice: 0,
      words: 5000,
      description: "Basic AI detection for individuals",
      features: ["Basic detection accuracy", "Standard processing speed"],
    },
    standard: {
      name: "Standard",
      monthlyPrice: 7.99,
      yearlyPrice: 5.59, // 30% off
      words: 50000,
      description: "For individual teachers and researchers",
      features: [
        "Advanced detection accuracy",
        "Priority processing",
        "Basic API access",
        "Detection analytics",
        "Optional AI Humanizer (+$1.99/mo)",
      ],
      popular: true,
    },
    pro: {
      name: "Pro",
      monthlyPrice: 11.99,
      yearlyPrice: 8.39, // 30% off
      words: 200000,
      description: "For educators and small departments",
      features: [
        "Highest detection accuracy",
        "Express processing",
        "Full API access",
        "Up to 5 team members",
        "Advanced analytics",
        "Optional AI Humanizer (+$1.99/mo)",
      ],
    },
  };

  // Competitor Data
  const competitors = {
    gptzero: {
      name: "GPTZero",
      logo: "G",
      color: "gray",
      signupUrl: "https://app.gptzero.me/signup",
      valueProposition: [
        {
          title: "Better Value",
          description:
            "Up to 65% lower cost per 10,000 words compared to GPTZero",
        },
        {
          title: "Higher Accuracy",
          description: "Advanced AI models with lower false positive rates",
        },
        {
          title: "Exclusive Feature",
          description:
            "AI Humanizer tool to help convert AI text into human-like writing",
        },
      ],
      plans: {
        free: {
          name: "Free",
          monthlyPrice: 0,
          yearlyPrice: 0,
          words: 10000,
          description: "Access Basic AI detection",
          features: ["Basic AI Scan", "5 Free Advanced Scans"],
        },
        essential: {
          name: "Essential",
          monthlyPrice: 14.99,
          yearlyPrice: 8.33, // $99.96 annually
          words: 150000,
          description: "More words for basic AI detection",
          features: [
            "Basic AI Scan",
            "Grammar Check",
            "AI Vocabulary Check",
            "Chrome Extension",
          ],
        },
        premium: {
          name: "Premium",
          monthlyPrice: 23.99,
          yearlyPrice: 12.99, // $155.88 annually
          words: 300000,
          description: "Access all premium features",
          features: [
            "All Essential features",
            "Advanced Scan",
            "Writing Feedback",
            "Plagiarism Check",
            "Source and Generate Citations",
          ],
          popular: true,
        },
        professional: {
          name: "Professional",
          monthlyPrice: 45.99,
          yearlyPrice: 24.99, // $299.88 annually
          words: 500000,
          description: "For teams and enterprises",
          features: [
            "All Premium features",
            "Up to 10 million words overage",
            "Scan up to 250 files at once",
            "Page by page scanning",
            "Teams Collaboration",
            "Enterprise grade security",
          ],
        },
      },
    },
    originality: {
      name: "Originality.ai",
      logo: "O",
      color: "orange",
      signupUrl: "https://originality.ai/",
      valueProposition: [
        {
          title: "More Affordable",
          description: "Subscription-based instead of expensive credits",
        },
        {
          title: "Specialized Focus",
          description:
            "Made specifically for educators with education features",
        },
        {
          title: "Better Integration",
          description:
            "Works seamlessly with LMS platforms and education workflows",
        },
      ],
      plans: {
        paygo: {
          name: "Pay-as-you-go",
          monthlyPrice: 30, // One-time
          yearlyPrice: 30,
          words: 300000, // 3000 credits × 100 words
          description: "One time on-demand pricing",
          features: [
            "AI Checker",
            "Plagiarism Checker",
            "Readability Checker",
            "Grammar & Spelling Checker",
            "30 days scan history",
          ],
          oneTime: true,
        },
        pro: {
          name: "Pro",
          monthlyPrice: 14.95,
          yearlyPrice: 12.95, // Annual pricing
          words: 200000, // 2000 credits × 100 words
          description: "For individuals and small teams",
          features: [
            "Everything in Pay-as-you-go",
            "2,000 credits per month",
            "File upload (docx, pdf, doc)",
            "Full site scans",
            "Team management",
            "Chrome extension",
          ],
          popular: true,
        },
        enterprise: {
          name: "Enterprise",
          monthlyPrice: 179,
          yearlyPrice: 136.58, // Annual pricing
          words: 1500000, // 15000 credits × 100 words
          description: "Ideal for agencies & publishers",
          features: [
            "Everything in Pro",
            "15,000 credits per month",
            "Dedicated Customer Success Manager",
            "Priority support",
            "365 days scan history",
            "API access",
          ],
        },
      },
    },
    quillbot: {
      name: "Quillbot",
      logo: "Q",
      color: "emerald",
      signupUrl: "https://quillbot.com/",
      valueProposition: [
        {
          title: "Specialized Focus",
          description: "Made specifically for AI detection, not a side feature",
        },
        {
          title: "Better Value",
          description: "Lower cost per word than Quillbot's AI detection tool",
        },
        {
          title: "Comprehensive Features",
          description:
            "Full suite of detection tools rather than a single detector",
        },
      ],
      plans: {
        free: {
          name: "Free",
          monthlyPrice: 0,
          yearlyPrice: 0,
          words: 1200,
          description: "Limited AI detection access",
          features: [
            "Basic AI detection (1,200 words)",
            "Paraphrase up to 125 words",
            "Fix basic grammar errors",
            "Basic text humanizing",
          ],
        },
        premium: {
          name: "Premium (Monthly)",
          monthlyPrice: 9.95,
          yearlyPrice: 3.33,
          words: 1000000, // Effectively unlimited
          description: "Full access to all features",
          features: [
            "Unlimited AI detection",
            "Unlimited paraphrasing",
            "Advanced grammar checks",
            "Advanced text humanizing",
            "Plagiarism prevention",
            "Custom summaries",
          ],
          popular: true,
        },
      },
    },
  };

  // Map StudentAIDetector plans to competitor plans
  const planMappings = {
    gptzero: {
      free: { studentai: "free", competitor: "free" },
      standard: { studentai: "standard", competitor: "essential" },
      pro: { studentai: "pro", competitor: "premium" },
    },
    originality: {
      free: { studentai: "free", competitor: "paygo" },
      standard: { studentai: "standard", competitor: "pro" },
      pro: { studentai: "pro", competitor: "enterprise" },
    },
    quillbot: {
      free: { studentai: "free", competitor: "free" },
      standard: { studentai: "standard", competitor: "premium" },
      pro: { studentai: "pro", competitor: "premium" },
    },
  };

  // Function to calculate the cost per 10,000 words
  const calculateCostPer10kWords = (price, words) => {
    if (price === 0 || words === 0) return 0;
    return ((price / words) * 10000).toFixed(2);
  };

  // Calculate savings percentage
  const calculateSavings = (studentaiPlan, competitorPlan) => {
    const studentaiPrice = annualBilling
      ? studentaiData[studentaiPlan].yearlyPrice
      : studentaiData[studentaiPlan].monthlyPrice;

    const competitorPrice = annualBilling
      ? competitorPlan.yearlyPrice
      : competitorPlan.monthlyPrice;

    if (studentaiPrice === 0 || competitorPrice === 0) return 0;

    return Math.round(
      ((competitorPrice - studentaiPrice) / competitorPrice) * 100
    );
  };

  // Get current comparison data based on selected competitor and active tab
  const currentComparison = planMappings[selectedCompetitor][activeTab];
  const studentaiPlan = studentaiData[currentComparison.studentai];
  const competitor = competitors[selectedCompetitor];
  const competitorPlan = competitor.plans[currentComparison.competitor];

  const studentaiPrice = annualBilling
    ? studentaiPlan.yearlyPrice
    : studentaiPlan.monthlyPrice;
  const competitorPrice = annualBilling
    ? competitorPlan.yearlyPrice
    : competitorPlan.monthlyPrice;

  const studentaiCostPer10k = calculateCostPer10kWords(
    studentaiPrice,
    studentaiPlan.words
  );
  const competitorCostPer10k = calculateCostPer10kWords(
    competitorPrice,
    competitorPlan.words
  );

  const savings = calculateSavings(currentComparison.studentai, competitorPlan);

  // Available competitors for selection dropdown
  const availableCompetitors = [
    { id: "gptzero", name: "GPTZero", color: "gray" },
    { id: "originality", name: "Originality.ai", color: "orange" },
    { id: "quillbot", name: "Quillbot", color: "emerald" },
  ];

  return (
    <section
      ref={comparisonRef}
      className="w-full py-16 overflow-hidden relative"
    >
      {/* Background decorative elements */}
      <div className="absolute top-0 left-1/3 w-96 h-96 bg-gradient-to-r from-blue-200/30 to-purple-200/30 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-0 right-1/3 w-80 h-80 bg-gradient-to-r from-green-200/30 to-blue-200/30 dark:from-green-900/20 dark:to-blue-900/20 rounded-full blur-3xl -z-10"></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center mb-3 px-4 py-1.5 bg-blue-100/80 dark:bg-blue-900/30 rounded-full border border-blue-200 dark:border-blue-800/50"
          >
            <BarChart4 className="w-4 h-4 text-blue-600 dark:text-blue-400 mr-2" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Side-by-Side Comparison
            </span>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              StudentAIDetector vs{" "}
              <span
                className={`relative inline-block ${
                  selectedCompetitor === "originality"
                    ? "text-orange-600 dark:text-orange-400"
                    : "text-gray-800 dark:text-gray-200"
                }`}
              >
                <span>{competitor.name}</span>
                <div className="absolute -bottom-1 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full opacity-70"></div>
              </span>
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-4"
          >
            <p>
              See why StudentAIDetector offers better accuracy, more features,
              and up to{" "}
              <span className="text-blue-600 dark:text-blue-400 font-semibold">
                {savings > 0
                  ? `${savings}% cost savings`
                  : "significant advantages"}
              </span>{" "}
              compared to {competitor.name}
            </p>
          </motion.div>

          {/* Competitor selector dropdown */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.25 }}
            className="inline-block mt-4 mb-8 relative"
          >
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              Compare with:
            </div>

            <div ref={menuRef} className="relative inline-block text-left">
              <button
                onClick={() => setIsCompetitorMenuOpen(!isCompetitorMenuOpen)}
                className="flex items-center justify-between min-w-[180px] rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-750 text-gray-700 dark:text-gray-300"
              >
                <div className="flex items-center">
                  <div
                    className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                      selectedCompetitor === "originality"
                        ? "bg-orange-100 dark:bg-orange-900/30 text-orange-500 dark:text-orange-400"
                        : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                    }`}
                  >
                    <span className="text-xs font-medium">
                      {competitor.logo}
                    </span>
                  </div>
                  <span className="font-medium">{competitor.name}</span>
                </div>
                <ChevronDown
                  className={`ml-2 w-4 h-4 transform transition-transform duration-200 ${
                    isCompetitorMenuOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              <AnimatePresence>
                {isCompetitorMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-0 left-0 mt-2 origin-top rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 z-30"
                  >
                    <div className="py-1">
                      {availableCompetitors.map((comp) => (
                        <button
                          key={comp.id}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-750 flex items-center ${
                            selectedCompetitor === comp.id
                              ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                              : "text-gray-700 dark:text-gray-300"
                          }`}
                          onClick={() => {
                            setSelectedCompetitor(comp.id);
                            setIsCompetitorMenuOpen(false);
                            // Reset to standard tab when changing competitor
                            setActiveTab("standard");
                          }}
                        >
                          <div
                            className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${
                              comp.id === "originality"
                                ? "bg-orange-100 dark:bg-orange-900/30 text-orange-500 dark:text-orange-400"
                                : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                            }`}
                          >
                            <span className="text-xs font-medium">
                              {comp.id === "gptzero" ? "G" : "O"}
                            </span>
                          </div>
                          {comp.name}
                          {selectedCompetitor === comp.id && (
                            <Check className="ml-auto w-4 h-4 text-blue-500" />
                          )}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>

        {/* Billing toggle */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="max-w-md mx-auto mb-12"
        >
          <div className="flex items-center justify-center p-2 bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
            <div
              className={`flex-1 cursor-pointer text-center py-2 px-3 rounded ${
                !annualBilling
                  ? "bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300"
                  : ""
              }`}
              onClick={() => setAnnualBilling(false)}
            >
              Monthly
            </div>
            <div
              className={`flex-1 cursor-pointer text-center py-2 px-3 rounded ${
                annualBilling
                  ? "bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300"
                  : ""
              }`}
              onClick={() => setAnnualBilling(true)}
            >
              <div className="flex items-center justify-center">
                Annual
                <span className="ml-2 inline-flex items-center px-2 py-0.5 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300 rounded-full">
                  Save 30%
                </span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="flex justify-center mb-10"
        >
          <div className="inline-flex p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
            {Object.keys(planMappings[selectedCompetitor]).map((tab) => (
              <button
                key={tab}
                className={`px-6 py-2 text-sm font-medium rounded-md transition-all ${
                  activeTab === tab
                    ? "bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm"
                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100"
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Main Comparison Card */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`${selectedCompetitor}-${activeTab}-${annualBilling}`}
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.98 }}
            transition={{ duration: 0.4 }}
            className="mb-8"
          >
            <div className="bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-xl border border-gray-200 dark:border-gray-700">
              {/* Header */}
              <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-800">
                <div className="col-span-3 p-6">
                  <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                    Features & Pricing
                  </h3>
                </div>
                <div className="col-span-2 p-6 bg-blue-50 dark:bg-blue-900/20 border-x border-gray-200 dark:border-gray-800 relative">
                  {studentaiPlan.popular && (
                    <div className="absolute top-0 inset-x-0 h-1.5 bg-blue-500"></div>
                  )}
                  <div className="flex items-center">
                    <Image
                      src="/icon.png"
                      alt="StudentAIDetector"
                      width={28}
                      height={28}
                      className="mr-3"
                    />
                    <div>
                      <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                        StudentAIDetector
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {studentaiPlan.name}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="col-span-2 p-6 relative">
                  {competitorPlan.popular && (
                    <div
                      className={`absolute top-0 inset-x-0 h-1.5 ${
                        selectedCompetitor === "originality"
                          ? "bg-orange-500"
                          : "bg-gray-500"
                      }`}
                    ></div>
                  )}
                  <div className="flex items-center">
                    <div
                      className={`w-7 h-7 rounded-full ${
                        selectedCompetitor === "originality"
                          ? "bg-orange-100 dark:bg-orange-900/30"
                          : "bg-gray-200 dark:bg-gray-700"
                      } flex items-center justify-center mr-3`}
                    >
                      <span
                        className={`font-bold ${
                          selectedCompetitor === "originality"
                            ? "text-orange-600 dark:text-orange-400"
                            : "text-gray-600 dark:text-gray-300"
                        }`}
                      >
                        {competitor.logo}
                      </span>
                    </div>
                    <div>
                      <h3
                        className={`text-lg font-semibold ${
                          selectedCompetitor === "originality"
                            ? "text-orange-600 dark:text-orange-400"
                            : "text-gray-600 dark:text-gray-400"
                        }`}
                      >
                        {competitor.name}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {competitorPlan.name}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pricing Row */}
              <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-800">
                <div className="col-span-3 p-6 flex items-center">
                  <DollarSign className="w-5 h-5 text-gray-400 mr-3" />
                  <span className="text-gray-800 dark:text-gray-200 font-medium">
                    {competitorPlan.oneTime ? "Price" : "Monthly Price"}
                  </span>
                </div>
                <div className="col-span-2 p-6 bg-blue-50 dark:bg-blue-900/20 border-x border-gray-200 dark:border-gray-800">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    $
                    {annualBilling
                      ? studentaiPlan.yearlyPrice
                      : studentaiPlan.monthlyPrice}
                    <span className="text-sm font-normal text-gray-500 dark:text-gray-400 ml-1">
                      /mo
                    </span>
                  </div>
                  {annualBilling && studentaiPlan.monthlyPrice > 0 && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1 flex items-center">
                      <span className="line-through">
                        ${studentaiPlan.monthlyPrice}/mo
                      </span>
                      <Badge className="ml-2 bg-green-100 text-green-800 border-0 dark:bg-green-900/40 dark:text-green-300">
                        Save 30%
                      </Badge>
                    </div>
                  )}
                </div>
                <div className="col-span-2 p-6">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    ${competitorPrice}
                    <span className="text-sm font-normal text-gray-500 dark:text-gray-400 ml-1">
                      {competitorPlan.oneTime ? " one-time" : "/mo"}
                    </span>
                  </div>
                  {annualBilling &&
                    !competitorPlan.oneTime &&
                    competitorPlan.monthlyPrice >
                      competitorPlan.yearlyPrice && (
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        <span className="line-through">
                          ${competitorPlan.monthlyPrice}/mo
                        </span>
                      </div>
                    )}
                  {competitorPlan.oneTime && (
                    <div className="text-xs text-amber-600 dark:text-amber-400 mt-1 flex items-center">
                      <CreditCard className="h-3 w-3 mr-1" />
                      One-time payment
                    </div>
                  )}
                </div>
              </div>

              {/* Words/Credits Allocation Row */}
              <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-800">
                <div className="col-span-3 p-6 flex items-center">
                  <Ruler className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <span className="text-gray-800 dark:text-gray-200 font-medium">
                      {selectedCompetitor === "originality"
                        ? "Monthly Usage"
                        : "Monthly Word Limit"}
                    </span>
                    {selectedCompetitor === "originality" && (
                      <p className="text-xs text-gray-500 mt-1">
                        1 credit = 100 words
                      </p>
                    )}
                  </div>
                </div>
                <div className="col-span-2 p-6 bg-blue-50 dark:bg-blue-900/20 border-x border-gray-200 dark:border-gray-800">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {studentaiPlan.words.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    words per month
                  </div>
                </div>
                <div className="col-span-2 p-6">
                  <div className="text-lg font-semibold text-gray-900 dark:text-white">
                    {selectedCompetitor === "originality"
                      ? `${(
                          competitorPlan.words / 100
                        ).toLocaleString()} credits`
                      : competitorPlan.words.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedCompetitor === "originality"
                      ? `${competitorPlan.words.toLocaleString()} words`
                      : "words per month"}
                    {competitorPlan.oneTime && " (one-time)"}
                  </div>
                </div>
              </div>

              {/* Value Comparison Row */}
              <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-800">
                <div className="col-span-3 p-6 flex items-center">
                  <Award className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <span className="text-gray-800 dark:text-gray-200 font-medium">
                      Value (Cost per 10k words)
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Lower is better
                    </p>
                  </div>
                </div>
                <div className="col-span-2 p-6 bg-blue-50 dark:bg-blue-900/20 border-x border-gray-200 dark:border-gray-800">
                  <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                    ${studentaiCostPer10k}
                  </div>
                  {savings > 0 && (
                    <Badge className="bg-green-100 text-green-800 border-0 dark:bg-green-900/40 dark:text-green-300">
                      {savings}% cheaper
                    </Badge>
                  )}
                </div>
                <div className="col-span-2 p-6">
                  <div className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                    ${competitorCostPer10k}
                  </div>
                  {competitorPlan.oneTime && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      One-time purchase
                    </div>
                  )}
                </div>
              </div>

              {/* Payment Model Row - Only show for Originality comparison */}
              {selectedCompetitor === "originality" && (
                <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-800">
                  <div className="col-span-3 p-6 flex items-center">
                    <CreditCard className="w-5 h-5 text-gray-400 mr-3" />
                    <div>
                      <span className="text-gray-800 dark:text-gray-200 font-medium">
                        Payment Model
                      </span>
                    </div>
                  </div>
                  <div className="col-span-2 p-6 bg-blue-50 dark:bg-blue-900/20 border-x border-gray-200 dark:border-gray-800">
                    <div className="flex items-center">
                      <Badge className="bg-blue-100 text-blue-700 border-0 dark:bg-blue-900/40 dark:text-blue-300">
                        Subscription
                      </Badge>
                      <TrendingDown className="ml-2 h-4 w-4 text-green-500" />
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      Predictable monthly cost
                    </div>
                  </div>
                  <div className="col-span-2 p-6">
                    <div className="flex items-center">
                      <Badge
                        className={`${
                          competitorPlan.oneTime
                            ? "bg-amber-100 text-amber-700 border-0 dark:bg-amber-900/40 dark:text-amber-300"
                            : "bg-blue-100 text-blue-700 border-0 dark:bg-blue-900/40 dark:text-blue-300"
                        }`}
                      >
                        {competitorPlan.oneTime
                          ? "Credits"
                          : "Credits Subscription"}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {competitorPlan.oneTime
                        ? "Purchase credits as needed"
                        : "Monthly credit allowance"}
                    </div>
                  </div>
                </div>
              )}

              {/* Key Features Comparison */}
              <div className="grid grid-cols-7 border-b border-gray-200 dark:border-gray-800">
                <div className="col-span-3 p-6 flex items-start">
                  <Sparkles className="w-5 h-5 text-gray-400 mr-3 mt-1" />
                  <div>
                    <span className="text-gray-800 dark:text-gray-200 font-medium">
                      Key Features
                    </span>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      What's included in this plan
                    </p>
                  </div>
                </div>
                <div className="col-span-2 p-6 bg-blue-50 dark:bg-blue-900/20 border-x border-gray-200 dark:border-gray-800">
                  <ul className="space-y-3">
                    {studentaiPlan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {feature}
                        </span>
                      </li>
                    ))}
                    {currentComparison.studentai !== "free" && (
                      <li className="flex items-start">
                        <Zap className="w-5 h-5 text-purple-500 mr-2 flex-shrink-0" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          <span className="font-medium text-purple-600 dark:text-purple-400">
                            Exclusive:
                          </span>{" "}
                          AI Humanizer available
                        </span>
                      </li>
                    )}
                  </ul>
                </div>
                <div className="col-span-2 p-6">
                  <ul className="space-y-3">
                    {competitorPlan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check
                          className={`w-5 h-5 ${
                            selectedCompetitor === "originality"
                              ? "text-orange-400"
                              : "text-gray-400"
                          } mr-2 flex-shrink-0`}
                        />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* CTA Row */}
              <div className="grid grid-cols-7">
                <div className="col-span-3 p-6 flex items-center">
                  <span className="text-gray-800 dark:text-gray-200 font-medium">
                    Choose your plan
                  </span>
                </div>
                <div className="col-span-2 p-6 bg-blue-50 dark:bg-blue-900/20 border-x border-gray-200 dark:border-gray-800">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">
                    <Link
                      href="https://app.studentaidetector.com/signup"
                      className="flex items-center justify-center"
                    >
                      Get Started
                      <ArrowRight className="ml-2 w-4 h-4" />
                    </Link>
                  </Button>
                </div>
                <div className="col-span-2 p-6">
                  <Button variant="outline" className="w-full">
                    <Link
                      href={competitor.signupUrl}
                      className="flex items-center justify-center"
                    >
                      Visit {competitor.name}
                      <ChevronRight className="ml-1 w-4 h-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Summary Card */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`summary-${selectedCompetitor}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="mt-12 max-w-3xl mx-auto"
          >
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-xl p-6 border border-blue-100 dark:border-blue-900/30 shadow-sm">
              <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-300 mb-3 flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Why choose StudentAIDetector over {competitor.name}?
              </h3>
              <ul className="space-y-3 mt-4">
                {competitor.valueProposition.map((item, index) => (
                  <li key={index} className="flex items-start">
                    <div className="bg-blue-100 dark:bg-blue-800/50 rounded-full p-1 mr-3 flex-shrink-0">
                      <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <span className="font-medium text-gray-800 dark:text-gray-200">
                        {item.title}:
                      </span>
                      <span className="text-gray-700 dark:text-gray-300 ml-1">
                        {item.description}
                      </span>
                    </div>
                  </li>
                ))}
              </ul>

              <div className="mt-5">
                <Link
                  href={`/compare/studentaidetector-vs-${selectedCompetitor}`}
                >
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    See Full Comparison
                    <ArrowRight className="ml-2 w-4 h-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Animation Styles */}
        <style jsx global>{`
          @keyframes float {
            0% {
              transform: translateY(0px);
            }
            50% {
              transform: translateY(-10px);
            }
            100% {
              transform: translateY(0px);
            }
          }

          @keyframes pulse {
            0% {
              transform: scale(1);
              opacity: 0.8;
            }
            50% {
              transform: scale(1.05);
              opacity: 1;
            }
            100% {
              transform: scale(1);
              opacity: 0.8;
            }
          }

          .animate-float {
            animation: float 3s ease-in-out infinite;
          }

          .animate-pulse-slow {
            animation: pulse 4s ease-in-out infinite;
          }
        `}</style>
      </div>
    </section>
  );
}
