import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@/components/ui/accordion"

export function Faq() {
  const faqs = [
    {
      question: "How accurate is StudentAIDetector?",
      answer:
        "Our AI detection algorithms are continuously improved and currently achieve over 90% accuracy in identifying AI-generated content from major models like ChatGPT, GPT-4, Claude, and others. We use a multi-layered approach that analyzes patterns, linguistic features, and contextual elements to provide reliable results.",
    },
    {
      question: "Can StudentAIDetector detect all AI-written content?",
      answer:
        "While we strive for the highest accuracy, no AI detector is 100% perfect. Our system is regularly updated to recognize content from the latest AI models. We focus on minimizing both false positives (incorrectly flagging human content as AI) and false negatives (missing AI-generated content).",
    },
    {
      question: "How does the AI humanizer work?",
      answer:
        "Our AI humanizer uses advanced natural language processing to rewrite AI-generated text to sound more natural and human-like, while maintaining the original meaning. It adjusts sentence structure, vocabulary, and writing style to create more authentic content that better reflects human writing patterns.",
    },
    {
      question: "Is using the humanizer tool ethical?",
      answer:
        "The humanizer tool is designed to help users improve their writing and develop a more natural style. We encourage ethical use, such as refining AI-assisted drafts to better match your voice or improving content quality. Users should always follow their institution's academic integrity policies and properly cite sources.",
    },
    {
      question: "Can I use StudentAIDetector for free?",
      answer:
        "Yes, we offer a free plan that includes 10 AI detection queries per month, up to 1,500 words per query, and 3 basic humanization tool uses. This allows you to try our service before committing to a paid plan.",
    },
    {
      question: "What file formats does StudentAIDetector support?",
      answer:
        "Our free plan supports text input only. The Basic plan supports .txt, .pdf, and .docx files, while the Pro plan supports all common document formats. This makes it easy to analyze content regardless of how it's formatted.",
    },
  ]

  return (
    <section className="container py-12 md:py-16">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold tracking-tight mb-2">Frequently Asked Questions</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Find answers to common questions about our AI detection and humanization tools
        </p>
      </div>

      <div className="max-w-3xl mx-auto">
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
              <AccordionContent>{faq.answer}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  )
}
