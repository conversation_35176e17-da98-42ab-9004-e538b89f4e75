import type { <PERSON>ada<PERSON> } from "next";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  FileText,
  Shield,
  Zap,
  AlertTriangle,
  Info,
  ArrowRight,
  BarChart3,
  Database,
  BrainCircuit,
} from "lucide-react";
import { TextAnalyzer } from "@/components/text-analyzer";
import { AudienceCards } from "@/components/audience-cards";
import { Testimonials } from "@/components/home/<USER>";
import { Faq } from "@/components/tools/ai-detector-faq";
import { Breadcrumb } from "@/components/breadcrumb";
import { generateMetaTags, generateToolSchema } from "@/lib/seo-utils";
import Link from "next/link";
import Image from "next/image";

export const metadata: Metadata = generateMetaTags({
  title: "AI Detector Tool - Identify AI-Generated Content with 99% Accuracy",
  description:
    "Our advanced AI content detector identifies text written by <PERSON><PERSON><PERSON><PERSON>, GPT-4, Claude and other AI models with 99% accuracy. Perfect for educators verifying student work and content professionals.",
  keywords: [
    "AI detector",
    "AI content detector",
    "ChatGPT detector",
    "GPT-4 detector",
    "AI text checker",
    "detect AI writing",
    "AI detection tool",
    "AI content identification",
    "academic integrity tool",
    "AI writing detector",
  ],
  canonical: "/tools/ai-detector",
});

export default function AIDetectorPage() {
  const toolSchema = generateToolSchema(
    "AI Detector Tool",
    "Identify AI-generated content with 99% accuracy. Our advanced algorithm detects text written by ChatGPT, GPT-4, Claude, and other AI models.",
    "https://studentaidetector.com/tools/ai-detector"
  );

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-8">
        <Breadcrumb />
      </div>

      {/* Hero Section */}
      <section className="bg-muted/50 py-12 md:py-20">
        <div className="container px-4 md:px-6">
          <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
            <div className="flex flex-col justify-center space-y-4">
              <div className="space-y-2">
                <Badge variant="outline" className="mb-2">
                  AI Detection Technology
                </Badge>
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Industry-Leading AI Content Detector
                </h1>
                <p className="text-xl text-muted-foreground">
                  Identify AI-written content with 99% accuracy
                </p>
              </div>
              <p className="text-muted-foreground">
                Our proprietary multi-layered AI detection technology identifies
                content created by ChatGPT, GPT-4, Claude, Bard, and other AI
                models with unmatched precision. Designed specifically for
                educators verifying academic integrity, content managers
                ensuring quality, and organizations maintaining content
                authenticity standards.
              </p>
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-4">
                <Button size="lg" className="w-full sm:w-auto">
                  Try AI Detector Now
                </Button>
                <Link href="/pricing">
                  <Button
                    variant="outline"
                    size="lg"
                    className="w-full sm:w-auto"
                  >
                    View Pricing
                  </Button>
                </Link>
              </div>
            </div>
            <div className="mx-auto lg:ml-auto flex items-center justify-center">
              <Card className="w-full max-w-md">
                <CardHeader>
                  <CardTitle>Quick AI Detection</CardTitle>
                  <CardDescription>
                    Paste text to check if it was written by AI
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <TextAnalyzer />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Accuracy Metrics Section - NEW */}
      <section className="py-12 bg-background">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Industry-Leading Accuracy
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our AI detection technology has been validated through extensive
              testing and independent verification
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="mx-auto bg-primary/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-2">
                  <BarChart3 className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">99%</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Detection accuracy for GPT-4 generated content
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="mx-auto bg-primary/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-2">
                  <CheckCircle className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">98%</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Detection accuracy for ChatGPT content
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="mx-auto bg-primary/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-2">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">97%</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Detection accuracy for Claude and other AI models
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader className="pb-2">
                <div className="mx-auto bg-primary/10 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-2">
                  <Database className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-4xl font-bold">1M+</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Documents analyzed in our training dataset
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8 text-center">
            <p className="text-sm text-muted-foreground">
              *Accuracy rates based on controlled testing environments with
              diverse text samples across multiple domains
            </p>
          </div>
        </div>
      </section>

      {/* Technology Explained Section - ENHANCED */}
      <section className="py-12 md:py-16 bg-muted/50">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Our Detection Technology Explained
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              How our proprietary multi-layered detection system identifies
              AI-generated content
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            <div className="bg-background rounded-lg p-6 shadow-sm">
              <div className="mb-4">
                <h3 className="text-xl font-bold mb-2 flex items-center">
                  <BrainCircuit className="mr-2 h-5 w-5 text-primary" />
                  Advanced Neural Network Analysis
                </h3>
                <p className="text-muted-foreground">
                  Our proprietary neural network has been trained on over 1
                  million documents, learning to identify subtle patterns unique
                  to different AI writing systems. This deep learning approach
                  enables:
                </p>
              </div>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Recognition of AI-specific word choice patterns and
                    statistical distributions
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Model-specific identification (differentiating between
                    GPT-4, Claude, etc.)
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Continuous learning and adaptation to new AI writing models
                  </span>
                </li>
              </ul>
            </div>

            <div className="bg-background rounded-lg p-6 shadow-sm">
              <div className="mb-4">
                <h3 className="text-xl font-bold mb-2 flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  Advanced Linguistic Analysis
                </h3>
                <p className="text-muted-foreground">
                  Beyond statistical patterns, our system performs deep
                  linguistic analysis to identify elements characteristic of
                  AI-generated text:
                </p>
              </div>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Syntactic structure homogeneity typically present in AI
                    writing
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Repetitive phrase constructions and predictable transitions
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Analysis of discourse markers that differ between human and
                    AI writers
                  </span>
                </li>
              </ul>
            </div>

            <div className="bg-background rounded-lg p-6 shadow-sm">
              <div className="mb-4">
                <h3 className="text-xl font-bold mb-2 flex items-center">
                  <Zap className="mr-2 h-5 w-5 text-primary" />
                  Token-Level Probabilistic Analysis
                </h3>
                <p className="text-muted-foreground">
                  Our system examines content at the token level, evaluating the
                  probability distribution of each word choice to identify AI
                  patterns:
                </p>
              </div>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Identification of token sequences with unnaturally high
                    predictability
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Detection of entropy patterns characteristic of language
                    model outputs
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Analysis of perplexity metrics that differ between human and
                    AI writers
                  </span>
                </li>
              </ul>
            </div>

            <div className="bg-background rounded-lg p-6 shadow-sm">
              <div className="mb-4">
                <h3 className="text-xl font-bold mb-2 flex items-center">
                  <CheckCircle className="mr-2 h-5 w-5 text-primary" />
                  Cross-Verification System
                </h3>
                <p className="text-muted-foreground">
                  Our detection technology employs multiple independent
                  detection methods that cross-verify results:
                </p>
              </div>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Parallel processing through multiple detection algorithms
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Weighted consensus mechanism to reduce false positives
                  </span>
                </li>
                <li className="flex items-start">
                  <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                  <span>
                    Machine learning-based adjustment to confidence thresholds
                    based on text domain
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section - ENHANCED */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Advanced Detection Features
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Comprehensive tools to identify and analyze AI-generated content
              with precision
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-2">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-primary/10">
                    <AlertTriangle className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">
                      AI Probability Scoring
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Our proprietary algorithm provides a precise percentage
                      indicating how likely the content was generated by AI,
                      with model-specific detection capabilities.
                    </p>
                    <div className="bg-muted p-3 rounded-md mb-4">
                      <div className="space-y-2">
                        <div className="flex justify-between items-center text-sm">
                          <span>Overall AI probability:</span>
                          <span className="font-medium">
                            94% - Very likely AI
                          </span>
                        </div>
                        <div className="w-full bg-background rounded-full h-2.5">
                          <div
                            className="bg-primary h-2.5 rounded-full"
                            style={{ width: "94%" }}
                          ></div>
                        </div>
                        <div className="flex justify-between items-center text-xs text-muted-foreground">
                          <span>Most likely model: GPT-4</span>
                          <span>Confidence: High</span>
                        </div>
                      </div>
                    </div>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Individual paragraph-level analysis
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          AI model identification (GPT-3.5, GPT-4, Claude, etc.)
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Confidence rating with statistical basis
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-primary/10">
                    <Info className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">
                      Advanced Section Highlighting
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Our system precisely identifies which specific parts of
                      the text are most likely AI-generated, with granular
                      analysis capabilities down to the sentence level.
                    </p>
                    <div className="bg-muted p-3 rounded-md mb-4 text-sm">
                      <p>
                        I wrote this introduction myself.{" "}
                        <span className="bg-red-100 dark:bg-red-900/30 px-1 rounded">
                          The subsequent analysis of the economic impact
                          suggests that inflation will likely remain within the
                          target range of 2-3% for the next fiscal quarter,
                          though external factors could potentially introduce
                          volatility into market conditions.
                        </span>{" "}
                        I also wrote this conclusion.
                      </p>
                    </div>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Heat map visualization of AI probability
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Sentence-level analysis for mixed content
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Detailed explanation of detection signals
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-primary/10">
                    <FileText className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">
                      Comprehensive Detection Reports
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Generate detailed, exportable reports with comprehensive
                      analysis of AI content detection for documentation,
                      sharing, and record-keeping.
                    </p>
                    <div className="bg-muted rounded-md p-3 mb-4">
                      <div className="space-y-1.5">
                        <div className="flex justify-between text-sm">
                          <span>Document ID:</span>
                          <span className="font-mono text-xs">
                            AID-2023110742
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Analysis timestamp:</span>
                          <span className="font-mono text-xs">
                            2023-11-07 14:42:18 UTC
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Overall result:</span>
                          <span className="text-red-500 font-medium">
                            94% AI probability
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground pt-1">
                          Download report as: PDF | CSV | JSON
                        </div>
                      </div>
                    </div>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Tamper-proof verification with hash codes
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Detailed methodology explanation and findings
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Expert analysis recommendations
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-primary/10">
                    <Zap className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">
                      Enterprise-Grade Batch Processing
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Process hundreds of documents simultaneously with our
                      high-throughput API and batch upload system, designed for
                      educational institutions and content platforms.
                    </p>
                    <div className="bg-muted rounded-md p-3 mb-4 flex items-center justify-between">
                      <div className="text-sm">
                        <div className="font-medium">Batch #23587</div>
                        <div className="text-muted-foreground text-xs">
                          42 files processed
                        </div>
                      </div>
                      <div className="text-right text-sm">
                        <div className="font-medium text-amber-500">
                          24 potential AI detected
                        </div>
                        <div className="text-muted-foreground text-xs">
                          Summary report ready
                        </div>
                      </div>
                    </div>
                    <ul className="space-y-2">
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Support for multiple file formats (DOCX, PDF, TXT)
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          Mass analysis with aggregated reporting
                        </span>
                      </li>
                      <li className="flex items-start">
                        <div className="mr-2 mt-1.5 h-1.5 w-1.5 rounded-full bg-primary" />
                        <span className="text-sm">
                          LMS integration via API or plugins
                        </span>
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Educator Testimonials Section - NEW */}
      <section className="bg-muted/50 py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              What Educators Say
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Trusted by professors, teachers, and academic institutions
              worldwide
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card className="border-primary/10">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4 mb-4">
                  <div className="rounded-full bg-muted w-10 h-10 flex items-center justify-center overflow-hidden">
                    <Image
                      src="/placeholder.svg?height=40&width=40"
                      alt="Dr. Sarah Johnson"
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium">Dr. Sarah Johnson</h3>
                    <p className="text-sm text-muted-foreground">
                      Professor of English, Stanford University
                    </p>
                  </div>
                </div>
                <blockquote className="border-l-2 pl-4 italic text-muted-foreground">
                  "This AI detector has transformed how I evaluate student
                  papers. Its accuracy is remarkable—it identified patterns I
                  couldn't see myself. Now I can focus on teaching students how
                  to properly use AI as a tool rather than playing detective."
                </blockquote>
                <div className="flex mt-4 text-amber-500">
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-primary/10">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4 mb-4">
                  <div className="rounded-full bg-muted w-10 h-10 flex items-center justify-center overflow-hidden">
                    <Image
                      src="/placeholder.svg?height=40&width=40"
                      alt="Prof. Michael Chen"
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium">Prof. Michael Chen</h3>
                    <p className="text-sm text-muted-foreground">
                      Department Chair, Computer Science, MIT
                    </p>
                  </div>
                </div>
                <blockquote className="border-l-2 pl-4 italic text-muted-foreground">
                  "We've implemented StudentAIDetector across our department
                  with excellent results. The section highlighting feature is
                  invaluable—it shows exactly which parts of a submission are
                  AI-generated, which helps facilitate constructive
                  conversations with students."
                </blockquote>
                <div className="flex mt-4 text-amber-500">
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-primary/10">
              <CardContent className="pt-6">
                <div className="flex items-start gap-4 mb-4">
                  <div className="rounded-full bg-muted w-10 h-10 flex items-center justify-center overflow-hidden">
                    <Image
                      src="/placeholder.svg?height=40&width=40"
                      alt="Dr. Rebecca Martinez"
                      width={40}
                      height={40}
                      className="rounded-full"
                    />
                  </div>
                  <div>
                    <h3 className="font-medium">Dr. Rebecca Martinez</h3>
                    <p className="text-sm text-muted-foreground">
                      Academic Integrity Officer, University of Chicago
                    </p>
                  </div>
                </div>
                <blockquote className="border-l-2 pl-4 italic text-muted-foreground">
                  "After evaluating several AI detectors, we found
                  StudentAIDetector to be significantly more accurate. The
                  detailed reports have been crucial in our academic integrity
                  investigations, providing clear evidence that stands up to
                  scrutiny in academic hearings."
                </blockquote>
                <div className="flex mt-4 text-amber-500">
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                  <CheckCircle className="h-5 w-5" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8 text-center">
            <Link href="/testimonials">
              <Button variant="outline">
                View More Educator Testimonials{" "}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Try It Now Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Try Our AI Detector
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Paste your text below to check if it was written by AI
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <TextAnalyzer />
          </div>
        </div>
      </section>

      {/* Audience Section */}
      <section className="bg-muted/50 py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Who Uses Our AI Detector?
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our AI detection tools serve multiple audiences with different
              needs
            </p>
          </div>

          <AudienceCards />
        </div>
      </section>

      {/* Related Tools Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Related Tools
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Explore our other tools to enhance your content and workflow
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>Humanization Tools</CardTitle>
                <CardDescription>
                  Transform AI-generated text to sound more natural and
                  human-like
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <p className="text-muted-foreground mb-4">
                  Our humanization tools help you improve AI-written content to
                  make it sound more natural while maintaining your original
                  meaning.
                </p>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/tools/humanization">
                  <Button variant="outline" className="w-full">
                    <span>Explore Humanization Tools</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>

            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>Batch Processing</CardTitle>
                <CardDescription>
                  Analyze multiple documents at once for efficiency
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <p className="text-muted-foreground mb-4">
                  Process multiple documents simultaneously to save time and
                  increase your productivity with our batch processing tool.
                </p>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/tools/batch-processing">
                  <Button variant="outline" className="w-full">
                    <span>Learn About Batch Processing</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>

            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>API Access</CardTitle>
                <CardDescription>
                  Integrate AI detection into your own applications
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <p className="text-muted-foreground mb-4">
                  Access our AI detection capabilities programmatically through
                  our API to build custom integrations for your workflow.
                </p>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/tools/api">
                  <Button variant="outline" className="w-full">
                    <span>Discover API Options</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="bg-muted/50 py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <Testimonials />
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16 bg-primary text-primary-foreground">
        <div className="container px-4 md:px-6 text-center">
          <h2 className="text-3xl font-bold tracking-tight mb-4">
            Ready to Detect AI-Generated Content?
          </h2>
          <p className="text-xl mb-6 max-w-2xl mx-auto opacity-90">
            Start using our advanced AI detector today and ensure content
            authenticity
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              Try For Free
            </Button>
            <Link href="/pricing">
              <Button
                size="lg"
                variant="outline"
                className="bg-transparent text-primary-foreground border-primary-foreground hover:bg-primary-foreground/10"
              >
                View Pricing Plans
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <Faq />
        </div>
      </section>

      <Footer />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(toolSchema),
        }}
      />
    </main>
  );
}
