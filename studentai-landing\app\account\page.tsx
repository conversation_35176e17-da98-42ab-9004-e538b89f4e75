"use client"

import { Progress } from "@/components/ui/progress"

import type React from "react"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { CreditCard, User, Settings } from "lucide-react"
import Link from "next/link"

export default function AccountPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("profile")
  const [isLoading, setIsLoading] = useState(true)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    // In a real app, this would fetch user data from an API
    const userData = localStorage.getItem("user")
    if (!userData) {
      router.push("/login")
      return
    }

    setUser(JSON.parse(userData))
    setIsLoading(false)
  }, [router])

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  useEffect(() => {
    if (user) {
      setFormData((prev) => ({
        ...prev,
        name: user.name || "",
        email: user.email || "",
      }))
    }
  }, [user])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleUpdateProfile = (e: React.FormEvent) => {
    e.preventDefault()

    // In a real app, this would call an API to update the user's profile
    const updatedUser = {
      ...user,
      name: formData.name,
      email: formData.email,
    }

    localStorage.setItem("user", JSON.stringify(updatedUser))
    setUser(updatedUser)

    toast({
      title: "Profile updated",
      description: "Your profile has been updated successfully.",
    })
  }

  const handleUpdatePassword = (e: React.FormEvent) => {
    e.preventDefault()

    if (formData.newPassword !== formData.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation must match.",
        variant: "destructive",
      })
      return
    }

    // In a real app, this would call an API to update the user's password
    toast({
      title: "Password updated",
      description: "Your password has been updated successfully.",
    })

    setFormData((prev) => ({
      ...prev,
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    }))
  }

  if (isLoading) {
    return (
      <main className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container py-12 flex-1 flex items-center justify-center">
          <p>Loading...</p>
        </div>
        <Footer />
      </main>
    )
  }

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container py-12">
        <div className="flex flex-col gap-6 max-w-3xl mx-auto">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold">Account Settings</h1>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="capitalize">
                {user.plan} Plan
              </Badge>
              <Link href="/account/subscription">
                <Button variant="outline" size="sm">
                  Upgrade
                </Button>
              </Link>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="subscription" className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Subscription
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>Update your account information</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleUpdateProfile} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Name</Label>
                      <Input id="name" name="name" value={formData.name} onChange={handleInputChange} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" name="email" type="email" value={formData.email} onChange={handleInputChange} />
                    </div>
                    <Button type="submit">Update Profile</Button>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Change Password</CardTitle>
                  <CardDescription>Update your password</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleUpdatePassword} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="currentPassword">Current Password</Label>
                      <Input
                        id="currentPassword"
                        name="currentPassword"
                        type="password"
                        value={formData.currentPassword}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newPassword">New Password</Label>
                      <Input
                        id="newPassword"
                        name="newPassword"
                        type="password"
                        value={formData.newPassword}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                      />
                    </div>
                    <Button type="submit">Update Password</Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="subscription" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Subscription Details</CardTitle>
                  <CardDescription>Manage your subscription plan</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="font-medium">Current Plan</h3>
                    <div className="flex items-center justify-between p-4 border rounded-md">
                      <div>
                        <p className="font-medium capitalize">{user.plan} Plan</p>
                        <p className="text-sm text-muted-foreground">
                          {user.plan === "free"
                            ? "Limited features and 3 analyses per month"
                            : user.plan === "basic"
                              ? "$9.99/month - 50 analyses per month"
                              : "$19.99/month - Unlimited analyses"}
                        </p>
                      </div>
                      <Badge variant={user.plan === "pro" ? "default" : "outline"}>
                        {user.plan === "pro" ? "Current" : "Upgrade Available"}
                      </Badge>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Usage</h3>
                    <div className="p-4 border rounded-md">
                      <div className="flex justify-between items-center mb-2">
                        <span>Analyses used this month</span>
                        <span className="font-medium">
                          {user.usageCount || 0} /
                          {user.plan === "free" ? "3" : user.plan === "basic" ? "50" : "Unlimited"}
                        </span>
                      </div>
                      <Progress
                        value={
                          user.plan === "pro" ? 50 : ((user.usageCount || 0) / (user.plan === "free" ? 3 : 50)) * 100
                        }
                        className="h-2"
                      />
                    </div>
                  </div>

                  {user.plan !== "pro" && (
                    <div className="space-y-2">
                      <h3 className="font-medium">Upgrade Options</h3>
                      <div className="grid gap-4 md:grid-cols-2">
                        {user.plan === "free" && (
                          <div className="p-4 border rounded-md">
                            <p className="font-medium">Basic Plan</p>
                            <p className="text-sm text-muted-foreground mb-4">$9.99/month - 50 analyses per month</p>
                            <Link href="/pricing?plan=basic">
                              <Button variant="outline" className="w-full">
                                Upgrade to Basic
                              </Button>
                            </Link>
                          </div>
                        )}
                        <div className="p-4 border rounded-md">
                          <p className="font-medium">Pro Plan</p>
                          <p className="text-sm text-muted-foreground mb-4">$19.99/month - Unlimited analyses</p>
                          <Link href="/pricing?plan=pro">
                            <Button className="w-full">Upgrade to Pro</Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                  <CardDescription>Manage your account preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Email Notifications</p>
                        <p className="text-sm text-muted-foreground">Receive email updates about your account</p>
                      </div>
                      <Switch checked={true} />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Save Analysis History</p>
                        <p className="text-sm text-muted-foreground">Store your previous text analyses</p>
                      </div>
                      <Switch checked={true} />
                    </div>
                  </div>

                  <div className="pt-4">
                    <Button variant="destructive">Delete Account</Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <Footer />
    </main>
  )
}

function Switch({ checked }: { checked: boolean }) {
  return (
    <div className={`w-11 h-6 rounded-full p-1 cursor-pointer ${checked ? "bg-primary" : "bg-muted"}`}>
      <div className={`h-4 w-4 rounded-full bg-white transition-transform ${checked ? "translate-x-5" : ""}`} />
    </div>
  )
}

function Badge({
  variant,
  className,
  children,
}: { variant: "default" | "outline"; className?: string; children: React.ReactNode }) {
  return (
    <span
      className={`px-2 py-1 rounded-full text-xs font-medium ${
        variant === "default"
          ? "bg-primary text-primary-foreground"
          : "border border-muted-foreground/20 text-muted-foreground"
      } ${className || ""}`}
    >
      {children}
    </span>
  )
}
