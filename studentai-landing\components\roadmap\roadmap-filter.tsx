"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronsUpDown } from "lucide-react"

const frameworks = [
  {
    value: "all",
    label: "All",
  },
  {
    value: "core-detection",
    label: "Core Detection",
  },
  {
    value: "user-experience",
    label: "User Experience",
  },
  {
    value: "content-processing",
    label: "Content Processing",
  },
  {
    value: "content-enhancement",
    label: "Content Enhancement",
  },
  {
    value: "integration",
    label: "Integration",
  },
  {
    value: "education",
    label: "Education",
  },
  {
    value: "business",
    label: "Business",
  },
]

export function RoadmapFilter() {
  const [open, setOpen] = useState(false)
  const [value, setValue] = useState("all")

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" aria-expanded={open} className="w-[200px] justify-between">
          {value ? frameworks.find((framework) => framework.value === value)?.label : "Select framework..."}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search framework..." />
          <CommandList>
            <CommandEmpty>No framework found.</CommandEmpty>
            <CommandGroup>
              {frameworks.map((framework) => (
                <CommandItem
                  key={framework.value}
                  value={framework.value}
                  onSelect={() => {
                    setValue(framework.value === value ? "" : framework.value)
                    setOpen(false)
                  }}
                >
                  <Check
                    className="mr-2 h-4 w-4"
                    style={{
                      visibility: value === framework.value ? "visible" : "hidden",
                    }}
                  />
                  {framework.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
