import Image from "next/image"

export default function OurStorySection() {
  return (
    <section className="w-full py-12 md:py-24 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
          <div className="flex justify-center">
            <div className="relative h-[400px] w-[400px] overflow-hidden rounded-full border-8 border-blue-100">
              <Image src="/images/founder.jpg" alt="Founder of Student AI Detector" fill className="object-cover" />
            </div>
          </div>
          <div className="flex flex-col justify-center space-y-4">
            <h2 className="text-3xl font-bold tracking-tight">Our Story</h2>
            <p className="text-gray-500 md:text-lg/relaxed">
              Student AI Detector was founded by Dr. <PERSON>, a professor with 15 years of experience in education.
              In 2022, he noticed a sudden shift in student writing patterns that coincided with the rise of advanced AI
              writing tools.
            </p>
            <p className="text-gray-500 md:text-lg/relaxed">
              "I wasn't looking to catch students cheating—I wanted to preserve the value of education. When students
              submit AI-generated work, they miss out on critical thinking and writing skills that are essential for
              their future success."
            </p>
            <p className="text-gray-500 md:text-lg/relaxed">
              Working with a team of AI specialists and educators, Dr. Wilson developed a detection system specifically
              calibrated for academic writing. Today, Student AI Detector helps educators worldwide maintain academic
              integrity while teaching students about the responsible use of AI tools.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
