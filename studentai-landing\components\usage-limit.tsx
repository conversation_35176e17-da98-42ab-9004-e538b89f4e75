import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ip<PERSON>ontent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { InfoIcon } from "lucide-react"
import Link from "next/link"

interface UsageLimitProps {
  current: number
  limit: number
  plan: string
}

export function UsageLimit({ current, limit, plan }: UsageLimitProps) {
  const percentage = (current / limit) * 100
  const isNearLimit = percentage >= 80
  const isAtLimit = current >= limit

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center gap-2">
            <div className="w-full sm:w-24 flex flex-col gap-1">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">Usage</span>
                <span
                  className={`font-medium ${isNearLimit ? "text-amber-500" : ""} ${isAtLimit ? "text-red-500" : ""}`}
                >
                  {current}/{limit}
                </span>
              </div>
              <Progress
                value={percentage}
                className="h-1.5"
                indicatorClassName={`${isNearLimit ? "bg-amber-500" : ""} ${isAtLimit ? "bg-red-500" : ""}`}
              />
            </div>
            <InfoIcon className="h-4 w-4 text-muted-foreground hidden sm:block" />
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom" align="end" className="max-w-xs">
          <div className="space-y-2 p-1">
            <p className="font-medium">Usage Limit</p>
            <p className="text-sm text-muted-foreground">
              You've used {current} of {limit} analyses on your {plan} plan.
              {isNearLimit && !isAtLimit && " You're approaching your limit."}
              {isAtLimit && " You've reached your limit."}
            </p>
            {(isNearLimit || plan !== "pro") && (
              <Link href="/pricing" className="text-sm text-primary hover:underline block mt-2">
                Upgrade your plan for more analyses
              </Link>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
