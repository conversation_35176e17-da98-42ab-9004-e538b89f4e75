import { Card, CardContent } from "@/components/ui/card"
import { Shield, Zap, FileText, Sparkles } from "lucide-react"
import Image from "next/image"

export function FeatureHighlights() {
  return (
    <section className="bg-muted/50 py-12 md:py-16">
      <div className="container">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold tracking-tight mb-2">Key Features</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Comprehensive tools for AI detection and text humanization
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          <Card className="bg-background">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Advanced AI Detection</h3>
                  <p className="text-muted-foreground mb-4">
                    Our multi-layered detection approach identifies content from ChatGPT, GPT-4, <PERSON>, and other AI
                    models with high accuracy.
                  </p>
                  <div className="mb-4">
                    <Image
                      src="/screenshots/ai-detection-feature.png"
                      alt="AI Detection Feature"
                      width={400}
                      height={300}
                      className="rounded-lg border shadow-sm"
                    />
                  </div>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Pattern recognition algorithms</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Linguistic analysis of sentence structure</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Contextual evaluation of content</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Comparative benchmarking against known AI models</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-background">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Sparkles className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">AI Text Humanizer</h3>
                  <p className="text-muted-foreground mb-4">
                    Transform AI-generated text to sound more natural and human-like while maintaining the original
                    meaning.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Natural paraphrasing that preserves meaning</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Style adjustment to match human writing patterns</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Vocabulary enhancement for more nuanced expression</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">AI section fixing to target problematic passages</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-background">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Fast & Efficient Processing</h3>
                  <p className="text-muted-foreground mb-4">
                    Get results quickly with our optimized processing engine, designed for both individual and batch
                    analysis.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Analyze up to 15,000 words per query</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Batch processing for multiple documents</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Results in seconds, not minutes</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">API access for integration with your workflows</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-background">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">Comprehensive Reports</h3>
                  <p className="text-muted-foreground mb-4">
                    Get detailed insights into AI content detection with our clear, actionable reports.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Overall AI probability score</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Highlighted suspicious sections</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Sentence-by-sentence breakdown</span>
                    </li>
                    <li className="flex items-start">
                      <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span className="text-sm">Exportable results for documentation</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
