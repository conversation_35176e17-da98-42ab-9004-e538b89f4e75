import type { Metadata } from "next"
import { getAllBlogPosts, getFeaturedPosts, blogCategories } from "@/lib/blog"
import { defaultMetadata } from "@/lib/seo-utils"
import { PostCard } from "@/components/blog/post-card"
import { BlogSidebar } from "@/components/blog/blog-sidebar"
import { NewsletterSection } from "@/components/blog/newsletter-section"
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"

export const metadata: Metadata = {
  title: "Blog & Resources | StudentAidDetector",
  description:
    "Explore our collection of articles, guides, and resources about AI detection, academic integrity, and content creation.",
  keywords: [
    ...defaultMetadata.keywords,
    "AI detection resources",
    "academic integrity articles",
    "content creation guides",
  ],
}

export default function BlogPage() {
  const allPosts = getAllBlogPosts()
  const featuredPosts = getFeaturedPosts()
  const recentPosts = allPosts.slice(0, 3)

  // Get all unique tags from posts
  const allTags = Array.from(new Set(allPosts.flatMap((post) => post.tags))).slice(0, 10)

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-12 flex-grow">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <header className="mb-12 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Blog & Resources</h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Insights, guides, and resources about AI detection, academic integrity, and content creation
            </p>
          </header>

          {/* Search Bar */}
          <div className="relative max-w-2xl mx-auto mb-12">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
            <Input type="search" placeholder="Search articles..." className="pl-10 py-6 text-lg" />
          </div>

          {/* Featured Post */}
          {featuredPosts.length > 0 && (
            <section className="mb-12">
              <h2 className="text-2xl font-bold mb-6">Featured</h2>
              <PostCard
                post={{
                  ...featuredPosts[0],
                  coverImage: "/images/blog/content-creator-writing.png",
                }}
                variant="featured"
              />
            </section>
          )}

          {/* Main Content */}
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Posts */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="all" className="mb-8">
                <TabsList className="mb-6">
                  <TabsTrigger value="all">All Posts</TabsTrigger>
                  <TabsTrigger value="ai-detection">AI Detection</TabsTrigger>
                  <TabsTrigger value="academic">Academic</TabsTrigger>
                  <TabsTrigger value="writing">Writing</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="mt-0">
                  <div className="grid sm:grid-cols-2 gap-6">
                    {allPosts.map((post, index) => (
                      <PostCard
                        key={post.id}
                        post={{
                          ...post,
                          coverImage:
                            index === 0
                              ? "/images/blog/ai-in-education-presentation.png"
                              : index === 1
                                ? "/images/blog/blog-writer.png"
                                : "/images/blog/author-avatar-1.png",
                        }}
                      />
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="ai-detection" className="mt-0">
                  <div className="grid sm:grid-cols-2 gap-6">
                    {allPosts
                      .filter((post) => post.category === "AI Detection")
                      .map((post, index) => (
                        <PostCard
                          key={post.id}
                          post={{
                            ...post,
                            coverImage:
                              index % 2 === 0
                                ? "/images/blog/ai-in-education-presentation.png"
                                : "/images/blog/blog-writer.png",
                          }}
                        />
                      ))}
                  </div>
                </TabsContent>

                <TabsContent value="academic" className="mt-0">
                  <div className="grid sm:grid-cols-2 gap-6">
                    {allPosts
                      .filter((post) => post.category === "Academic Integrity" || post.category === "Education")
                      .map((post, index) => (
                        <PostCard
                          key={post.id}
                          post={{
                            ...post,
                            coverImage:
                              index % 2 === 0
                                ? "/images/blog/ai-in-education-presentation.png"
                                : "/images/blog/author-avatar-1.png",
                          }}
                        />
                      ))}
                  </div>
                </TabsContent>

                <TabsContent value="writing" className="mt-0">
                  <div className="grid sm:grid-cols-2 gap-6">
                    {allPosts
                      .filter((post) => post.category === "Writing Tips")
                      .map((post, index) => (
                        <PostCard
                          key={post.id}
                          post={{
                            ...post,
                            coverImage:
                              index % 2 === 0
                                ? "/images/blog/content-creator-writing.png"
                                : "/images/blog/blog-writer.png",
                          }}
                        />
                      ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div>
              <BlogSidebar recentPosts={recentPosts} categories={blogCategories} popularTags={allTags} />
            </div>
          </div>

          {/* Newsletter */}
          <div className="mt-16">
            <NewsletterSection newsletterImage="/images/blog/newsletter-signup.png" />
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
