"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertTriangle,
  CheckCircle,
  X,
  Shield,
  BarChart,
  ArrowRight,
  Lock,
  Sparkles,
  Zap,
  Bot,
  LineChart,
  MessageSquare,
} from "lucide-react";
import { motion } from "framer-motion";
import { Badge } from "./ui/badge";
import Link from "next/link";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AIDetectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  text: string;
}

export function AIDetectionModal({
  isOpen,
  onClose,
  text,
}: AIDetectionModalProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<null | {
    score: number;
    isAi: boolean;
    patterns?: string[];
    fullInsights?: string[];
    confidence?: string;
    remainingCredits?: number;
    likelyModel?: string;
    isPremiumResult?: boolean;
  }>(null);
  const [error, setError] = useState<{
    message: string;
    requiresLogin?: boolean;
    remainingTime?: number;
  } | null>(null);

  useEffect(() => {
    if (isOpen && text) {
      analyzeText();
    }

    return () => {
      setResult(null);
      setError(null);
      setProgress(0);
    };
  }, [isOpen]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isAnalyzing) {
      interval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + (100 - prev) * 0.1;
          return newProgress > 95 ? 95 : newProgress;
        });
      }, 200);
    } else {
      setProgress(100);
    }

    return () => clearInterval(interval);
  }, [isAnalyzing]);

  const analyzeText = async () => {
    if (!text.trim()) return;

    setIsAnalyzing(true);
    setResult(null);
    setError(null);
    setProgress(0);

    try {
      const response = await fetch("/api/detect", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ text }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 429) {
          setError({
            message:
              data.message || "Rate limit exceeded. Please try again later.",
            requiresLogin: data.requiresLogin,
            remainingTime: data.remainingTime,
          });
        } else {
          throw new Error(data.error || `Error: ${response.status}`);
        }
        return;
      }

      setResult(data.result);
    } catch (err) {
      setError({
        message: "Failed to analyze text. Please try again later.",
      });
      console.error("Error analyzing text:", err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const formatTimeRemaining = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));

    return `${hours}h ${minutes}m`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-2xl p-0 overflow-hidden border-0 shadow-xl h-auto max-h-[90vh] overflow-y-auto">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 -z-10" />

        <DialogHeader className="px-6 pt-6 pb-4 sticky top-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm z-10 border-b border-gray-100 dark:border-gray-800">
          <DialogTitle className="text-xl font-bold flex items-center">
            <Shield className="w-5 h-5 text-blue-500 mr-2" />
            <span>AI Content Analysis</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0 rounded-full ml-auto"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="p-6">
          {isAnalyzing ? (
            <motion.div
              className="flex flex-col items-center justify-center py-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className="relative w-28 h-28 mb-8">
                <motion.div
                  className="absolute inset-0 border-4 border-blue-200 dark:border-blue-900/30 rounded-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                />
                <motion.div
                  className="absolute inset-0 border-4 border-t-blue-500 rounded-full"
                  initial={{ rotate: 0 }}
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                />
                <motion.div
                  className="absolute inset-0 flex items-center justify-center"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  <Shield className="h-12 w-12 text-blue-500" />
                </motion.div>
              </div>

              <motion.h3
                className="text-2xl font-bold mb-3"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                Analyzing Your Text
              </motion.h3>

              <motion.p
                className="text-gray-500 dark:text-gray-400 text-center max-w-md mb-8"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Our advanced AI is scanning for patterns and indicators of
                AI-generated content
              </motion.p>

              <motion.div
                className="w-full max-w-md h-2 bg-blue-100 dark:bg-blue-900/20 rounded-full overflow-hidden"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                <motion.div
                  className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"
                  style={{ width: `${progress}%` }}
                />
              </motion.div>

              <motion.p
                className="mt-3 text-sm text-blue-500 dark:text-blue-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                {Math.round(progress)}% complete
              </motion.p>
            </motion.div>
          ) : error ? (
            <div className="space-y-6 py-4">
              <motion.div
                className="rounded-xl border border-red-100 bg-red-50/50 p-5 dark:border-red-900/30 dark:bg-red-900/10"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
              >
                <div className="flex items-start space-x-4">
                  <div className="bg-red-100 dark:bg-red-900/30 rounded-full p-2.5">
                    <AlertTriangle className="h-5 w-5 text-red-500" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg text-red-900 dark:text-red-400">
                      Analysis Limit Reached
                    </h3>
                    <p className="mt-1 text-red-800/70 dark:text-red-300/70">
                      {error.message}
                    </p>
                  </div>
                </div>
              </motion.div>

              {error.requiresLogin && (
                <motion.div
                  className="w-full rounded-xl shadow-sm bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700/50 overflow-hidden"
                  initial={{ y: 30, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 100 }}
                >
                  <div className="relative overflow-hidden">
                    <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-400/10 to-transparent -z-10"></div>
                    <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-indigo-400/10 to-transparent -z-10"></div>

                    <div className="px-6 py-8">
                      <div className="flex items-start gap-5">
                        <div className="hidden sm:flex shrink-0 w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-500 items-center justify-center shadow-lg shadow-blue-500/20">
                          <Shield className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <motion.div
                            className="flex flex-wrap items-center gap-2 mb-2"
                            initial={{ x: -20, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.3 }}
                          >
                            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                              Unlock Unlimited Analysis
                            </h3>
                            <Badge className="bg-gradient-to-r from-blue-500 to-indigo-500 border-0 text-white">
                              Free Account
                            </Badge>
                          </motion.div>
                          <motion.p
                            className="text-gray-600 dark:text-gray-300 mb-6 max-w-lg"
                            initial={{ x: -20, opacity: 0 }}
                            animate={{ x: 0, opacity: 1 }}
                            transition={{ delay: 0.4 }}
                          >
                            Create an account to access unlimited AI detection
                            and premium insights for academic integrity.
                          </motion.p>

                          <motion.div
                            className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8"
                            initial={{ y: 10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.5 }}
                          >
                            {[
                              {
                                icon: <CheckCircle className="h-4 w-4" />,
                                title: "Unlimited Analysis",
                                text: "No daily detection limits",
                              },
                              {
                                icon: <LineChart className="h-4 w-4" />,
                                title: "Detailed Reports",
                                text: "AI pattern identification",
                              },
                              {
                                icon: <MessageSquare className="h-4 w-4" />,
                                title: "Expert Support",
                                text: "Priority assistance",
                              },
                            ].map((feature, i) => (
                              <div
                                key={i}
                                className="flex items-start gap-2.5 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/30"
                              >
                                <div className="mt-0.5 text-blue-500 dark:text-blue-400">
                                  {feature.icon}
                                </div>
                                <div>
                                  <h4 className="font-medium text-gray-900 dark:text-gray-100">
                                    {feature.title}
                                  </h4>
                                  <p className="text-xs text-gray-600 dark:text-gray-400">
                                    {feature.text}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </motion.div>

                          <motion.div
                            className="flex flex-col sm:flex-row gap-3"
                            initial={{ y: 10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            transition={{ delay: 0.7 }}
                          >
                            <Button
                              asChild
                              size="lg"
                              className="w-full sm:flex-1 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-0 text-white shadow-lg shadow-blue-500/20 transition-all duration-300 hover:shadow-blue-500/40 hover:-translate-y-0.5"
                            >
                              <Link href="/auth/register">
                                <span className="flex items-center justify-center">
                                  Create Free Account
                                  <ArrowRight className="ml-2 h-4 w-4" />
                                </span>
                              </Link>
                            </Button>
                            <Button
                              asChild
                              size="lg"
                              variant="outline"
                              className="w-full sm:w-auto border-indigo-200 dark:border-indigo-800 text-indigo-700 dark:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/30"
                            >
                              <Link href="/auth/login">Sign In</Link>
                            </Button>
                          </motion.div>

                          {error.remainingTime && (
                            <motion.div
                              className="mt-5 p-3 bg-blue-50/70 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30 flex items-center"
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ delay: 0.8 }}
                            >
                              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2" />
                              <p className="text-sm text-blue-700 dark:text-blue-300">
                                Next free analysis available in{" "}
                                <span className="font-bold">
                                  {formatTimeRemaining(error.remainingTime)}
                                </span>
                              </p>
                            </motion.div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          ) : result ? (
            <div className="pb-4">
              <motion.div
                className="flex flex-col items-center text-center mb-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <div className="relative w-full max-w-md mb-6">
                  <motion.div
                    className="h-3 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden w-full"
                    initial={{ width: 0 }}
                    animate={{ width: "100%" }}
                    transition={{ delay: 0.2, duration: 0.6 }}
                  />
                  <motion.div
                    className="absolute top-0 left-0 h-3 rounded-full"
                    style={{
                      width: `${result.score}%`,
                      background: `linear-gradient(to right, ${
                        result.score > 50
                          ? "rgb(249, 115, 22), rgb(239, 68, 68)"
                          : "rgb(34, 197, 94), rgb(16, 185, 129)"
                      })`,
                    }}
                    initial={{ width: 0 }}
                    animate={{ width: `${result.score}%` }}
                    transition={{ delay: 0.8, duration: 0.8, type: "spring" }}
                  />
                  <motion.div
                    className="absolute top-0 -ml-3 mt-1.5"
                    style={{ left: `${result.score}%` }}
                    initial={{ left: "0%", opacity: 0 }}
                    animate={{ left: `${result.score}%`, opacity: 1 }}
                    transition={{ delay: 1.2, duration: 0.5, type: "spring" }}
                  >
                    <div
                      className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        result.score > 50
                          ? "bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400"
                          : "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
                      }`}
                    >
                      <span className="text-xs font-bold">{result.score}</span>
                    </div>
                  </motion.div>
                  <div className="flex justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>Human</span>
                    <span>AI</span>
                  </div>
                </div>
                <motion.div
                  className={`w-20 h-20 rounded-2xl flex items-center justify-center mb-4 ${
                    result.score > 50
                      ? "bg-orange-100 dark:bg-orange-900/20"
                      : "bg-green-100 dark:bg-green-900/20"
                  }`}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ type: "spring", delay: 0.3 }}
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", delay: 0.5 }}
                  >
                    {result.score > 50 ? (
                      <Bot className="h-10 w-10 text-orange-500" />
                    ) : (
                      <CheckCircle className="h-10 w-10 text-green-500" />
                    )}
                  </motion.div>
                </motion.div>
                <motion.h2
                  className={`text-2xl sm:text-3xl font-bold mb-3 ${
                    result.score > 50
                      ? "text-orange-600 dark:text-orange-400"
                      : "text-green-600 dark:text-green-400"
                  }`}
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  {result.score > 50
                    ? "AI-Generated Content"
                    : "Human-Written Content"}
                </motion.h2>
                <motion.div
                  className="flex flex-col items-center gap-3"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="flex items-center gap-3">
                    <Badge
                      className={`px-3 py-1 ${
                        result.score > 50
                          ? "bg-orange-100 text-orange-700 hover:bg-orange-100 dark:bg-orange-900/30 dark:text-orange-400"
                          : "bg-green-100 text-green-700 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400"
                      }`}
                    >
                      {result.score}% AI Probability
                    </Badge>
                    <Badge
                      className={`px-3 py-1 ${
                        result.score > 85 || result.score < 15
                          ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                          : result.score > 65 || result.score < 35
                          ? "bg-blue-100/70 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
                          : "bg-blue-100/50 text-blue-700 dark:bg-blue-900/10 dark:text-blue-400"
                      }`}
                    >
                      {result.score > 85 || result.score < 15
                        ? "High Confidence"
                        : result.score > 65 || result.score < 35
                        ? "Medium Confidence"
                        : "Low Confidence"}
                    </Badge>
                  </div>
                  {result.likelyModel && result.score > 50 && (
                    <div className="mt-1 px-4 py-2 rounded-full bg-indigo-50 dark:bg-indigo-900/30 border border-indigo-100 dark:border-indigo-800/30">
                      <p className="text-indigo-700 dark:text-indigo-300 font-medium flex items-center text-sm gap-1.5">
                        <Sparkles className="h-4 w-4" />
                        Likely model:{" "}
                        <span className="font-bold">{result.likelyModel}</span>
                      </p>
                    </div>
                  )}
                </motion.div>
              </motion.div>
              <Tabs defaultValue="patterns" className="w-full">
                <div className="flex items-center justify-between mb-2">
                  <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto">
                    <TabsTrigger
                      value="patterns"
                      className="flex items-center gap-1.5"
                    >
                      <BarChart className="h-4 w-4" />
                      <span>Key Patterns</span>
                      {result.patterns && (
                        <Badge
                          variant="outline"
                          className="ml-1.5 h-5 px-1.5 text-xs font-normal"
                        >
                          {result.patterns.length}
                        </Badge>
                      )}
                    </TabsTrigger>
                    <TabsTrigger
                      value="advanced"
                      className="relative group flex items-center gap-1.5 overflow-hidden"
                    >
                      <span className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20 dark:from-indigo-500/10 dark:to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity" />
                      <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500/30 via-purple-500/30 to-indigo-500/30 rounded-full blur-md opacity-0 group-hover:opacity-70 animate-pulse-slow" />
                      <Sparkles className="h-4 w-4 text-indigo-500 dark:text-indigo-400 relative z-10" />
                      <span className="relative z-10">Advanced Analysis</span>
                      <Badge
                        variant="outline"
                        className="relative z-10 ml-1.5 h-5 px-1.5 text-xs font-normal border-indigo-200 dark:border-indigo-800 text-indigo-600 dark:text-indigo-400"
                      >
                        <Lock className="h-2.5 w-2.5 mr-0.5" /> PRO
                      </Badge>
                    </TabsTrigger>
                  </TabsList>
                </div>
                <TabsContent value="patterns" className="mt-3">
                  <motion.div
                    className="space-y-4 bg-white dark:bg-gray-800 rounded-xl p-5 border border-gray-100 dark:border-gray-700 shadow-sm"
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.1, type: "spring", stiffness: 100 }}
                  >
                    {result.patterns && result.patterns.length > 0 ? (
                      <>
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3">
                          <h3 className="font-semibold flex items-center gap-2 text-gray-900 dark:text-gray-100">
                            <BarChart className="h-4 w-4 text-blue-500" />
                            <span>Key Pattern Analysis</span>
                          </h3>
                          <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                            <span>Patterns detected:</span>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              {result.patterns.length}
                            </span>
                          </div>
                        </div>
                        <div className="space-y-3 mt-3">
                          {result.patterns.map((pattern, i) => (
                            <motion.div
                              key={i}
                              className="p-3 bg-gray-50 dark:bg-gray-800/80 rounded-lg text-sm border border-gray-100 dark:border-gray-700/50"
                              initial={{ x: -20, opacity: 0 }}
                              animate={{ x: 0, opacity: 1 }}
                              transition={{ delay: 0.2 + i * 0.1 }}
                            >
                              {pattern}
                            </motion.div>
                          ))}
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-10 text-gray-500 dark:text-gray-400">
                        No patterns detected. Try with a longer text sample.
                      </div>
                    )}
                  </motion.div>
                </TabsContent>
                <TabsContent value="advanced" className="mt-3">
                  <motion.div
                    className="bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm overflow-hidden"
                    initial={{ y: 10, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.1, type: "spring", stiffness: 100 }}
                  >
                    <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border-b border-indigo-100 dark:border-indigo-800/50 p-4">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <div className="bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full p-1.5">
                            <Sparkles className="h-4 w-4 text-white" />
                          </div>
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                            Advanced AI Pattern Analysis
                          </h3>
                        </div>
                        <Badge
                          variant="outline"
                          className="text-xs border-indigo-200 dark:border-indigo-800 text-indigo-600 dark:text-indigo-400"
                        >
                          <Lock className="h-3 w-3 mr-1" />
                          Premium
                        </Badge>
                      </div>
                    </div>
                    <div className="p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="bg-indigo-100 dark:bg-indigo-900/30 p-1 rounded-md">
                          <LineChart className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                        </div>
                        <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                          Expert Summary
                        </p>
                      </div>
                      <p className="text-gray-700 dark:text-gray-300 text-sm">
                        {result.score > 50
                          ? `This text displays patterns consistent with AI generation, particularly in ${
                              result.score > 85 ? "very" : ""
                            } structured language and formulaic construction. ${
                              result.likelyModel
                                ? `The specific patterns strongly suggest ${result.likelyModel} authorship.`
                                : "The patterns are characteristic of modern large language models."
                            }`
                          : `This text displays patterns consistent with human authorship, particularly in terms of varied sentence structure and natural language flow. The presence of stylistic inconsistencies and unique language patterns is a significant indicator.`}
                      </p>
                    </div>
                    <div className="bg-gradient-to-b from-indigo-50/50 to-purple-50/50 dark:from-indigo-900/10 dark:to-purple-900/10 p-4">
                      <div className="mb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="bg-indigo-100 dark:bg-indigo-900/30 p-1 rounded-md">
                              <MessageSquare className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                            </div>
                            <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                              In-Depth Analysis
                            </p>
                          </div>
                          <div className="text-xs text-indigo-500 dark:text-indigo-400 font-medium flex items-center">
                            <Lock className="h-3 w-3 mr-1" />
                            Premium Insight
                          </div>
                        </div>
                        <div className="mt-2 relative">
                          <div className="blur-[3px] opacity-70 pointer-events-none">
                            <div className="p-3 bg-white dark:bg-gray-800/80 rounded-lg text-sm border border-gray-200 dark:border-gray-700/50 mb-2">
                              {result.fullInsights && result.fullInsights[0]
                                ? result.fullInsights[0]
                                : "Detailed analysis of linguistic patterns, structural consistency, and creative elements..."}
                            </div>
                            <div className="p-3 bg-white dark:bg-gray-800/80 rounded-lg text-sm border border-gray-200 dark:border-gray-700/50">
                              {result.fullInsights && result.fullInsights[1]
                                ? result.fullInsights[1]
                                : "Assessment of creativity markers, semantic coherence, and linguistic fingerprinting..."}
                            </div>
                          </div>
                          <div className="absolute inset-0 bg-gradient-to-t from-indigo-50/80 via-transparent to-transparent dark:from-indigo-900/40 flex items-end justify-center pb-3">
                            <div className="w-full max-w-xs text-center">
                              <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                                Unlock detailed AI pattern analysis and get
                                precise insights
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-5 bg-gradient-to-r from-indigo-600 to-purple-600 flex flex-col items-center text-center">
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 }}
                        className="mb-4"
                      >
                        <div className="bg-white/20 backdrop-blur-sm p-3 rounded-full">
                          <Zap className="h-6 w-6 text-white" />
                        </div>
                      </motion.div>
                      <h3 className="text-white font-bold text-lg mb-1">
                        Unlock Premium AI Detection
                      </h3>
                      <p className="text-indigo-100 mb-4 max-w-md text-sm">
                        Get access to in-depth analysis, model identification,
                        and detailed AI patterns with a free account
                      </p>
                      <Button
                        asChild
                        size="lg"
                        className="bg-white hover:bg-gray-100 text-indigo-700 border-0 shadow-lg shadow-indigo-800/20 hover:shadow-indigo-800/30"
                      >
                        <Link href="/auth/register">
                          <span className="flex items-center">
                            Create Free Account
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </span>
                        </Link>
                      </Button>
                    </div>
                  </motion.div>
                </TabsContent>
              </Tabs>
              <motion.div
                className="flex flex-wrap gap-3 justify-end mt-8 border-t border-gray-100 dark:border-gray-800 pt-6"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.9 }}
              >
                {result.score > 50 && (
                  <Button
                    asChild
                    className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 border-0 shadow-md hover:shadow-lg transition-all"
                    size="lg"
                  >
                    <Link href="/tools/humanization">
                      <span className="flex items-center">
                        Humanize This Text
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </span>
                    </Link>
                  </Button>
                )}
                <Button variant="outline" onClick={onClose} size="lg">
                  Close
                </Button>
              </motion.div>
            </div>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
}

<style jsx global>{`
  @keyframes pulse-slow {
    0% {
      opacity: 0;
    }
    50% {
      opacity: 0.7;
    }
    100% {
      opacity: 0;
    }
  }

  .animate-pulse-slow {
    animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
`}</style>;
