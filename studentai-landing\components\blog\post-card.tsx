import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, User, ArrowRight } from "lucide-react"
import type { BlogPost } from "@/lib/blog"
import { Button } from "@/components/ui/button"

interface PostCardProps {
  post: BlogPost
  variant?: "default" | "featured"
}

export function PostCard({ post, variant = "default" }: PostCardProps) {
  const isFeatured = variant === "featured"

  return (
    <Card className={`overflow-hidden h-full flex flex-col ${isFeatured ? "lg:flex-row" : ""}`}>
      <div className={`${isFeatured ? "lg:w-1/2" : ""}`}>
        <Link href={`/blog/${post.id}`} className="block overflow-hidden">
          <div className={`aspect-video bg-muted relative overflow-hidden ${isFeatured ? "h-full" : ""}`}>
            <img
              src={post.coverImage || "/placeholder.svg?height=400&width=600"}
              alt={`Cover image for article: ${post.title}`}
              className="object-cover w-full h-full transition-transform hover:scale-105 duration-300"
              width={isFeatured ? 600 : 400}
              height={isFeatured ? 400 : 300}
              loading={isFeatured ? "eager" : "lazy"}
            />
          </div>
        </Link>
      </div>

      <div className={`flex flex-col ${isFeatured ? "lg:w-1/2" : ""}`}>
        <CardHeader>
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
            <Badge variant="outline">{post.category}</Badge>
          </div>
          <Link href={`/blog/${post.id}`} className="group">
            <h3
              className={`font-bold group-hover:text-primary transition-colors ${isFeatured ? "text-2xl" : "text-xl"}`}
            >
              {post.title}
            </h3>
          </Link>
        </CardHeader>

        <CardContent className="flex-grow">
          <p className="text-muted-foreground line-clamp-3">{post.excerpt}</p>

          <div className="flex flex-wrap gap-4 text-sm text-muted-foreground mt-4">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <time dateTime={post.date}>
                {new Date(post.date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </time>
            </div>
            <div className="flex items-center gap-1">
              <User className="h-4 w-4" />
              <span>{post.author.name}</span>
            </div>
          </div>
        </CardContent>

        <CardFooter className="pt-0">
          <div className="flex flex-col gap-4 w-full">
            {isFeatured && (
              <div className="flex flex-wrap gap-2 mb-2">
                {post.tags.slice(0, 3).map((tag) => (
                  <Link href={`/blog/tag/${tag.replace(/ /g, "-")}`} key={tag}>
                    <Badge variant="secondary" className="hover:bg-secondary/80 cursor-pointer">
                      {tag}
                    </Badge>
                  </Link>
                ))}
              </div>
            )}

            <Link href={`/blog/${post.id}`} className="w-full">
              <Button variant={isFeatured ? "default" : "outline"} className="w-full gap-2 justify-center">
                Read Article
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardFooter>
      </div>
    </Card>
  )
}
