import { getBlogPost, getRecentPosts } from "@/lib/blog"
import { notFound } from "next/navigation"
import type { Metada<PERSON> } from "next"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { PostContent } from "@/components/blog/post-content"
import { BlogSidebar } from "@/components/blog/blog-sidebar"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import ultimateGuide from "@/public/images/blog/ultimate-guide-to-ai-content-detection-in-2025.png"
import Image from "next/image"

interface BlogPostPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = getBlogPost(params.slug)

  if (!post) {
    return {
      title: "Blog Post Not Found",
    }
  }

  return {
    title: `${post.title} | StudentAidDetector Blog`,
    description: post.excerpt,
    keywords: post.tags,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.date,
      authors: [post.author.name],
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [post.coverImage],
    },
  }
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const post = getBlogPost(params.slug)

  if (!post) {
    notFound()
  }

  const recentPosts = getRecentPosts(3).filter((p) => p.id !== post.id)

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-12 flex-grow">
        <Link href="/blog" className="inline-block mb-8">
          <Button variant="ghost" className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to all posts
          </Button>
        </Link>

        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Image
              src={ultimateGuide || "/placeholder.svg"}
              alt="The Ultimate Guide to AI Content Detection in 2025"
              width={1200}
              height={600}
              className="w-full h-auto object-cover"
            />
            <PostContent post={post} />
          </div>

          <div>
            <BlogSidebar recentPosts={recentPosts} />
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
