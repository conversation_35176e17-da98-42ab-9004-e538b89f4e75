import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { BookO<PERSON>, User, PenTool, Building, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function AudienceSection() {
  const [activeCard, setActiveCard] = useState<number | null>(null);

  const audiences = [
    {
      icon: <BookOpen className="h-6 w-6" />,
      title: "Educators",
      subtitle: "For teachers and professors",
      description:
        "Verify student submissions, maintain academic integrity, and teach responsible AI use in your classroom.",
      link: "/for-educators",
      color:
        "from-blue-50 via-blue-50 to-transparent dark:from-blue-950/40 dark:via-blue-950/30 dark:to-transparent",
      iconBg: "bg-blue-100 dark:bg-blue-900/30",
      iconColor: "text-blue-600 dark:text-blue-400",
      borderColor: "border-blue-200 dark:border-blue-800",
      hoverBorderColor:
        "group-hover:border-blue-400 dark:group-hover:border-blue-600",
    },
    {
      icon: <User className="h-6 w-6" />,
      title: "Students",
      subtitle: "For learners at all levels",
      description:
        "Check your work before submission, learn how to use AI responsibly, and develop your authentic voice.",
      link: "/for-students",
      color:
        "from-purple-50 via-purple-50 to-transparent dark:from-purple-950/40 dark:via-purple-950/30 dark:to-transparent",
      iconBg: "bg-purple-100 dark:bg-purple-900/30",
      iconColor: "text-purple-600 dark:text-purple-400",
      borderColor: "border-purple-200 dark:border-purple-800",
      hoverBorderColor:
        "group-hover:border-purple-400 dark:group-hover:border-purple-600",
    },
    {
      icon: <PenTool className="h-6 w-6" />,
      title: "Content Creators",
      subtitle: "For writers and publishers",
      description:
        "Ensure content authenticity, verify freelance submissions, and maintain your brand's unique voice.",
      link: "/for-writers",
      color:
        "from-amber-50 via-amber-50 to-transparent dark:from-amber-950/40 dark:via-amber-950/30 dark:to-transparent",
      iconBg: "bg-amber-100 dark:bg-amber-900/30",
      iconColor: "text-amber-600 dark:text-amber-400",
      borderColor: "border-amber-200 dark:border-amber-800",
      hoverBorderColor:
        "group-hover:border-amber-400 dark:group-hover:border-amber-600",
    },
    {
      icon: <Building className="h-6 w-6" />,
      title: "Institutions",
      subtitle: "For schools and organizations",
      description:
        "Implement campus-wide AI detection, develop AI policies, and integrate with your existing systems.",
      link: "/use-cases",
      color:
        "from-emerald-50 via-emerald-50 to-transparent dark:from-emerald-950/40 dark:via-emerald-950/30 dark:to-transparent",
      iconBg: "bg-emerald-100 dark:bg-emerald-900/30",
      iconColor: "text-emerald-600 dark:text-emerald-400",
      borderColor: "border-emerald-200 dark:border-emerald-800",
      hoverBorderColor:
        "group-hover:border-emerald-400 dark:group-hover:border-emerald-600",
    },
  ];

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.6, ease: "easeOut" } },
  };

  return (
    <section className="w-full py-20 overflow-hidden relative">
      {/* Background decorative elements - FIXED: Contained within the section */}
      <div
        className="absolute left-0 right-0 w-full h-[500px] bg-gradient-radial from-blue-50/50 to-transparent dark:from-blue-950/10 opacity-70 blur-3xl"
        style={{ zIndex: -1 }}
      />

      {/* FIXED: Repositioned decorative elements to stay within the container */}
      <div
        className="absolute left-[5%] top-1/3 w-96 h-96 rounded-full bg-purple-100/30 dark:bg-purple-900/10 blur-3xl"
        style={{ zIndex: -1 }}
      />
      <div
        className="absolute right-[5%] bottom-1/4 w-96 h-96 rounded-full bg-amber-100/30 dark:bg-amber-900/10 blur-3xl"
        style={{ zIndex: -1 }}
      />

      <div className="container mx-auto px-4 relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ width: "0%" }}
            animate={{ width: "180px" }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
            className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mb-6 rounded-full"
          />
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">
            Who Uses Our AI Detector?
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Our AI detection tools serve multiple audiences with different needs
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 xl:gap-8"
          variants={container}
          initial="hidden"
          animate="show"
        >
          {audiences.map((audience, index) => (
            <motion.div
              key={audience.title}
              variants={item}
              className="relative group"
              onMouseEnter={() => setActiveCard(index)}
              onMouseLeave={() => setActiveCard(null)}
            >
              <div
                className={`absolute inset-0 bg-gradient-to-b ${audience.color} rounded-2xl transform scale-[0.97] opacity-0 group-hover:opacity-100 group-hover:scale-100 transition-all duration-300`}
                style={{ zIndex: -1 }}
              />

              <div
                className={`relative h-full rounded-xl border ${audience.borderColor} ${audience.hoverBorderColor} transition-all duration-300 group-hover:shadow-lg backdrop-blur-sm bg-white/60 dark:bg-gray-800/60 overflow-hidden`}
              >
                {/* Color accent top border */}
                <div
                  className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${
                    index === 0
                      ? "from-blue-400 to-blue-600"
                      : index === 1
                      ? "from-purple-400 to-purple-600"
                      : index === 2
                      ? "from-amber-400 to-amber-600"
                      : "from-emerald-400 to-emerald-600"
                  } transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out`}
                />

                <div className="p-6">
                  <div className="mb-6 flex items-center gap-4">
                    <div
                      className={`p-3 rounded-lg ${audience.iconBg} ${audience.iconColor}`}
                    >
                      {audience.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                        {audience.title}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {audience.subtitle}
                      </p>
                    </div>
                  </div>

                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    {audience.description}
                  </p>

                  <div className="mt-auto">
                    <Button
                      variant="ghost"
                      className={`group-hover:bg-opacity-10 p-0 h-auto ${
                        index === 0
                          ? "text-blue-600 hover:text-blue-700 hover:bg-blue-100 dark:hover:bg-blue-800/30"
                          : index === 1
                          ? "text-purple-600 hover:text-purple-700 hover:bg-purple-100 dark:hover:bg-purple-800/30"
                          : index === 2
                          ? "text-amber-600 hover:text-amber-700 hover:bg-amber-100 dark:hover:bg-amber-800/30"
                          : "text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100 dark:hover:bg-emerald-800/30"
                      }`}
                      asChild
                    >
                      <Link href={audience.link} className="flex items-center">
                        <span>
                          Learn more about{" "}
                          {index === 0
                            ? "educator"
                            : index === 1
                            ? "student"
                            : index === 2
                            ? "content"
                            : "institutional"}{" "}
                          solutions
                        </span>
                        <ArrowRight className="h-4 w-4 ml-1.5 transform transition-transform group-hover:translate-x-1" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
