"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"

import { <PERSON><PERSON>oot<PERSON> } from "@/components/ui/card"

import type React from "react"
import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useSearchParams, useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { CheckCircle, AlertCircle, Shield, Lock, CheckCircle2, ChromeIcon as Google } from "lucide-react"
import Link from "next/link"

export default function SignupPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()
  const [step, setStep] = useState(1)
  const [selectedPlan, setSelectedPlan] = useState("free")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  })

  useEffect(() => {
    const plan = searchParams.get("plan")
    if (plan && ["free", "basic", "pro"].includes(plan)) {
      setSelectedPlan(plan)
    }
  }, [searchParams])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))

    // Mark field as touched
    if (!touched[name]) {
      setTouched((prev) => ({ ...prev, [name]: true }))
    }

    // Validate field
    validateField(name, value)
  }

  const validateField = (name: string, value: string) => {
    const newErrors = { ...errors }

    switch (name) {
      case "name":
        if (!value.trim()) {
          newErrors.name = "Name is required"
        } else {
          delete newErrors.name
        }
        break
      case "email":
        if (!value.trim()) {
          newErrors.email = "Email is required"
        } else if (!/\S+@\S+\.\S+/.test(value)) {
          newErrors.email = "Email is invalid"
        } else {
          delete newErrors.email
        }
        break
      case "password":
        if (!value) {
          newErrors.password = "Password is required"
        } else if (value.length < 8) {
          newErrors.password = "Password must be at least 8 characters"
        } else {
          delete newErrors.password
        }

        // Also validate confirmPassword if it's been touched
        if (touched.confirmPassword) {
          if (formData.confirmPassword !== value) {
            newErrors.confirmPassword = "Passwords don't match"
          } else {
            delete newErrors.confirmPassword
          }
        }
        break
      case "confirmPassword":
        if (!value) {
          newErrors.confirmPassword = "Please confirm your password"
        } else if (value !== formData.password) {
          newErrors.confirmPassword = "Passwords don't match"
        } else {
          delete newErrors.confirmPassword
        }
        break
    }

    setErrors(newErrors)
  }

  const validateStep1 = () => {
    // Mark all fields as touched
    const step1Fields = ["name", "email"]
    const allTouched = step1Fields.reduce(
      (acc, key) => {
        acc[key] = true
        return acc
      },
      {} as Record<string, boolean>,
    )

    setTouched((prev) => ({ ...prev, ...allTouched }))

    // Validate all fields
    step1Fields.forEach((name) => {
      validateField(name, formData[name as keyof typeof formData] as string)
    })

    // Return true if no errors
    return !step1Fields.some((name) => errors[name])
  }

  const validateStep2 = () => {
    // Mark all fields as touched
    const step2Fields = ["password", "confirmPassword"]
    const allTouched = step2Fields.reduce(
      (acc, key) => {
        acc[key] = true
        return acc
      },
      {} as Record<string, boolean>,
    )

    setTouched((prev) => ({ ...prev, ...allTouched }))

    // Validate all fields
    step2Fields.forEach((name) => {
      validateField(name, formData[name as keyof typeof formData] as string)
    })

    // Return true if no errors
    return !step2Fields.some((name) => errors[name])
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateStep1() || !validateStep2()) {
      toast({
        title: "Please fix the errors in the form",
        description: "There are some issues with your information.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // In a real app, this would call an API endpoint to create the user
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Mock user creation - in a real app, this would be handled by your backend
      const user = {
        name: formData.name,
        email: formData.email,
        plan: selectedPlan,
        usageCount: 0,
        createdAt: new Date().toISOString(),
      }

      // Store in localStorage for demo purposes
      localStorage.setItem("user", JSON.stringify(user))

      toast({
        title: "Account created",
        description: `Welcome to StudentAIDetector, ${formData.name}!`,
      })

      // Redirect to home page
      router.push("/")
    } catch (error) {
      console.error("Error creating account:", error)
      toast({
        title: "Sign up failed",
        description: "There was an error creating your account. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGoogleSignUp = async () => {
    // Implement Google Sign-Up logic here
    // You would typically use a library like Firebase or NextAuth for this
    // For this demo, we'll just simulate a successful signup
    toast({
      title: "Google Sign-Up",
      description: "Google Sign-Up is not implemented in this demo.",
    })
  }

  const planFeatures = {
    free: ["10 AI detections per month", "Up to 1,500 words per analysis", "Basic paraphrasing tool (3/month)"],
    basic: [
      "100 AI detections per month",
      "Up to 5,000 words per analysis",
      "Basic humanization tools (50/month)",
      "File upload (.txt, .pdf, .docx)",
    ],
    pro: [
      "500 AI detections per month",
      "Up to 15,000 words per analysis",
      "All humanization tools (unlimited)",
      "Batch processing (up to 20 files)",
    ],
  }

  const planPrices = {
    free: "Free",
    basic: "$9.99/month",
    pro: "$19.99/month",
  }

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container py-8 md:py-12 flex-1 flex flex-col md:flex-row gap-8 items-start">
        {/* Left Column - Value Proposition */}
        <div className="w-full md:w-1/2 md:sticky md:top-24">
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold mb-2">Join StudentAIDetector Today</h1>
              <p className="text-muted-foreground text-lg">
                Detect AI-generated content and improve your writing with our powerful tools.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-start gap-3">
                <div className="mt-1 bg-primary/10 p-1.5 rounded-full">
                  <CheckCircle className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Advanced AI Detection</h3>
                  <p className="text-sm text-muted-foreground">Identify AI-generated content with over 99% accuracy</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="mt-1 bg-primary/10 p-1.5 rounded-full">
                  <CheckCircle className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Humanization Tools</h3>
                  <p className="text-sm text-muted-foreground">Transform AI text to sound more natural and authentic</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="mt-1 bg-primary/10 p-1.5 rounded-full">
                  <CheckCircle className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium">Easy Integration</h3>
                  <p className="text-sm text-muted-foreground">Works with your favorite tools and platforms</p>
                </div>
              </div>
            </div>

            {/* Testimonials */}
            <div className="bg-muted/50 p-4 rounded-lg">
              <h3 className="font-medium mb-3">What Our Users Say</h3>
              <div className="space-y-3">
                <div className="bg-background p-3 rounded-md">
                  <p className="text-sm italic mb-2">
                    "StudentAIDetector has transformed how I evaluate student work. I can now quickly identify
                    AI-generated content with confidence."
                  </p>
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                      <span className="text-xs font-bold text-primary">JM</span>
                    </div>
                    <span className="text-xs font-medium">Dr. Jessica Martinez, English Professor</span>
                  </div>
                </div>

                <div className="bg-background p-3 rounded-md">
                  <p className="text-sm italic mb-2">
                    "As a content marketer, I need to ensure my work is original. This tool helps me verify authenticity
                    and improve my writing."
                  </p>
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center">
                      <span className="text-xs font-bold text-primary">MC</span>
                    </div>
                    <span className="text-xs font-medium">Michael Chen, Content Marketing Manager</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Trusted By */}
            <div>
              <p className="text-sm text-center text-muted-foreground mb-3">Trusted by leading institutions</p>
              <div className="flex flex-wrap justify-center gap-4 items-center">
                <div className="bg-muted/30 p-2 rounded-md">
                  <div className="w-24 h-8 bg-muted/50 rounded"></div>
                </div>
                <div className="bg-muted/30 p-2 rounded-md">
                  <div className="w-24 h-8 bg-muted/50 rounded"></div>
                </div>
                <div className="bg-muted/30 p-2 rounded-md">
                  <div className="w-24 h-8 bg-muted/50 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Column - Signup Form */}
        <div className="w-full md:w-1/2">
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{step === 1 ? "Create your account" : "Choose your plan"}</CardTitle>
              <CardDescription>Get started with StudentAIDetector today</CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full mb-4" variant="outline" onClick={handleGoogleSignUp}>
                <Google className="mr-2 h-4 w-4" />
                Sign up with Google
              </Button>
              {step === 1 && (
                <SignupStep1
                  formData={formData}
                  handleInputChange={handleInputChange}
                  errors={errors}
                  touched={touched}
                  setErrors={setErrors}
                  setTouched={setTouched}
                  setStep={setStep}
                />
              )}
              {step === 2 && (
                <SignupStep2
                  formData={formData}
                  selectedPlan={selectedPlan}
                  setSelectedPlan={setSelectedPlan}
                  planFeatures={planFeatures}
                  planPrices={planPrices}
                  errors={errors}
                  touched={touched}
                  handleInputChange={handleInputChange}
                  handleSubmit={handleSubmit}
                  isSubmitting={isSubmitting}
                  setStep={setStep}
                />
              )}
            </CardContent>

            {/* Progress Indicator */}
            <CardFooter className="flex justify-between items-center">
              <p className="text-sm text-muted-foreground">Step {step} of 2</p>
              <div className="flex items-center gap-2">
                {step === 2 && (
                  <Button variant="outline" size="sm" onClick={() => setStep(1)}>
                    Back
                  </Button>
                )}
                {step === 1 && (
                  <Button
                    size="sm"
                    onClick={() => {
                      if (Object.keys(errors).length === 0 && formData.name && formData.email) {
                        setStep(2)
                      } else {
                        toast({
                          title: "Please fix the errors in the form",
                          description: "There are some issues with your information.",
                          variant: "destructive",
                        })
                      }
                    }}
                  >
                    Next
                  </Button>
                )}
              </div>
            </CardFooter>
          </Card>

          {/* Trust Badges */}
          <div className="mt-6 flex flex-wrap justify-center gap-4">
            <div className="flex items-center gap-2 bg-muted/30 px-3 py-2 rounded-md">
              <Lock className="h-4 w-4 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">SSL Secured</span>
            </div>
            <div className="flex items-center gap-2 bg-muted/30 px-3 py-2 rounded-md">
              <Shield className="h-4 w-4 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">GDPR Compliant</span>
            </div>
            <div className="flex items-center gap-2 bg-muted/30 px-3 py-2 rounded-md">
              <Lock className="h-4 w-4 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">FERPA Compliant</span>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </main>
  )
}

interface SignupStep1Props {
  formData: any
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  errors: Record<string, string>
  touched: Record<string, boolean>
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>
  setTouched: React.Dispatch<React.SetStateAction<Record<string, boolean>>>
  setStep: React.Dispatch<React.SetStateAction<number>>
}

function SignupStep1({
  formData,
  handleInputChange,
  errors,
  touched,
  setErrors,
  setTouched,
  setStep,
}: SignupStep1Props) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name" className="flex items-center justify-between">
          Name
          {errors.name && touched.name && (
            <span className="text-xs text-destructive flex items-center">
              <AlertCircle className="h-3 w-3 mr-1" />
              {errors.name}
            </span>
          )}
        </Label>
        <Input
          id="name"
          name="name"
          placeholder="Enter your name"
          required
          value={formData.name}
          onChange={handleInputChange}
          className={errors.name && touched.name ? "border-destructive" : ""}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="email" className="flex items-center justify-between">
          Email
          {errors.email && touched.email && (
            <span className="text-xs text-destructive flex items-center">
              <AlertCircle className="h-3 w-3 mr-1" />
              {errors.email}
            </span>
          )}
        </Label>
        <Input
          id="email"
          name="email"
          type="email"
          placeholder="Enter your email"
          required
          value={formData.email}
          onChange={handleInputChange}
          className={errors.email && touched.email ? "border-destructive" : ""}
        />
      </div>
    </div>
  )
}

interface SignupStep2Props {
  formData: any
  selectedPlan: string
  setSelectedPlan: React.Dispatch<React.SetStateAction<string>>
  planFeatures: Record<string, string[]>
  planPrices: Record<string, string>
  errors: Record<string, string>
  touched: Record<string, boolean>
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  handleSubmit: (e: React.FormEvent) => Promise<void>
  isSubmitting: boolean
  setStep: React.Dispatch<React.SetStateAction<number>>
}

function SignupStep2({
  formData,
  selectedPlan,
  setSelectedPlan,
  planFeatures,
  planPrices,
  errors,
  touched,
  handleInputChange,
  handleSubmit,
  isSubmitting,
  setStep,
}: SignupStep2Props) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="password" className="flex items-center justify-between">
          Password
          {errors.password && touched.password && (
            <span className="text-xs text-destructive flex items-center">
              <AlertCircle className="h-3 w-3 mr-1" />
              {errors.password}
            </span>
          )}
        </Label>
        <Input
          id="password"
          name="password"
          type="password"
          placeholder="Create a password"
          required
          onChange={handleInputChange}
          className={errors.password && touched.password ? "border-destructive" : ""}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="confirmPassword" className="flex items-center justify-between">
          Confirm Password
          {errors.confirmPassword && touched.confirmPassword && (
            <span className="text-xs text-destructive flex items-center">
              <AlertCircle className="h-3 w-3 mr-1" />
              {errors.confirmPassword}
            </span>
          )}
        </Label>
        <Input
          id="confirmPassword"
          name="confirmPassword"
          type="password"
          placeholder="Confirm your password"
          required
          onChange={handleInputChange}
          className={errors.confirmPassword && touched.confirmPassword ? "border-destructive" : ""}
        />
      </div>

      <div className="pt-4">
        <Label>Select a Plan</Label>
        <Tabs value={selectedPlan} onValueChange={setSelectedPlan} className="w-full mt-2">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="free">Free</TabsTrigger>
            <TabsTrigger value="basic">Basic</TabsTrigger>
            <TabsTrigger value="pro">Pro</TabsTrigger>
          </TabsList>

          {Object.keys(planFeatures).map((plan) => (
            <TabsContent key={plan} value={plan} className="mt-2 space-y-2">
              <div className="flex justify-between items-center">
                <span className="font-bold text-lg">{planPrices[plan as keyof typeof planPrices]}</span>
                {plan !== "free" && <Badge variant="outline">Most Popular</Badge>}
              </div>
              <ul className="space-y-1">
                {planFeatures[plan as keyof typeof planFeatures].map((feature, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <CheckCircle2 className="h-3.5 w-3.5 text-primary mr-2 flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            </TabsContent>
          ))}
        </Tabs>
      </div>

      <div className="flex items-center gap-2 pt-2">
        <input type="checkbox" id="terms" className="rounded" required />
        <Label htmlFor="terms" className="text-sm font-normal">
          I agree to the{" "}
          <Link href="/terms" className="text-primary hover:underline">
            Terms of Service
          </Link>{" "}
          and{" "}
          <Link href="/privacy" className="text-primary hover:underline">
            Privacy Policy
          </Link>
        </Label>
      </div>

      <Button type="submit" className="w-full mt-6 py-6 text-lg" disabled={isSubmitting}>
        {isSubmitting ? "Creating Account..." : "Create Account"}
      </Button>
    </div>
  )
}
