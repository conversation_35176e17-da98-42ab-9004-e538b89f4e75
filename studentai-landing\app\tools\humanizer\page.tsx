"use client";

import { useState, useEffect } from "react";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import {
  Wand2,
  ArrowRight,
  Check,
  Copy,
  RotateCcw,
  <PERSON>rkles,
  Settings,
  FileText,
  Clock,
  AlertCircle,
  Zap,
  Crown,
  RefreshCw,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";
import Link from "next/link";

interface HumanizeResult {
  original_length: number;
  humanized_text: string;
  style: string;
  complexity: string;
  processing_time: number;
  processed_at: number;
  remainingCredits: number;
  isPremiumResult: boolean;
}

interface StyleOption {
  id: string;
  name: string;
  description: string;
}

interface ComplexityOption {
  id: string;
  name: string;
  description: string;
}

export default function AIHumanizer() {
  const [inputText, setInputText] = useState("");
  const [result, setResult] = useState<HumanizeResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [copied, setCopied] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState("professional");
  const [selectedComplexity, setSelectedComplexity] = useState("moderate");
  const [styles, setStyles] = useState<StyleOption[]>([]);
  const [complexities, setComplexityOptions] = useState<ComplexityOption[]>([]);
  const [showUpgrade, setShowUpgrade] = useState(false);
  const [remainingCredits, setRemainingCredits] = useState(2);

  // Fetch available options on component mount
  useEffect(() => {
    fetch("/api/humanize")
      .then((res) => res.json())
      .then((data) => {
        setStyles(data.styles || []);
        setComplexityOptions(data.complexities || []);
      })
      .catch((error) => {
        console.error("Failed to fetch options:", error);
      });
  }, []);

  const handleHumanize = async () => {
    if (!inputText.trim()) {
      toast.error("Please enter some text to humanize");
      return;
    }

    if (inputText.length < 50) {
      toast.error("Text must be at least 50 characters long");
      return;
    }

    setIsAnalyzing(true);
    setResult(null);

    try {
      const response = await fetch("/api/humanize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: inputText,
          style: selectedStyle,
          complexity: selectedComplexity,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 429) {
          setShowUpgrade(true);
          toast.error(data.message || "Rate limit exceeded");
          return;
        }
        throw new Error(data.error || "Failed to humanize text");
      }

      setResult(data.result);
      setRemainingCredits(data.result.remainingCredits);
      toast.success("Text successfully humanized!");
    } catch (error) {
      console.error("Error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to humanize text"
      );
    } finally {
      setIsAnalyzing(false);
    }
  };

  const copyToClipboard = () => {
    if (result?.humanized_text) {
      navigator.clipboard.writeText(result.humanized_text);
      setCopied(true);
      toast.success("Humanized text copied to clipboard!");
      setTimeout(() => setCopied(false), 3000);
    }
  };

  const clearText = () => {
    setInputText("");
    setResult(null);
    setCopied(false);
  };

  const characterCount = inputText.length;
  const maxCharacters = 5000;

  return (
    <main className="min-h-screen flex flex-col bg-gradient-to-b from-white to-purple-50 dark:from-gray-900 dark:to-purple-950/30">
      <Navbar />

      {/* Hero Section */}
      <section className="relative pt-20 md:pt-32 pb-8 overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute -top-[30%] -left-[10%] w-[500px] h-[500px] bg-gradient-to-br from-purple-300/20 to-pink-300/20 dark:from-purple-900/20 dark:to-pink-900/20 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-[10%] -right-[10%] w-[400px] h-[400px] bg-gradient-to-tr from-blue-300/20 to-cyan-300/20 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-full blur-3xl"></div>
        </div>

        <div className="container px-4 md:px-6">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="inline-flex items-center justify-center p-2 mb-6 rounded-full bg-purple-100 dark:bg-purple-900/30 backdrop-blur-sm">
                <div className="px-4 py-1.5 rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white text-sm font-medium flex items-center gap-1.5">
                  <Sparkles className="w-4 h-4" />
                  <span>AI Humanizer</span>
                </div>
              </div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-4xl md:text-6xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600 mb-4"
            >
              Transform AI Text to Human
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-8"
            >
              Convert AI-generated content into natural, human-like text that
              bypasses detection
            </motion.p>

            {/* Credits indicator */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-full shadow-sm border border-gray-200 dark:border-gray-700 mb-8"
            >
              <Zap className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {remainingCredits} free humanizations remaining today
              </span>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Tool Section */}
      <section className="flex-1 pb-16">
        <div className="container px-4 md:px-6 max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="space-y-6"
            >
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold flex items-center gap-2">
                    <FileText className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                    Input Text
                  </h2>
                  <button
                    onClick={clearText}
                    className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    title="Clear text"
                  >
                    <RotateCcw className="w-4 h-4 text-gray-500" />
                  </button>
                </div>

                <div className="space-y-4">
                  <Textarea
                    placeholder="Paste your AI-generated text here to humanize it..."
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    className="min-h-[300px] resize-none border-gray-200 dark:border-gray-700 focus:border-purple-500 dark:focus:border-purple-400"
                    maxLength={maxCharacters}
                  />

                  <div className="flex justify-between items-center text-sm">
                    <span
                      className={`${
                        characterCount < 50
                          ? "text-red-500"
                          : characterCount > maxCharacters * 0.9
                          ? "text-amber-500"
                          : "text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      {characterCount}/{maxCharacters} characters
                      {characterCount < 50 && " (minimum 50)"}
                    </span>
                  </div>
                </div>

                {/* Style and Complexity Selection */}
                <div className="grid md:grid-cols-2 gap-4 mt-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Writing Style
                    </label>
                    <select
                      value={selectedStyle}
                      onChange={(e) => setSelectedStyle(e.target.value)}
                      className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 focus:border-purple-500 dark:focus:border-purple-400 focus:outline-none"
                    >
                      {styles.map((style) => (
                        <option key={style.id} value={style.id}>
                          {style.name} - {style.description}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Complexity Level
                    </label>
                    <select
                      value={selectedComplexity}
                      onChange={(e) => setSelectedComplexity(e.target.value)}
                      className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 focus:border-purple-500 dark:focus:border-purple-400 focus:outline-none"
                    >
                      {complexities.map((complexity) => (
                        <option key={complexity.id} value={complexity.id}>
                          {complexity.name} - {complexity.description}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <Button
                  onClick={handleHumanize}
                  disabled={isAnalyzing || characterCount < 50}
                  className="w-full mt-6 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white py-3"
                >
                  {isAnalyzing ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Humanizing...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4 mr-2" />
                      Humanize Text
                    </>
                  )}
                </Button>
              </div>
            </motion.div>

            {/* Output Section */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="space-y-6"
            >
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold flex items-center gap-2">
                    <Sparkles className="w-5 h-5 text-green-600 dark:text-green-400" />
                    Humanized Text
                  </h2>
                  {result && (
                    <button
                      onClick={copyToClipboard}
                      className="flex items-center gap-2 px-3 py-2 rounded-lg bg-green-100 dark:bg-green-900/30 hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors"
                      title="Copy to clipboard"
                    >
                      {copied ? (
                        <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                      ) : (
                        <Copy className="w-4 h-4 text-green-600 dark:text-green-400" />
                      )}
                      <span className="text-sm font-medium text-green-700 dark:text-green-300">
                        {copied ? "Copied!" : "Copy"}
                      </span>
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {result ? (
                    <>
                      <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 min-h-[300px]">
                        <p className="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap">
                          {result.humanized_text}
                        </p>
                      </div>

                      {/* Results Stats */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                            {result.processing_time.toFixed(1)}s
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Processing Time
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                            {result.original_length}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Characters
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600 dark:text-green-400 capitalize">
                            {result.style}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Style
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400 capitalize">
                            {result.complexity}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Complexity
                          </div>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="p-8 text-center text-gray-500 dark:text-gray-400 min-h-[300px] flex flex-col items-center justify-center border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
                      <Wand2 className="w-12 h-12 mb-4 opacity-50" />
                      <p className="text-lg font-medium mb-2">
                        Ready to humanize
                      </p>
                      <p className="text-sm">
                        Enter your AI-generated text and click "Humanize Text"
                        to see the results
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Upgrade Modal */}
      <AnimatePresence>
        {showUpgrade && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
            onClick={() => setShowUpgrade(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 max-w-md w-full border border-gray-200 dark:border-gray-700"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-full flex items-center justify-center">
                  <Crown className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold mb-2">Upgrade to Premium</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  You've reached your daily limit. Upgrade for unlimited
                  humanizations and premium features.
                </p>
                <div className="space-y-3">
                  <Link href="/pricing">
                    <Button className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white">
                      View Pricing Plans
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    onClick={() => setShowUpgrade(false)}
                    className="w-full"
                  >
                    Maybe Later
                  </Button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      <Footer />
    </main>
  );
}
