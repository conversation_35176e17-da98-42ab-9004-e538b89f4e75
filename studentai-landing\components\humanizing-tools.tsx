"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Loader2, Lock, Sparkles, Wand2 } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

interface HumanizingToolsProps {
  text: string
  result: {
    score: number
    highlightedText: string
    suspiciousSections: {
      text: string
      score: number
      startIndex: number
      endIndex: number
    }[]
  }
  userPlan: string
}

export function HumanizingTools({ text, result, userPlan }: HumanizingToolsProps) {
  const [activeTab, setActiveTab] = useState("paraphrase")
  const [humanizedText, setHumanizedText] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [naturalness, setNaturalness] = useState([0.5])
  const [creativity, setCreativity] = useState([0.5])
  const { toast } = useToast()

  const isPremiumFeature = (feature: string) => {
    const featureMap: Record<string, string[]> = {
      free: ["paraphrase"],
      basic: ["paraphrase", "style"],
      pro: ["paraphrase", "style", "enhance", "fix"],
    }

    return !featureMap[userPlan]?.includes(feature)
  }

  const handleHumanize = async () => {
    if (isPremiumFeature(activeTab)) {
      toast({
        title: "Premium feature",
        description: "Please upgrade your plan to access this feature.",
        variant: "default",
      })
      return
    }

    setIsProcessing(true)

    try {
      // In a real app, this would call an API endpoint
      await new Promise((resolve) => setTimeout(resolve, 2000))

      // Mock humanized text - in a real app, this would come from your AI service
      let mockHumanizedText = ""

      switch (activeTab) {
        case "paraphrase":
          mockHumanizedText = `I've reworded the text to sound more natural while keeping the same meaning. The structure has been varied to avoid repetitive patterns that AI detectors might flag.

${text
  .split(". ")
  .map((sentence) => (sentence.length > 0 ? sentence + ". " + (Math.random() > 0.7 ? "Actually, " : "") : ""))
  .join("")}`
          break
        case "style":
          mockHumanizedText = `I've adjusted the writing style to sound more conversational and human-like. I've added some natural variations and inconsistencies.

${text
  .split(". ")
  .map((sentence) =>
    sentence.length > 0 ? (Math.random() > 0.5 ? sentence : sentence.replace(/the/g, "that")) + ". " : "",
  )
  .join("")}`
          break
        case "enhance":
          mockHumanizedText = `I've enhanced the vocabulary and added more nuanced expressions to make the text sound more sophisticated and human-written.

${text
  .split(". ")
  .map((sentence) =>
    sentence.length > 0 ? sentence.replace(/good/g, "excellent").replace(/bad/g, "problematic") + ". " : "",
  )
  .join("")}`
          break
        case "fix":
          mockHumanizedText = `I've fixed the most AI-like sections of your text to help it pass AI detection tools.

${text.replace(result.suspiciousSections[0].text, "This section has been completely rewritten to sound more natural and human-like, with varied sentence structures and more personal expressions.")}`
          break
      }

      setHumanizedText(mockHumanizedText)
    } catch (error) {
      console.error("Error humanizing text:", error)
      toast({
        title: "Humanization failed",
        description: "There was an error processing your text. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-primary" />
            Humanizing Tools
          </CardTitle>
          {userPlan !== "pro" && (
            <Link href="/pricing">
              <Badge variant="outline" className="cursor-pointer w-fit">
                Upgrade for more tools
              </Badge>
            </Link>
          )}
        </div>
        <CardDescription>Transform AI-generated text to sound more human and bypass detection</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="overflow-x-auto pb-2">
            <TabsList className="w-full grid-cols-4 inline-grid min-w-[400px]">
              <TabsTrigger value="paraphrase" disabled={isProcessing}>
                Paraphrase
              </TabsTrigger>
              <TabsTrigger value="style" disabled={isProcessing || isPremiumFeature("style")} className="relative">
                Style Adjuster
                {isPremiumFeature("style") && <Lock className="h-3 w-3 absolute top-1 right-1" />}
              </TabsTrigger>
              <TabsTrigger value="enhance" disabled={isProcessing || isPremiumFeature("enhance")} className="relative">
                Vocabulary
                {isPremiumFeature("enhance") && <Lock className="h-3 w-3 absolute top-1 right-1" />}
              </TabsTrigger>
              <TabsTrigger value="fix" disabled={isProcessing || isPremiumFeature("fix")} className="relative">
                Fix AI Sections
                {isPremiumFeature("fix") && <Lock className="h-3 w-3 absolute top-1 right-1" />}
              </TabsTrigger>
            </TabsList>
          </div>

          <div className="mt-4 space-y-4">
            <TabsContent value="paraphrase">
              <p className="text-sm text-muted-foreground mb-4">
                Rewords your text to maintain the same meaning while making it sound more natural and human-written.
              </p>
            </TabsContent>
            <TabsContent value="style">
              <p className="text-sm text-muted-foreground mb-4">
                Adjusts the writing style to match common human patterns with natural variations and inconsistencies.
              </p>
            </TabsContent>
            <TabsContent value="enhance">
              <p className="text-sm text-muted-foreground mb-4">
                Enhances vocabulary and adds nuanced expressions to make the text sound more sophisticated.
              </p>
            </TabsContent>
            <TabsContent value="fix">
              <p className="text-sm text-muted-foreground mb-4">
                Specifically targets and rewrites sections that are most likely to be flagged by AI detectors.
              </p>
            </TabsContent>

            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="naturalness">Naturalness</Label>
                  <span className="text-sm text-muted-foreground">{Math.round(naturalness[0] * 100)}%</span>
                </div>
                <Slider
                  id="naturalness"
                  min={0}
                  max={1}
                  step={0.01}
                  value={naturalness}
                  onValueChange={setNaturalness}
                  disabled={isPremiumFeature(activeTab)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label htmlFor="creativity">Creativity</Label>
                  <span className="text-sm text-muted-foreground">{Math.round(creativity[0] * 100)}%</span>
                </div>
                <Slider
                  id="creativity"
                  min={0}
                  max={1}
                  step={0.01}
                  value={creativity}
                  onValueChange={setCreativity}
                  disabled={isPremiumFeature(activeTab)}
                />
              </div>

              <Button
                onClick={handleHumanize}
                disabled={isProcessing || isPremiumFeature(activeTab)}
                className="w-full"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : isPremiumFeature(activeTab) ? (
                  <>
                    <Lock className="mr-2 h-4 w-4" />
                    Upgrade to Access
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Humanize Text
                  </>
                )}
              </Button>
            </div>

            {humanizedText && !isProcessing && (
              <div className="mt-4">
                <Label htmlFor="humanized-text">Humanized Result</Label>
                <Textarea id="humanized-text" value={humanizedText} readOnly className="mt-2 min-h-[200px]" />
              </div>
            )}
          </div>
        </Tabs>
      </CardContent>
      <CardFooter className="flex flex-col sm:flex-row sm:justify-between border-t pt-4 gap-4">
        <p className="text-xs text-muted-foreground">
          {userPlan === "free"
            ? "Free plan: Basic paraphrasing only"
            : userPlan === "basic"
              ? "Basic plan: Paraphrasing and style adjustment"
              : "Pro plan: All humanizing tools unlocked"}
        </p>
        {userPlan !== "pro" && (
          <Link href="/pricing">
            <Button variant="outline" size="sm">
              Upgrade Plan
            </Button>
          </Link>
        )}
      </CardFooter>
    </Card>
  )
}
