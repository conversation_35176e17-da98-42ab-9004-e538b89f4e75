"use client";

import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>ircle,
  ArrowRight,
  <PERSON>rkles,
  Clock,
  Award,
  Coffee,
  X,
  <PERSON>Check,
  BarChart2,
  ShieldCheck,
} from "lucide-react";
import { useState, useEffect } from "react";

export default function TransformationSection() {
  const [isVisible, setIsVisible] = useState(false);

  // Intersection Observer to trigger animations when section is visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = document.getElementById("transformation-section");
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  return (
    <section
      id="transformation-section"
      className="w-full py-16 md:py-28 bg-gradient-to-b from-white to-blue-50 dark:from-gray-800 dark:to-gray-900 overflow-hidden relative"
    >
      {/* Enhanced animated background elements */}
      <div
        className="absolute left-0 top-20 w-64 h-64 bg-green-100/20 dark:bg-green-900/10 rounded-full blur-3xl -z-10 animate-float"
        style={{ animationDuration: "15s" }}
      />
      <div
        className="absolute right-20 bottom-40 w-80 h-80 bg-blue-100/20 dark:bg-blue-900/10 rounded-full blur-3xl -z-10 animate-float-slow"
        style={{ animationDuration: "18s", animationDelay: "2s" }}
      />
      <div
        className="absolute left-1/3 top-1/2 w-48 h-48 bg-purple-100/10 dark:bg-purple-900/5 rounded-full blur-3xl -z-10 animate-float"
        style={{ animationDuration: "20s", animationDelay: "1s" }}
      />

      {/* Subtle animated particles */}
      <div className="absolute inset-0 -z-5 opacity-30 dark:opacity-20">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-green-400/20 dark:bg-green-400/10"
            style={{
              top: `${30 + Math.random() * 40}%`,
              left: `${Math.random() * 100}%`,
              width: `${Math.random() * 6 + 2}px`,
              height: `${Math.random() * 6 + 2}px`,
              animation: `float-vertical ${
                Math.random() * 10 + 15
              }s infinite alternate ease-in-out`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          ></div>
        ))}
      </div>

      <div className="container mx-auto px-4 md:px-6 relative">
        {/* Enhanced section header with visual accents */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 mb-2 px-4 py-1.5 bg-green-100/90 dark:bg-green-900/40 backdrop-blur-sm rounded-full animate-fade-in border border-green-200/40 dark:border-green-700/40 shadow-sm">
            <Sparkles className="h-4 w-4 text-green-500 dark:text-green-400" />
            <span className="text-sm font-medium text-green-700 dark:text-green-300">
              Transformative Solution
            </span>
          </div>

          <h2
            className={`text-3xl md:text-4xl lg:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-white dark:via-gray-100 dark:to-white max-w-3xl mx-auto ${
              isVisible ? "animate-reveal" : "opacity-0"
            }`}
            style={{ animationDelay: "0.2s" }}
          >
            Transform Your Teaching Experience
          </h2>

          <p
            className={`max-w-[800px] mx-auto mt-4 text-gray-600 dark:text-gray-300 md:text-xl/relaxed ${
              isVisible ? "animate-reveal" : "opacity-0"
            }`}
            style={{ animationDelay: "0.4s" }}
          >
            Imagine a world where you spend less time worrying about academic
            integrity and more time doing what you love—teaching.
          </p>

          <div
            className={`h-1.5 bg-gradient-to-r from-green-500 to-blue-500 w-[80px] mx-auto mt-6 rounded-full ${
              isVisible ? "animate-reveal" : "opacity-0"
            }`}
            style={{ animationDelay: "0.6s" }}
          />
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Right column: Before & After transformation visuals with interactive elements */}
          <div
            className={`order-1 md:order-2 ${
              isVisible ? "animate-fade-in" : "opacity-0"
            }`}
            style={{ animationDelay: "0.3s" }}
          >
            <div className="relative">
              {/* Enhanced glow effect */}
              <div className="absolute -z-10 w-full h-full bg-gradient-to-br from-blue-100/50 via-green-100/40 to-transparent dark:from-blue-900/30 dark:via-green-900/20 blur-xl transform translate-x-4 translate-y-4 rounded-3xl" />

              <div className="relative z-10">
                <div className="p-1 bg-gradient-to-r from-red-500/40 to-amber-500/40 dark:from-red-700/60 dark:to-amber-700/60 rounded-2xl mb-6 shadow-lg">
                  <div className="bg-white dark:bg-gray-900 rounded-xl p-6 backdrop-blur-sm">
                    <div className="flex items-center justify-center gap-2 mb-6">
                      <div className="p-1.5 rounded-full bg-green-100 dark:bg-green-900/50">
                        <ShieldCheck className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 dark:text-white">
                        The Transformation Journey
                      </h3>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Before card with animated pulse */}
                      <div className="bg-gradient-to-br from-red-50 to-red-100/50 dark:from-red-900/30 dark:to-red-900/10 p-6 rounded-lg border border-red-200 dark:border-red-800/30 shadow-md animate-pulse-slow relative overflow-hidden group">
                        <div className="absolute inset-0 bg-red-500/5 dark:bg-red-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                        <div className="flex justify-between items-start mb-4">
                          <span className="text-sm font-bold text-red-600 dark:text-red-400 uppercase tracking-wider">
                            Before
                          </span>
                          <X className="h-5 w-5 text-red-500 dark:text-red-400" />
                        </div>

                        <h4 className="text-lg font-bold text-gray-800 dark:text-white mb-3">
                          Time-Consuming Process
                        </h4>

                        <ul className="space-y-3 text-sm">
                          <li className="flex items-start gap-2">
                            <div className="mt-1 min-w-4">
                              <Clock className="h-4 w-4 text-red-500 dark:text-red-400" />
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">
                              Hours spent manually checking suspicious
                              assignments
                            </span>
                          </li>
                          <li className="flex items-start gap-2">
                            <div className="mt-1 min-w-4">
                              <Clock className="h-4 w-4 text-red-500 dark:text-red-400" />
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">
                              Inconsistent detection of AI content
                            </span>
                          </li>
                          <li className="flex items-start gap-2">
                            <div className="mt-1 min-w-4">
                              <Clock className="h-4 w-4 text-red-500 dark:text-red-400" />
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">
                              Constant worry about academic dishonesty
                            </span>
                          </li>
                        </ul>

                        {/* Visual indicator */}
                        <div className="mt-4 w-full bg-red-200 dark:bg-red-900/30 h-2 rounded-full overflow-hidden">
                          <div
                            className="bg-red-500 dark:bg-red-600 h-full"
                            style={{ width: "30%" }}
                          ></div>
                        </div>
                        <div className="mt-1 text-xs text-right text-red-600 dark:text-red-400">
                          30% Efficiency
                        </div>
                      </div>

                      {/* After card with animated effects */}
                      <div className="bg-gradient-to-br from-green-50 to-blue-50/50 dark:from-green-900/30 dark:to-blue-900/20 p-6 rounded-lg border border-green-200 dark:border-green-800/30 shadow-lg relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-blue-500/5 dark:from-green-500/10 dark:to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                        <div className="flex justify-between items-start mb-4">
                          <span className="text-sm font-bold text-green-600 dark:text-green-400 uppercase tracking-wider">
                            After
                          </span>
                          <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                        </div>

                        <h4 className="text-lg font-bold text-gray-800 dark:text-white mb-3">
                          Streamlined Efficiency
                        </h4>

                        <ul className="space-y-3 text-sm">
                          <li className="flex items-start gap-2">
                            <div className="mt-1 min-w-4">
                              <FileCheck className="h-4 w-4 text-green-500 dark:text-green-400" />
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">
                              Instant AI content detection in seconds
                            </span>
                          </li>
                          <li className="flex items-start gap-2">
                            <div className="mt-1 min-w-4">
                              <BarChart2 className="h-4 w-4 text-green-500 dark:text-green-400" />
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">
                              99.4% accuracy with detailed reports
                            </span>
                          </li>
                          <li className="flex items-start gap-2">
                            <div className="mt-1 min-w-4">
                              <Coffee className="h-4 w-4 text-green-500 dark:text-green-400" />
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">
                              More time for meaningful teaching
                            </span>
                          </li>
                        </ul>

                        {/* Visual indicator with animation */}
                        <div className="mt-4 w-full bg-green-200 dark:bg-green-900/30 h-2 rounded-full overflow-hidden">
                          <div
                            className={`bg-green-500 dark:bg-green-600 h-full ${
                              isVisible ? "animate-expand-width" : "w-0"
                            }`}
                            style={{ animationFillMode: "forwards" }}
                          ></div>
                        </div>
                        <div className="mt-1 text-xs text-right text-green-600 dark:text-green-400">
                          95% Efficiency
                        </div>
                      </div>
                    </div>

                    {/* Results metrics with animated counting */}
                    <div className="mt-8 grid grid-cols-3 gap-4 border-t border-gray-100 dark:border-gray-800 pt-6">
                      <div className="text-center">
                        <div
                          className={`text-2xl font-bold text-blue-600 dark:text-blue-400 ${
                            isVisible ? "animate-count" : ""
                          }`}
                          data-value="95"
                        >
                          95%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Time Saved
                        </div>
                      </div>
                      <div className="text-center">
                        <div
                          className={`text-2xl font-bold text-blue-600 dark:text-blue-400 ${
                            isVisible ? "animate-count" : ""
                          }`}
                          data-value="99.4"
                        >
                          99.4%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Detection Accuracy
                        </div>
                      </div>
                      <div className="text-center">
                        <div
                          className={`text-2xl font-bold text-blue-600 dark:text-blue-400 ${
                            isVisible ? "animate-count" : ""
                          }`}
                          data-value="87"
                        >
                          87%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Increased Focus
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Left column: Benefits with elegant styling */}
          <div
            className={`space-y-6 order-2 md:order-1 ${
              isVisible ? "animate-fade-in" : "opacity-0"
            }`}
            style={{ animationDelay: "0.5s" }}
          >
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
              <Award className="h-6 w-6 text-blue-500 dark:text-blue-400" />
              <span>How Will Your Work Transform?</span>
            </h3>

            <div className="space-y-4">
              {[
                {
                  title: "From Doubt to Confidence",
                  description:
                    "Stop second-guessing your assessments. Our tool gives you objective, data-backed insights into content authenticity.",
                  icon: ShieldCheck,
                  color: "blue",
                },
                {
                  title: "From Hours to Seconds",
                  description:
                    "What once took hours of careful reading now takes mere seconds with our advanced AI detection technology.",
                  icon: Clock,
                  color: "green",
                },
                {
                  title: "From Frustration to Focus",
                  description:
                    "Shift your energy from policing to mentoring. Spend more time on what matters—developing your students' skills.",
                  icon: Award,
                  color: "purple",
                },
              ].map((item, i) => (
                <div
                  key={i}
                  className={`p-6 rounded-xl bg-white dark:bg-gray-800/80 shadow-md border border-gray-100 dark:border-gray-700/50 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 group ${
                    isVisible ? "animate-fade-in" : "opacity-0"
                  }`}
                  style={{ animationDelay: `${0.6 + i * 0.2}s` }}
                >
                  <div className="flex gap-4">
                    <div
                      className={`shrink-0 p-2 rounded-lg bg-${item.color}-100 dark:bg-${item.color}-900/30 group-hover:bg-${item.color}-200 dark:group-hover:bg-${item.color}-800/40 transition-colors duration-300`}
                    >
                      <item.icon
                        className={`h-6 w-6 text-${item.color}-600 dark:text-${item.color}-400`}
                      />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {item.title}
                      </h4>
                      <p className="text-gray-600 dark:text-gray-300">
                        {item.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA button */}
            <div
              className={`mt-8 ${isVisible ? "animate-fade-in" : "opacity-0"}`}
              style={{ animationDelay: "1.2s" }}
            >
              <Button
                onClick={() =>
                  window.open(
                    "https://app.studentaidetector.com/signup",
                    "_blank"
                  )
                }
                size="lg"
                className="text-base px-8 py-6 bg-gradient-to-r from-green-500 via-green-600 to-blue-600 hover:from-green-600 hover:via-green-700 hover:to-blue-700 text-white shadow-lg hover:shadow-green-500/25 group transition-all duration-300 ease-out transform hover:-translate-y-1 hover:shadow-xl rounded-lg"
              >
                See It In Action
                <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Additional animation styles */}
      <style jsx global>{`
        @keyframes expand-width {
          from {
            width: 0;
          }
          to {
            width: 95%;
          }
        }

        .animate-expand-width {
          animation: expand-width 1.5s ease-out forwards;
        }

        @keyframes count {
          from {
            content: "0";
          }
          to {
            content: attr(data-value);
          }
        }

        .animate-count {
          animation: count 2s forwards;
          counter-reset: count 0;
        }

        .animate-count::after {
          content: counter(count);
          animation: count 2s forwards;
        }
      `}</style>
    </section>
  );
}
