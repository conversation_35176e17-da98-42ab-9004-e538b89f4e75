import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Check, Code, Zap, Lock, X } from "lucide-react"
import Link from "next/link"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

export function ApiPricing() {
  const apiPlans = [
    {
      name: "Starter API",
      price: 49,
      requests: "5,000",
      features: [
        "Basic AI detection endpoints",
        "5,000 words per request",
        "Standard rate limiting",
        "Basic authentication",
        "Community support",
      ],
    },
    {
      name: "Professional API",
      price: 149,
      requests: "20,000",
      features: [
        "All detection endpoints",
        "15,000 words per request",
        "Higher rate limits",
        "API key management",
        "Email support",
        "Basic analytics",
      ],
    },
    {
      name: "Enterprise API",
      price: "Custom",
      requests: "Custom",
      features: [
        "Custom endpoints",
        "Unlimited words per request",
        "Custom rate limits",
        "Advanced security options",
        "Dedicated support",
        "Advanced analytics & reporting",
      ],
    },
  ]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">API Access</h2>
        <p className="text-muted-foreground mt-2">
          Integrate AI detection capabilities directly into your applications
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {apiPlans.map((plan) => (
          <Card key={plan.name} className="flex flex-col">
            <CardHeader>
              <CardTitle>{plan.name}</CardTitle>
              <div className="mt-4 flex items-baseline">
                <span className="text-3xl font-bold">
                  {typeof plan.price === "number" ? `$${plan.price}` : plan.price}
                </span>
                <span className="ml-1 text-sm text-muted-foreground">/month</span>
              </div>
              <CardDescription className="mt-2">{plan.requests} requests per month</CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-2">
                {plan.features.map((feature) => (
                  <li key={feature} className="flex items-start gap-2">
                    <Check className="h-4 w-4 mt-0.5 text-primary" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter>
              <Link href="/api/docs" className="w-full">
                <Button variant="outline" className="w-full">
                  Learn More
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            API Endpoints
          </CardTitle>
          <CardDescription>Available endpoints and their capabilities</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Endpoint</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Starter</TableHead>
                <TableHead>Professional</TableHead>
                <TableHead>Enterprise</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-mono text-sm">/detect</TableCell>
                <TableCell>Basic AI detection analysis</TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-mono text-sm">/analyze</TableCell>
                <TableCell>Detailed analysis with suspicious sections</TableCell>
                <TableCell>
                  <X className="h-4 w-4 text-muted-foreground mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-mono text-sm">/humanize</TableCell>
                <TableCell>Text humanization capabilities</TableCell>
                <TableCell>
                  <X className="h-4 w-4 text-muted-foreground mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-mono text-sm">/batch</TableCell>
                <TableCell>Process multiple texts in one request</TableCell>
                <TableCell>
                  <X className="h-4 w-4 text-muted-foreground mx-auto" />
                </TableCell>
                <TableCell>
                  <X className="h-4 w-4 text-muted-foreground mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-mono text-sm">/custom</TableCell>
                <TableCell>Custom detection models and parameters</TableCell>
                <TableCell>
                  <X className="h-4 w-4 text-muted-foreground mx-auto" />
                </TableCell>
                <TableCell>
                  <X className="h-4 w-4 text-muted-foreground mx-auto" />
                </TableCell>
                <TableCell>
                  <Check className="h-4 w-4 text-primary mx-auto" />
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter className="flex flex-col items-start gap-4">
          <div className="flex items-start gap-2">
            <Zap className="h-5 w-5 text-amber-500 mt-0.5" />
            <div>
              <p className="font-medium">Additional API Requests</p>
              <p className="text-sm text-muted-foreground">
                Additional requests beyond your plan limit are billed at $0.01 per request for Starter and $0.008 per
                request for Professional plans.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <Lock className="h-5 w-5 text-primary mt-0.5" />
            <div>
              <p className="font-medium">API Security</p>
              <p className="text-sm text-muted-foreground">
                All API access is secured with API keys and optional IP restrictions. Enterprise plans include
                additional security features.
              </p>
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
