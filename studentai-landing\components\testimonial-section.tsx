"use client";

import React from "react";
import { motion } from "framer-motion";
import { Star } from "lucide-react";
import Image from "next/image";

export default function TestimonialSection() {
  return (
    <section className="py-24 relative overflow-hidden">
      {/* Simple background */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-white via-blue-50/20 to-white dark:from-gray-900 dark:via-blue-900/5 dark:to-gray-900"></div>

      <div className="container mx-auto px-4">
        {/* Section header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            What Our Users Say
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Join the community of educators and institutions using our AI
            detection technology.
          </p>
        </div>

        {/* Image and stats */}
        <div className="max-w-5xl mx-auto">
          <div className="relative mb-16">
            {/* Main image */}
            <div className="rounded-2xl overflow-hidden shadow-xl">
              <div className="relative aspect-[16/9]">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Testimonials-1-xDWEq46ZaMS6VeS1kTjmqRYVKEdODk.png"
                  alt="Students and educators using AI detection technology"
                  fill
                  className="object-cover object-center"
                  priority
                />

                {/* Rating overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/50 to-transparent pt-20 pb-6 px-6">
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className="h-5 w-5 fill-current text-yellow-400"
                        />
                      ))}
                    </div>
                    <span className="text-white font-medium">4.9/5</span>
                  </div>
                  <p className="text-white font-medium mt-2">
                    Trusted by 500+ educational institutions worldwide
                  </p>
                </div>
              </div>
            </div>

            {/* Simple stat cards - overlay positions */}
            <div className="absolute -bottom-6 -left-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 z-30">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-blue-600 dark:text-blue-400">
                  98%
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Satisfaction rate
                </div>
              </div>
            </div>

            <div className="absolute -top-6 -right-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 z-30">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-indigo-600 dark:text-indigo-400">
                  25k+
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Active users
                </div>
              </div>
            </div>
          </div>

          {/* Simple key stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 mt-12">
            {[
              { label: "Countries", value: "50+", color: "blue" },
              { label: "Institutions", value: "500+", color: "indigo" },
              { label: "Accuracy", value: "99.4%", color: "violet" },
              { label: "Time Saved", value: "2.5 hrs/week", color: "green" },
            ].map((stat, i) => (
              <div
                key={i}
                className="text-center p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-100 dark:border-gray-700"
              >
                <div
                  className={`text-xl font-bold mb-1 ${
                    stat.color === "blue"
                      ? "text-blue-600 dark:text-blue-400"
                      : stat.color === "indigo"
                      ? "text-indigo-600 dark:text-indigo-400"
                      : stat.color === "violet"
                      ? "text-violet-600 dark:text-violet-400"
                      : "text-green-600 dark:text-green-400"
                  }`}
                >
                  {stat.value}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
