import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export function BlogList() {
  const blogPosts = [
    {
      title: "How to Tell if a Student Used ChatGPT: A Guide for Educators",
      excerpt:
        "Learn the telltale signs of AI-generated content and how to identify when students are using ChatGPT for assignments.",
      slug: "how-to-tell-if-student-used-chatgpt",
      date: "March 28, 2025",
      category: "For Educators",
      image: "/blog/chatgpt-detection.jpg",
    },
    {
      title: "AI Detection vs. Plagiarism Checking: What's the Difference?",
      excerpt:
        "Understand how AI detection differs from traditional plagiarism checking and why both are important for academic integrity.",
      slug: "ai-detection-vs-plagiarism-checking",
      date: "March 25, 2025",
      category: "Academic Integrity",
      image: "/blog/plagiarism-vs-ai.jpg",
    },
    {
      title: "5 Ways to Make AI-Generated Content Sound More Human",
      excerpt: "Practical tips for improving AI-written text to sound more natural and pass AI detection tools.",
      slug: "make-ai-content-sound-human",
      date: "March 20, 2025",
      category: "Writing Tips",
      image: "/blog/humanize-ai-text.jpg",
    },
    {
      title: "Will Google Penalize AI-Generated Content? SEO Facts vs. Myths",
      excerpt: "Explore the truth about how search engines treat AI-written content and best practices for SEO.",
      slug: "google-ai-content-seo-facts-myths",
      date: "March 15, 2025",
      category: "SEO",
      image: "/blog/ai-seo.jpg",
    },
    {
      title: "The Ethics of AI Writing Tools in Education",
      excerpt:
        "A balanced discussion on using AI writing assistants in academic settings and establishing ethical guidelines.",
      slug: "ethics-ai-writing-tools-education",
      date: "March 10, 2025",
      category: "Ethics",
      image: "/blog/ai-ethics.jpg",
    },
    {
      title: "How to Use AI Responsibly for Academic Writing",
      excerpt:
        "Guidelines for students on leveraging AI tools while maintaining academic integrity and developing writing skills.",
      slug: "use-ai-responsibly-academic-writing",
      date: "March 5, 2025",
      category: "For Students",
      image: "/blog/responsible-ai.jpg",
    },
  ]

  return (
    <div className="grid gap-8 md:grid-cols-2">
      {blogPosts.map((post) => (
        <Link key={post.slug} href={`/blog/${post.slug}`} className="block group">
          <Card className="overflow-hidden h-full transition-all hover:shadow-md">
            <div className="aspect-video relative overflow-hidden">
              <div className="absolute inset-0 bg-muted/20" />
              <div className="w-full h-full bg-muted" />
            </div>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-3">
                <Badge variant="secondary" className="text-xs font-normal">
                  {post.category}
                </Badge>
                <span className="text-xs text-muted-foreground">{post.date}</span>
              </div>
              <h2 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors">{post.title}</h2>
              <p className="text-muted-foreground">{post.excerpt}</p>
            </CardContent>
            <CardFooter className="pt-0 pb-6 px-6">
              <span className="text-sm font-medium text-primary">Read more</span>
            </CardFooter>
          </Card>
        </Link>
      ))}
    </div>
  )
}
