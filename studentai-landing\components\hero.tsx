import { Button } from "@/components/ui/button"
import Link from "next/link"

export function Hero() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 bg-blue-50/40">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">AI Detection & Humanization</h1>
            <h2 className="text-2xl font-bold tracking-tighter sm:text-3xl md:text-4xl lg:text-5xl text-accent">
              For Academic Integrity & Better Writing
            </h2>
          </div>
          <p className="max-w-[800px] text-muted-foreground md:text-xl">
            StudentAIDetector helps educators identify AI-generated content while giving students and writers tools to
            improve their writing. Our dual approach ensures content is authentic, natural, and effective.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 min-[400px]:gap-2">
            <Button size="lg" className="px-8">
              Try AI Detector
            </Button>
            <Button size="lg" variant="outline" className="px-8">
              <Link href="/tools/humanization">Humanize Text</Link>
            </Button>
          </div>
          <div className="flex items-center justify-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center">
              <div className="h-2 w-2 rounded-full bg-primary mr-2"></div>
              <span>90%+ Detection Accuracy</span>
            </div>
            <div className="flex items-center">
              <div className="h-2 w-2 rounded-full bg-primary mr-2"></div>
              <span>Used by 500+ Institutions</span>
            </div>
            <div className="flex items-center">
              <div className="h-2 w-2 rounded-full bg-primary mr-2"></div>
              <span>Free Plan Available</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
