"use client"

import { useState, useEffect } from "react"

export function CreditDisplay() {
  const [credits, setCredits] = useState(0)

  useEffect(() => {
    const storedUser = localStorage.getItem("user")
    if (storedUser) {
      const userData = JSON.parse(storedUser)
      setCredits(userData.credits || 0)
    }
  }, [])

  return <div className="bg-primary text-primary-foreground py-2 px-4 text-sm flex justify-end">Credits: {credits}</div>
}
