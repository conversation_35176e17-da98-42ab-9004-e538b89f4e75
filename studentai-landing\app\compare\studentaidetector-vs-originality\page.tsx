import { FeatureComparison } from "@/components/comparison/feature-comparison";
import { PricingComparison } from "@/components/comparison/pricing-comparison";
import { Metadata } from "next";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ArrowRight } from "lucide-react";

export const metadata: Metadata = {
  title: "StudentAIDetector vs Originality.ai | Feature & Pricing Comparison",
  description:
    "Compare StudentAIDetector with Originality.ai. See how our advanced AI detection features, pricing, and accuracy stack up against Originality.ai.",
};

export default function OriginalityComparison() {
  return (
    <div className="container px-4 py-12 mx-auto max-w-7xl">
      <div className="mb-8">
        <Link
          href="/compare"
          className="inline-flex items-center text-sm text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Back to comparisons
        </Link>

        <h1 className="mt-4 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
          StudentAIDetector vs Originality.ai
        </h1>
        <p className="mt-4 text-xl text-gray-600 dark:text-gray-400">
          See how StudentAIDetector compares to Originality.ai on features,
          accuracy, and pricing.
        </p>
      </div>

      <div className="my-12 space-y-16">
        {/* Feature Comparison Section */}
        <section id="features" className="scroll-mt-24">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl mb-8">
            Feature Comparison
          </h2>
          <FeatureComparison initialCompetitor="originality" />
        </section>

        {/* Pricing Comparison Section */}
        <section id="pricing" className="scroll-mt-24">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl mb-8">
            Pricing Comparison
          </h2>
          <PricingComparison initialCompetitor="originality" />
        </section>

        {/* Benefits/Value Proposition Section */}
        <section className="bg-blue-50 dark:bg-blue-900/10 rounded-2xl p-8 my-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Why Choose StudentAIDetector Over Originality.ai?
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3">
                Better Accuracy
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Our specialized algorithms provide higher accuracy with
                significantly lower false positive rates than Originality.ai.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3">
                More Cost Effective
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Subscription-based pricing rather than expensive credit-based
                system, giving you better value for educational needs.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3">
                Education-Focused
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Built specifically for educators with features tailored to
                academic environments and seamless LMS integration.
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="text-center py-8">
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
            <Link href="/signup" className="flex items-center">
              Try StudentAIDetector for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
            No credit card required. Start with our free plan today.
          </p>
        </div>
      </div>
    </div>
  );
}
