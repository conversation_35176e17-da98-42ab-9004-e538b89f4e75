import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@/components/ui/accordion"

export function Faq() {
  const faqs = [
    {
      question: "How accurate is the AI detector?",
      answer:
        "Our AI detection algorithms are continuously improved and currently achieve over 90% accuracy in identifying AI-generated content from major models like ChatGPT, GPT-4, <PERSON>, and others. We use a multi-layered approach that analyzes patterns, linguistic features, and contextual elements to provide reliable results.",
    },
    {
      question: "Can the AI detector identify content from all AI models?",
      answer:
        "Our system is designed to detect content from popular AI models including ChatGPT, GPT-4, <PERSON>, <PERSON><PERSON>, and many others. We regularly update our detection algorithms to keep pace with new AI models and improvements to existing ones.",
    },
    {
      question: "Will the detector flag human-written content as AI-generated?",
      answer:
        "We've optimized our system to minimize false positives. However, no AI detection system is perfect. In rare cases, highly formal or technical human writing might show patterns similar to AI-generated text. That's why we provide confidence scores and highlight specific sections, allowing you to make informed judgments.",
    },
    {
      question: "How many words can I analyze at once?",
      answer:
        "The word limit depends on your plan. Free users can analyze up to 1,500 words per query, Basic plan users up to 5,000 words, and Pro plan users up to 15,000 words. For longer documents, you can use our batch processing feature or split the text into multiple analyses.",
    },
    {
      question: "Can I use the AI detector for languages other than English?",
      answer:
        "Currently, our AI detector is optimized for English text. While it may work to some extent with other languages, the accuracy will be significantly lower. We're working on expanding our language support in future updates.",
    },
    {
      question: "How does the batch processing feature work?",
      answer:
        "Batch processing allows you to analyze multiple documents at once. Basic plan users can process up to 5 files simultaneously, while Pro plan users can process up to 20 files. This feature is ideal for educators checking multiple student submissions or content managers reviewing multiple articles.",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold tracking-tight mb-2">Frequently Asked Questions</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Find answers to common questions about our AI detection tool
        </p>
      </div>

      <div className="max-w-3xl mx-auto">
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
              <AccordionContent>{faq.answer}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  )
}
