"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDes<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"

export function InstitutionalPricing() {
  return (
    <section className="py-12 md:py-24">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">Academic Institution Packages</h2>
          <p className="text-muted-foreground text-lg max-w-3xl mx-auto mt-4">
            Customized plans for departments, schools, and universities with enhanced features and dedicated support.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Department</CardTitle>
              <CardDescription>For small departments</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-3xl font-bold">$249/month</div>
              <ul className="list-disc list-inside space-y-1">
                <li>10 educator accounts</li>
                <li>5,000 credits/month</li>
                <li>LMS integration</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>School</CardTitle>
              <CardDescription>For small schools</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-3xl font-bold">$599/month</div>
              <ul className="list-disc list-inside space-y-1">
                <li>25 educator accounts</li>
                <li>15,000 credits/month</li>
                <li>LMS integration</li>
                <li>API access</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>University</CardTitle>
              <CardDescription>For large universities</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="text-3xl font-bold">$1,499/month</div>
              <ul className="list-disc list-inside space-y-1">
                <li>100 educator accounts</li>
                <li>50,000 credits/month</li>
                <li>All features</li>
                <li>Custom reporting</li>
              </ul>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <p className="text-muted-foreground mb-4">
            Need a custom plan? Contact us to discuss your specific requirements.
          </p>
          <Link href="/contact">
            <Button>Contact Sales</Button>
          </Link>
        </div>
      </div>
    </section>
  )
}
