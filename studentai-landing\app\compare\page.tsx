import Link from "next/link";
import type { Metada<PERSON> } from "next";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  <PERSON>Right,
  BarChart2,
  ArrowUpDown,
  LineChart,
  Scale,
} from "lucide-react";

export const metadata: Metadata = {
  title:
    "AI Detector Comparison | StudentAIDetector vs Other AI Detection Tools",
  description:
    "Compare StudentAIDetector with GPTZero, Turnitin, Originality.AI and other AI detection tools. See feature-by-feature comparisons, pricing, and accuracy rates.",
  keywords:
    "AI detector comparison, StudentAIDetector vs GPTZero, AI detection tools comparison, Turnitin alternative, Originality.AI comparison",
};

export default function ComparisonHubPage() {
  return (
    <>
      <Navbar />
      <div className="container mx-auto px-4 py-12 pt-28">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Compare <span className="text-primary">StudentAIDetector</span>{" "}
              with Other AI Detection Tools
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              See how we stack up against leading AI detection platforms
            </p>
          </div>

          <div className="prose dark:prose-invert max-w-none mb-12">
            <p className="text-lg mb-6">
              Looking for the right AI detection tool for your needs? Our
              comprehensive comparison guides help you understand how
              StudentAIDetector stacks up against other popular AI content
              detectors and humanizers. We provide objective, feature-by-feature
              comparisons to help you make an informed decision.
            </p>

            <p className="mb-8">
              Each comparison includes detailed analysis of detection accuracy,
              supported AI models, humanization capabilities, pricing, and use
              case scenarios. We highlight both strengths and limitations of
              each tool to give you a complete picture.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-t-4 border-blue-500">
                <div className="bg-blue-50 dark:bg-blue-950/20 p-3 flex items-center gap-2">
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-md">
                    <BarChart2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    StudentAIDetector vs GPTZero
                  </h2>
                </div>
                <CardContent className="pt-6">
                  <p className="text-muted-foreground mb-6">
                    Compare our dual-function AI detector and humanizer against
                    GPTZero's detection-only platform. See how we stack up on
                    accuracy, features, and value for educators and students.
                  </p>
                  <Button
                    variant="ghost"
                    className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 p-0 h-auto"
                    asChild
                  >
                    <Link
                      href="/compare/studentaidetector-vs-gptzero"
                      className="flex items-center"
                    >
                      Read the full comparison
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Link>
                  </Button>
                </CardContent>
              </div>
            </Card>

            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-t-4 border-indigo-500">
                <div className="bg-indigo-50 dark:bg-indigo-950/20 p-3 flex items-center gap-2">
                  <div className="bg-indigo-100 dark:bg-indigo-900/30 p-1.5 rounded-md">
                    <ArrowUpDown className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    StudentAIDetector vs Turnitin
                  </h2>
                </div>
                <CardContent className="pt-6">
                  <p className="text-muted-foreground mb-6">
                    See how our affordable AI detection solution compares to
                    Turnitin's institutional platform. Compare features, pricing
                    models, and integration capabilities.
                  </p>
                  <Button
                    variant="ghost"
                    className="text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 p-0 h-auto"
                    asChild
                  >
                    <Link
                      href="/compare/studentaidetector-vs-turnitin"
                      className="flex items-center"
                    >
                      Read the full comparison
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Link>
                  </Button>
                </CardContent>
              </div>
            </Card>

            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-t-4 border-purple-500">
                <div className="bg-purple-50 dark:bg-purple-950/20 p-3 flex items-center gap-2">
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-1.5 rounded-md">
                    <LineChart className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    StudentAIDetector vs Originality.AI
                  </h2>
                </div>
                <CardContent className="pt-6">
                  <p className="text-muted-foreground mb-6">
                    Compare our academic-focused solution with Originality.AI's
                    content marketing platform. See which tool offers better
                    value for different use cases.
                  </p>
                  <Button
                    variant="ghost"
                    className="text-purple-600 hover:text-purple-800 hover:bg-purple-50 dark:hover:bg-purple-900/20 p-0 h-auto"
                    asChild
                  >
                    <Link
                      href="/compare/studentaidetector-vs-originality"
                      className="flex items-center"
                    >
                      Read the full comparison
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Link>
                  </Button>
                </CardContent>
              </div>
            </Card>

            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-t-4 border-teal-500">
                <div className="bg-teal-50 dark:bg-teal-950/20 p-3 flex items-center gap-2">
                  <div className="bg-teal-100 dark:bg-teal-900/30 p-1.5 rounded-md">
                    <Scale className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    StudentAIDetector vs QuillBot
                  </h2>
                </div>
                <CardContent className="pt-6">
                  <p className="text-muted-foreground mb-6">
                    Compare our dedicated AI detection and humanization platform
                    with QuillBot's broader writing assistant tools. See which
                    solution better fits your needs.
                  </p>
                  <Button
                    variant="ghost"
                    className="text-teal-600 hover:text-teal-800 hover:bg-teal-50 dark:hover:bg-teal-900/20 p-0 h-auto"
                    asChild
                  >
                    <Link
                      href="/compare/studentaidetector-vs-quillbot"
                      className="flex items-center"
                    >
                      Read the full comparison
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Link>
                  </Button>
                </CardContent>
              </div>
            </Card>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-8 mb-16 border border-blue-100 dark:border-blue-900/50">
            <h2 className="text-2xl font-semibold mb-6">
              Why Compare AI Detection Tools?
            </h2>
            <div className="prose dark:prose-invert max-w-none">
              <p>
                With the rapid advancement of AI writing tools like ChatGPT,
                Claude, and Bard, choosing the right AI detection solution is
                crucial for:
              </p>

              <div className="grid md:grid-cols-2 gap-4 mt-6">
                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Educators
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Ensuring academic integrity and fair assessment across all
                    student submissions
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Students
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Verifying their work meets institutional guidelines before
                    submission
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Content Creators
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Maintaining authenticity and avoiding AI content penalties
                    from search engines
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Businesses
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Protecting brand voice and content quality across all
                    marketing materials
                  </p>
                </div>
              </div>

              <p className="mt-6">
                Our comparison guides help you navigate the growing landscape of
                AI detection tools to find the solution that best fits your
                specific needs and budget.
              </p>
            </div>
          </div>

          <div className="mb-16">
            <h2 className="text-2xl font-bold mb-6">
              More AI Detector Comparisons
            </h2>
            <div className="grid md:grid-cols-2 gap-4 md:gap-6">
              <Link
                href="/compare/studentaidetector-vs-writer"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">
                  StudentAIDetector vs Writer.com
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>

              <Link
                href="/compare/studentaidetector-vs-winston"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">
                  StudentAIDetector vs Winston AI
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>

              <Link
                href="/compare/studentaidetector-vs-copyleaks"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">
                  StudentAIDetector vs Copyleaks
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>

              <Link
                href="/compare/studentaidetector-vs-zerogpt"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">
                  StudentAIDetector vs ZeroGPT
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-8 border">
            <h2 className="text-2xl font-semibold mb-6">
              Looking for Specific Solutions?
            </h2>
            <p className="text-muted-foreground mb-6">
              If you're interested in how StudentAIDetector can help with
              specific use cases, check out our dedicated solutions pages:
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <Link
                href="/for-educators"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  AI Detection for Educators
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>

              <Link
                href="/for-students"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  AI Detection for Students
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>

              <Link
                href="/for-bloggers"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  AI Detection for Bloggers
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>

              <Link
                href="/for-universities"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  AI Detection for University Admissions
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
            </div>
          </div>

          {/* CTA Section */}
          <section className="text-center mt-16">
            <h2 className="text-3xl font-bold mb-6">
              Ready to Experience the{" "}
              <span className="text-primary">StudentAIDetector Difference</span>
              ?
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Try our AI detection platform today and see why educators,
              students, and content creators choose StudentAIDetector.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/signup">Start Free Trial</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/pricing">View Pricing</Link>
              </Button>
            </div>
          </section>
        </div>
      </div>
      <Footer />
    </>
  );
}
