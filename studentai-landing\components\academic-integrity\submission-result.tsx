"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { AlertTriangle, CheckCircle, Info, Download, Copy, FileText } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface SubmissionResultProps {
  result: {
    overallScore: number
    plagiarismScore: number
    collusionScore: number
    aiGeneratedScore: number
    sources: Array<{
      url: string
      similarity: number
      title: string
    }>
    similarSubmissions: Array<{
      id: string
      similarity: number
      student: string
      date: string
    }>
    highlightedText: string
    suspiciousSections: Array<{
      text: string
      startIndex: number
      endIndex: number
      type: string
      similarity: number
    }>
  }
}

export function SubmissionResult({ result }: SubmissionResultProps) {
  const [activeTab, setActiveTab] = useState("summary")
  const { toast } = useToast()

  const getScoreColor = (score: number) => {
    if (score < 20) return "text-green-500"
    if (score < 50) return "text-amber-500"
    return "text-red-500"
  }

  const getScoreLabel = (score: number) => {
    if (score < 20) return "Low Risk"
    if (score < 50) return "Medium Risk"
    return "High Risk"
  }

  const getScoreIcon = (score: number) => {
    if (score < 20) return <CheckCircle className="h-5 w-5 text-green-500" />
    if (score < 50) return <Info className="h-5 w-5 text-amber-500" />
    return <AlertTriangle className="h-5 w-5 text-red-500" />
  }

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(result.highlightedText)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard.",
    })
  }

  const handleDownloadReport = () => {
    // In a real app, this would generate and download a PDF report
    toast({
      title: "Report downloaded",
      description: "The integrity report has been downloaded.",
    })
  }

  const renderHighlightedText = () => {
    const text = result.highlightedText
    const sections = [...result.suspiciousSections].sort((a, b) => a.startIndex - b.startIndex)

    if (sections.length === 0) return <p className="whitespace-pre-wrap">{text}</p>

    const parts = []
    let lastIndex = 0

    sections.forEach((section, i) => {
      // Add text before the suspicious section
      if (section.startIndex > lastIndex) {
        parts.push(<span key={`normal-${i}`}>{text.substring(lastIndex, section.startIndex)}</span>)
      }

      // Add the suspicious section with highlighting
      const highlightClass =
        section.type === "plagiarism"
          ? "bg-red-100 dark:bg-red-900/30"
          : section.type === "ai-generated"
            ? "bg-amber-100 dark:bg-amber-900/30"
            : "bg-blue-100 dark:bg-blue-900/30"

      parts.push(
        <span
          key={`highlight-${i}`}
          className={`${highlightClass} px-1 rounded`}
          title={`${section.type === "plagiarism" ? "Plagiarism" : section.type === "ai-generated" ? "AI-Generated" : "Collusion"} probability: ${section.similarity}%`}
        >
          {text.substring(section.startIndex, section.endIndex)}
        </span>,
      )

      lastIndex = section.endIndex
    })

    // Add any remaining text
    if (lastIndex < text.length) {
      parts.push(<span key="normal-last">{text.substring(lastIndex)}</span>)
    }

    return <p className="whitespace-pre-wrap break-words">{parts}</p>
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <h2 className="text-2xl font-bold">Integrity Analysis Results</h2>
          <Badge
            variant={result.overallScore < 20 ? "outline" : result.overallScore < 50 ? "secondary" : "destructive"}
          >
            {getScoreLabel(result.overallScore)}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleCopyToClipboard}>
            <Copy className="h-4 w-4 mr-1" /> Copy Text
          </Button>
          <Button variant="outline" size="sm" onClick={handleDownloadReport}>
            <Download className="h-4 w-4 mr-1" /> Download Report
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="summary">Summary</TabsTrigger>
          <TabsTrigger value="highlighted">Highlighted Text</TabsTrigger>
          <TabsTrigger value="sources">Sources & Matches</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4 mt-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center justify-between">
                  <span>Overall Integrity Score</span>
                  <span className={getScoreColor(result.overallScore)}>{result.overallScore}%</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Progress
                  value={result.overallScore}
                  className="h-2"
                  indicatorClassName={
                    result.overallScore < 20 ? "bg-green-500" : result.overallScore < 50 ? "bg-amber-500" : "bg-red-500"
                  }
                />
                <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                  <span>Low Risk</span>
                  <span>High Risk</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span>Plagiarism</span>
                  </div>
                  <span className={getScoreColor(result.plagiarismScore)}>{result.plagiarismScore}%</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Progress
                  value={result.plagiarismScore}
                  className="h-2"
                  indicatorClassName={
                    result.plagiarismScore < 20
                      ? "bg-green-500"
                      : result.plagiarismScore < 50
                        ? "bg-amber-500"
                        : "bg-red-500"
                  }
                />
                <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                  <span>Low</span>
                  <span>High</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <AlertTriangle className="h-4 w-4" />
                    <span>AI-Generated</span>
                  </div>
                  <span className={getScoreColor(result.aiGeneratedScore)}>{result.aiGeneratedScore}%</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Progress
                  value={result.aiGeneratedScore}
                  className="h-2"
                  indicatorClassName={
                    result.aiGeneratedScore < 20
                      ? "bg-green-500"
                      : result.aiGeneratedScore < 50
                        ? "bg-amber-500"
                        : "bg-red-500"
                  }
                />
                <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                  <span>Low</span>
                  <span>High</span>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="pt-4 space-y-2">
            <h4 className="text-sm font-medium">Analysis Summary</h4>
            <div className="p-4 bg-muted rounded-md">
              <p className="text-sm">
                Based on the analysis, this submission has an overall integrity score of {result.overallScore}%.
                {result.plagiarismScore > 20 &&
                  ` There are indications of potential plagiarism (score: ${result.plagiarismScore}%) from the following sources:`}
                {result.aiGeneratedScore > 20 &&
                  ` There are also patterns consistent with AI-generated content (score: ${result.aiGeneratedScore}%).`}
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="highlighted" className="mt-4">
          <div className="p-4 bg-muted rounded-md max-h-[400px] overflow-y-auto">{renderHighlightedText()}</div>
        </TabsContent>

        <TabsContent value="sources" className="mt-4 space-y-4">
          {result.sources.length > 0 ? (
            <>
              <h4 className="text-sm font-medium">External Sources</h4>
              <div className="space-y-2">
                {result.sources.map((source, index) => (
                  <Card key={index} className="border">
                    <CardContent className="flex items-center justify-between">
                      <div>
                        <a href={source.url} target="_blank" rel="noopener noreferrer" className="font-medium">
                          {source.title}
                        </a>
                        <p className="text-sm text-muted-foreground">{source.url}</p>
                      </div>
                      <Badge variant="outline">{source.similarity}% Similarity</Badge>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          ) : (
            <p className="text-center text-muted-foreground py-8">No external sources detected</p>
          )}

          {result.similarSubmissions.length > 0 && (
            <>
              <h4 className="text-sm font-medium">Similar Submissions</h4>
              <div className="space-y-2">
                {result.similarSubmissions.map((submission, index) => (
                  <Card key={index} className="border">
                    <CardContent className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{submission.student}</p>
                        <p className="text-sm text-muted-foreground">
                          {submission.date} - Submission ID: {submission.id}
                        </p>
                      </div>
                      <Badge variant="outline">{submission.similarity}% Similarity</Badge>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
