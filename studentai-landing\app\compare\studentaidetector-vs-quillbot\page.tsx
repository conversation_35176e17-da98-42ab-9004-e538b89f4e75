import { FeatureComparison } from "@/components/comparison/feature-comparison";
import { PricingComparison } from "@/components/comparison/pricing-comparison";
import { Metadata } from "next";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ChevronLeft, <PERSON>Right, Check } from "lucide-react";

export const metadata: Metadata = {
  title: "StudentAIDetector vs Quillbot | Feature & Pricing Comparison",
  description:
    "Compare StudentAIDetector with Quillbot. See how our specialized AI detection capabilities compare to Quillbot's AI detector feature.",
};

export default function QuillbotComparison() {
  return (
    <div className="container px-4 py-12 mx-auto max-w-7xl">
      <div className="mb-8">
        <Link
          href="/compare"
          className="inline-flex items-center text-sm text-gray-600 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Back to comparisons
        </Link>

        <h1 className="mt-4 text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
          StudentAIDetector vs Quillbot
        </h1>
        <p className="mt-4 text-xl text-gray-600 dark:text-gray-400">
          Compare our specialized AI detection tool against Quillbot's AI
          detector feature
        </p>
      </div>

      <div className="my-8 p-6 bg-green-50 dark:bg-green-900/10 rounded-xl border border-green-100 dark:border-green-800/30">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
          Different Focus Areas
        </h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Before comparing, it's worth noting that StudentAIDetector and
          Quillbot have different primary purposes:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 p-5 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="flex items-center text-green-600 dark:text-green-400 font-semibold mb-2">
              <span className="flex h-6 w-6 rounded-full bg-green-100 dark:bg-green-900/30 items-center justify-center mr-2">
                <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
              </span>
              Quillbot
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <strong>Primary focus:</strong> Writing assistant tool with AI
              detection as an additional feature
            </p>
            <ul className="mt-3 space-y-1">
              <li className="flex items-start text-sm text-gray-600 dark:text-gray-300">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Paraphrasing & rewriting tools</span>
              </li>
              <li className="flex items-start text-sm text-gray-600 dark:text-gray-300">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Summarization features</span>
              </li>
              <li className="flex items-start text-sm text-gray-600 dark:text-gray-300">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Grammar correction</span>
              </li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 p-5 rounded-lg border border-gray-200 dark:border-gray-700">
            <h3 className="flex items-center text-blue-600 dark:text-blue-400 font-semibold mb-2">
              <span className="flex h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 items-center justify-center mr-2">
                <Check className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </span>
              StudentAIDetector
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              <strong>Primary focus:</strong> Specialized AI content detection
              with high accuracy
            </p>
            <ul className="mt-3 space-y-1">
              <li className="flex items-start text-sm text-gray-600 dark:text-gray-300">
                <Check className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Advanced detection algorithms</span>
              </li>
              <li className="flex items-start text-sm text-gray-600 dark:text-gray-300">
                <Check className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Lower false positive rate</span>
              </li>
              <li className="flex items-start text-sm text-gray-600 dark:text-gray-300">
                <Check className="h-4 w-4 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Education-focused features</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="my-12 space-y-16">
        {/* Feature Comparison Section */}
        <section id="features" className="scroll-mt-24">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl mb-8">
            Feature Comparison
          </h2>
          <FeatureComparison initialCompetitor="quillbot" />
        </section>

        {/* Pricing Comparison Section */}
        <section id="pricing" className="scroll-mt-24">
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-3xl mb-8">
            Pricing Comparison
          </h2>
          <PricingComparison initialCompetitor="quillbot" />
        </section>

        {/* Benefits Section */}
        <section className="bg-blue-50 dark:bg-blue-900/10 rounded-2xl p-8 my-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            Benefits of Using StudentAIDetector for AI Detection
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3">
                Specialized Focus
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Purpose-built for AI content detection rather than an additional
                feature in a writing tool.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3">
                Higher Detection Accuracy
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                More precise detection algorithms with lower false positive
                rates for academic use.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-blue-600 dark:text-blue-400 mb-3">
                Education Tools
              </h3>
              <p className="text-gray-700 dark:text-gray-300">
                Education-specific features like detailed analytics and LMS
                integrations.
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <div className="text-center py-8">
          <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
            <Link href="/signup" className="flex items-center">
              Try StudentAIDetector for Free
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <p className="mt-3 text-sm text-gray-600 dark:text-gray-400">
            No credit card required. Start with our free plan today.
          </p>
        </div>
      </div>
    </div>
  );
}
