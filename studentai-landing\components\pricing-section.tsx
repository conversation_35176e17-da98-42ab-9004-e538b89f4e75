"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  CheckCircle,
  X,
  PieChart,
  Shield,
  Zap,
  BarChart3,
  Users,
  FileText,
  Clock,
  Sparkles,
  CreditCard,
  Key,
  Infinity as InfinityIcon,
  BarChart,
  Wand2,
} from "lucide-react";
import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";

// Word-based pricing plans
const plans = [
  {
    name: "Free",
    description: "Basic AI detection for individuals",
    monthlyPrice: 0,
    yearlyPrice: 0,
    highlight: false,
    features: [
      { icon: FileText, text: "5,000 words per month" },
      { icon: Shield, text: "Basic detection accuracy" },
      { icon: Clock, text: "Standard processing speed" },
    ],
    limitations: [
      "Limited report details",
      "No API access",
      "No team features",
      "Basic support only",
    ],
    ctaText: "Get Started",
    color: "gray",
    popularBadge: false,
    words: 5000,
  },
  {
    name: "Standard",
    description: "For individual teachers and researchers",
    monthlyPrice: 7.99,
    yearlyPrice: 5.59, // 30% off monthly price
    highlight: true,
    features: [
      { icon: FileText, text: "50,000 words per month" },
      { icon: Shield, text: "Advanced detection accuracy" },
      { icon: Zap, text: "Priority processing" },
      { icon: Key, text: "Basic API access" },
      { icon: BarChart, text: "Detection analytics" },
    ],
    limitations: [],
    ctaText: "Subscribe Now",
    color: "blue",
    popularBadge: true,
    words: 50000,
  },
  {
    name: "Pro",
    description: "For educators and small departments",
    monthlyPrice: 11.99,
    yearlyPrice: 8.39, // 30% off monthly price
    highlight: false,
    features: [
      { icon: FileText, text: "200,000 words per month" },
      { icon: Shield, text: "Highest detection accuracy" },
      { icon: Zap, text: "Express processing" },
      { icon: Key, text: "Full API access" },
      { icon: Users, text: "Up to 5 team members" },
      { icon: BarChart, text: "Advanced analytics" },
    ],
    limitations: [],
    ctaText: "Subscribe Now",
    color: "green",
    popularBadge: false,
    words: 200000,
  },
  {
    name: "Enterprise",
    description: "For institutions and larger teams",
    monthlyPrice: null,
    yearlyPrice: null,
    highlight: false,
    features: [
      { icon: FileText, text: "Custom word limits" },
      { icon: Shield, text: "Highest detection accuracy" },
      { icon: Zap, text: "Express processing" },
      { icon: Key, text: "Advanced API with higher limits" },
      { icon: Users, text: "Unlimited team members" },
      { icon: PieChart, text: "Custom analytics dashboard" },
      { icon: CreditCard, text: "Custom billing options" },
    ],
    limitations: [],
    ctaText: "Contact Sales",
    color: "purple",
    popularBadge: false,
    words: null,
  },
];

export default function PricingSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredPlan, setHoveredPlan] = useState<string | null>(null);
  const [annualBilling, setAnnualBilling] = useState(true);
  const [includeHumanizer, setIncludeHumanizer] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Humanizer addon price
  const humanizerPrice = 1.99;

  // Intersection Observer to trigger animations when section is visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = sectionRef.current;
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  const buttonColorMap = {
    gray: "bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-800",
    blue: "bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800",
    green:
      "bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800",
    purple:
      "bg-purple-600 hover:bg-purple-700 dark:bg-purple-700 dark:hover:bg-purple-800",
  };

  const buttonGradientMap = {
    gray: "from-gray-500 to-gray-600 dark:from-gray-600 dark:to-gray-700",
    blue: "from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700",
    green: "from-green-500 to-green-600 dark:from-green-600 dark:to-green-700",
    purple:
      "from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700",
  };

  // Calculate final price with humanizer if selected
  const getFinalPrice = (basePrice, includeHumanizerAddon = false) => {
    if (basePrice === 0 || basePrice === null) return basePrice;
    return includeHumanizerAddon ? basePrice + humanizerPrice : basePrice;
  };

  // Format number with commas for readability
  const formatNumber = (num) => {
    return num
      ? num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
      : "Custom";
  };

  return (
    <section
      ref={sectionRef}
      className="w-full py-16 md:py-28 bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 relative overflow-hidden"
    >
      {/* Decorative elements */}
      <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-100/20 dark:bg-blue-900/10 rounded-full blur-3xl -z-10" />
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-purple-100/20 dark:bg-purple-900/10 rounded-full blur-3xl -z-10" />

      {/* Animated particles */}
      <div className="absolute inset-0 -z-5 opacity-20 dark:opacity-10">
        {[...Array(5)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-gradient-to-r from-purple-300/40 to-blue-300/40 dark:from-purple-500/20 dark:to-blue-500/20"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              width: `${Math.random() * 6 + 4}px`,
              height: `${Math.random() * 6 + 4}px`,
              animation: `float-vertical ${
                Math.random() * 10 + 15
              }s infinite alternate ease-in-out`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div
          className={`text-center mb-12 ${
            isVisible ? "animate-fade-in" : "opacity-0"
          }`}
        >
          <div className="inline-flex items-center gap-2 mb-2 px-4 py-1.5 bg-blue-100/80 dark:bg-blue-900/30 backdrop-blur-sm rounded-full border border-blue-200/40 dark:border-blue-700/40 shadow-sm">
            <CreditCard className="h-4 w-4 text-blue-500 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Simple, Word-Based Pricing
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mt-4">
            Choose Your Plan
          </h2>

          <p className="max-w-[800px] mx-auto mt-4 text-gray-600 dark:text-gray-300 md:text-xl/relaxed">
            Pay only for what you need with our word-based pricing
          </p>

          {/* Billing toggle */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mt-8 mb-4">
            <div className="flex items-center justify-center">
              <span
                className={`text-sm ${
                  !annualBilling
                    ? "font-medium text-gray-900 dark:text-white"
                    : "text-gray-500 dark:text-gray-400"
                }`}
              >
                Monthly
              </span>
              <div className="mx-4">
                <Switch
                  id="billing-toggle"
                  checked={annualBilling}
                  onCheckedChange={setAnnualBilling}
                  className="data-[state=checked]:bg-blue-600"
                />
              </div>
              <div className="flex items-center">
                <span
                  className={`text-sm ${
                    annualBilling
                      ? "font-medium text-gray-900 dark:text-white"
                      : "text-gray-500 dark:text-gray-400"
                  }`}
                >
                  Annual
                </span>
                <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  Save 30%
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="humanizer-addon"
                checked={includeHumanizer}
                onCheckedChange={setIncludeHumanizer}
                className="data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
              />
              <div className="flex items-center space-x-1.5">
                <Label
                  htmlFor="humanizer-addon"
                  className="text-sm font-medium cursor-pointer"
                >
                  Add AI Humanizer
                </Label>
                <Wand2 className="h-4 w-4 text-purple-500" />
                <Badge
                  variant="outline"
                  className="text-xs bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:border-purple-700/50 dark:text-purple-300"
                >
                  +${humanizerPrice}/mo
                </Badge>
              </div>
            </div>
          </div>

          {includeHumanizer && (
            <div className="mt-3 mb-8">
              <div className="inline-flex items-center p-2 text-sm bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-lg border border-purple-100 dark:border-purple-800/30">
                <Wand2 className="h-4 w-4 mr-2 text-purple-500" />
                AI Humanizer helps make AI-generated text appear more
                human-written
              </div>
            </div>
          )}
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-4 max-w-7xl mx-auto">
          {plans.map((plan, i) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 30 }}
              animate={isVisible ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.2 + i * 0.1 }}
              className="flex"
              onMouseEnter={() => setHoveredPlan(plan.name)}
              onMouseLeave={() => setHoveredPlan(null)}
            >
              <Card
                className={`overflow-hidden w-full transition-all duration-300 ${
                  hoveredPlan === plan.name ? "transform -translate-y-2" : ""
                } ${
                  plan.highlight
                    ? "border-2 border-blue-500 dark:border-blue-400 shadow-xl shadow-blue-500/10 dark:shadow-blue-400/10 relative"
                    : "border border-gray-200 dark:border-gray-700 shadow-md"
                }`}
              >
                {plan.highlight && (
                  <div className="absolute top-0 inset-x-0 h-2 bg-gradient-to-r from-blue-400 to-blue-500 dark:from-blue-500 dark:to-blue-400" />
                )}

                {plan.popularBadge && (
                  <div className="absolute top-5 right-5">
                    <Badge className="bg-blue-500 dark:bg-blue-600 hover:bg-blue-600 dark:hover:bg-blue-700 text-white">
                      Most Popular
                    </Badge>
                  </div>
                )}

                <div className="p-6 md:p-6 bg-white dark:bg-gray-800">
                  <h3
                    className={`text-xl font-bold mb-1 ${
                      plan.color === "gray"
                        ? "text-gray-700 dark:text-gray-300"
                        : `text-${plan.color}-600 dark:text-${plan.color}-400`
                    }`}
                  >
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 min-h-[40px]">
                    {plan.description}
                  </p>

                  <div className="mb-4">
                    <div className="flex items-baseline">
                      {annualBilling ? (
                        <>
                          <span className="text-3xl font-bold text-gray-900 dark:text-white">
                            {plan.yearlyPrice === null
                              ? "Custom"
                              : `$${getFinalPrice(
                                  plan.yearlyPrice,
                                  includeHumanizer && plan.yearlyPrice > 0
                                ).toFixed(2)}`}
                          </span>
                          {plan.yearlyPrice !== null && (
                            <span className="text-gray-500 dark:text-gray-400 ml-1 text-sm">
                              /month
                            </span>
                          )}
                        </>
                      ) : (
                        <>
                          <span className="text-3xl font-bold text-gray-900 dark:text-white">
                            {plan.monthlyPrice === null
                              ? "Custom"
                              : `$${getFinalPrice(
                                  plan.monthlyPrice,
                                  includeHumanizer && plan.monthlyPrice > 0
                                ).toFixed(2)}`}
                          </span>
                          {plan.monthlyPrice !== null && (
                            <span className="text-gray-500 dark:text-gray-400 ml-1 text-sm">
                              /month
                            </span>
                          )}
                        </>
                      )}
                    </div>

                    {annualBilling && plan.monthlyPrice > 0 && (
                      <div className="flex items-center mt-1">
                        <span className="text-sm text-gray-500 dark:text-gray-400 line-through mr-1">
                          $
                          {getFinalPrice(
                            plan.monthlyPrice,
                            includeHumanizer && plan.monthlyPrice > 0
                          ).toFixed(2)}
                          /mo
                        </span>
                        <span className="text-xs text-green-600 dark:text-green-400">
                          Save 30%
                        </span>
                      </div>
                    )}

                    {annualBilling && plan.yearlyPrice > 0 && (
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Billed as $
                        {(
                          getFinalPrice(
                            plan.yearlyPrice,
                            includeHumanizer && plan.yearlyPrice > 0
                          ) * 12
                        ).toFixed(2)}{" "}
                        yearly
                      </div>
                    )}
                  </div>

                  <Button
                    className={`w-full group relative overflow-hidden ${
                      plan.color === "gray"
                        ? "bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-white"
                        : `${buttonColorMap[plan.color]} text-white`
                    }`}
                    size="lg"
                    onClick={() =>
                      window.open(
                        "https://app.studentaidetector.com/signup",
                        "_blank"
                      )
                    }
                  >
                    <span className="relative z-10">{plan.ctaText}</span>
                    {plan.color !== "gray" && (
                      <span
                        className={`absolute inset-0 bg-gradient-to-r ${
                          buttonGradientMap[plan.color]
                        } transform translate-y-full group-hover:translate-y-0 transition-transform duration-300`}
                      ></span>
                    )}
                  </Button>
                </div>

                <CardContent className="p-6 md:p-6 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                    What's included:
                  </div>
                  <ul className="space-y-3 text-sm">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <feature.icon
                          className={`h-5 w-5 ${
                            plan.color === "gray"
                              ? "text-gray-500 dark:text-gray-400"
                              : `text-${plan.color}-500 dark:text-${plan.color}-400`
                          } mr-2 flex-shrink-0 mt-0.5`}
                        />
                        <span className="text-gray-600 dark:text-gray-300">
                          {feature.text}
                        </span>
                      </li>
                    ))}

                    {includeHumanizer && plan.monthlyPrice > 0 && (
                      <li className="flex items-start">
                        <Wand2 className="h-5 w-5 text-purple-500 dark:text-purple-400 mr-2 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-600 dark:text-gray-300">
                          AI Humanizer included
                        </span>
                      </li>
                    )}
                  </ul>

                  {plan.limitations.length > 0 && (
                    <>
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-6 mb-3">
                        Limitations:
                      </div>
                      <ul className="space-y-2">
                        {plan.limitations.map((limitation, i) => (
                          <li key={i} className="flex items-start">
                            <X className="h-4 w-4 text-gray-400 dark:text-gray-500 mr-2 flex-shrink-0 mt-0.5" />
                            <span className="text-gray-500 dark:text-gray-400 text-sm">
                              {limitation}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </>
                  )}
                </CardContent>

                {plan.highlight && (
                  <div className="px-6 py-4 bg-gradient-to-r from-blue-50 to-blue-50 dark:from-blue-900/20 dark:to-blue-900/20 text-center border-t border-blue-100 dark:border-blue-800/30">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center justify-center gap-1">
                      <Sparkles className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                      Perfect for individual educators and students
                    </span>
                  </div>
                )}
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Feature comparison grid */}
        <div
          className={`mt-20 max-w-7xl mx-auto ${
            isVisible ? "animate-fade-in" : "opacity-0"
          }`}
          style={{ animationDelay: "0.8s" }}
        >
          <div className="text-center mb-10">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
              Compare Plans
            </h3>
            <div className="h-1 w-20 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-4 rounded-full"></div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-4 px-6 font-medium text-gray-500 dark:text-gray-400">
                    Feature
                  </th>
                  <th className="py-4 px-6 text-center">
                    <span className="font-bold text-gray-700 dark:text-gray-300">
                      Free
                    </span>
                  </th>
                  <th className="py-4 px-6 text-center">
                    <span className="font-bold text-blue-600 dark:text-blue-400">
                      Standard
                    </span>
                  </th>
                  <th className="py-4 px-6 text-center">
                    <span className="font-bold text-green-600 dark:text-green-400">
                      Pro
                    </span>
                  </th>
                  <th className="py-4 px-6 text-center">
                    <span className="font-bold text-purple-600 dark:text-purple-400">
                      Enterprise
                    </span>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-gray-100">
                    Monthly price
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    $0
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    $7.99
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    $11.99
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Custom
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-gray-100">
                    Annual price (per month)
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    $0
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    $5.59
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    $8.39
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Custom
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-gray-100">
                    Words per month
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    5,000
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    50,000
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    200,000
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Custom
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-gray-100">
                    AI Humanizer add-on (+$1.99/mo)
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    <X className="h-5 w-5 text-gray-400 mx-auto" />
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-gray-100">
                    API Access
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    <X className="h-5 w-5 text-gray-400 mx-auto" />
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Limited
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-gray-100">
                    Team Members
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    1
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    1
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Up to 5
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Unlimited
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-gray-100">
                    Processing Priority
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Standard
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Priority
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Express
                  </td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-300">
                    Express
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* Usage Info */}
        <div
          className={`mt-16 max-w-3xl mx-auto ${
            isVisible ? "animate-fade-in" : "opacity-0"
          }`}
          style={{ animationDelay: "0.9s" }}
        >
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800/30 rounded-xl p-6">
            <div className="flex items-start gap-4">
              <div className="p-2 bg-blue-100 dark:bg-blue-800/50 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Word-Based Plans
                </h4>
                <p className="text-gray-700 dark:text-gray-300 text-sm mb-2">
                  Our plans are based on words processed per month, giving you
                  flexibility to use our AI detection tools as needed. Monthly
                  limits reset on your billing date.
                </p>
                <p className="text-gray-700 dark:text-gray-300 text-sm">
                  The optional AI Humanizer add-on ($1.99/mo) helps make
                  AI-generated text appear more natural and less detectable by
                  other AI detection systems. Add it to any paid plan for
                  comprehensive AI text management.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Enterprise CTA section */}
        <div
          className={`mt-20 max-w-3xl mx-auto text-center ${
            isVisible ? "animate-fade-in" : "opacity-0"
          }`}
          style={{ animationDelay: "1s" }}
        >
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-8 md:p-10 shadow-lg border border-blue-100 dark:border-blue-800/30">
            <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
              Need a custom plan?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Contact our sales team for custom institutional pricing and
              high-volume word processing. We offer special educational
              discounts for schools and universities.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Button
                onClick={() =>
                  window.open(
                    "https://app.studentaidetector.com/pricing",
                    "_blank"
                  )
                }
                className="bg-white hover:bg-gray-50 text-blue-600 border border-blue-200 hover:border-blue-300 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-blue-400 dark:border-blue-800"
              >
                View Enterprise Options
              </Button>
              <Button
                onClick={() =>
                  window.open("https://studentaidetector.com/contact", "_blank")
                }
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              >
                Contact Sales
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Animation styles */}
      <style jsx global>{`
        @keyframes float-vertical {
          from {
            transform: translateY(0);
          }
          to {
            transform: translateY(-10px);
          }
        }

        @keyframes fade-in {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .animate-fade-in {
          animation: fade-in 0.8s forwards ease-out;
        }
      `}</style>
    </section>
  );
}
