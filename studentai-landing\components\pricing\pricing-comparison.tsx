import { Check, HelpCircle, X } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export function PricingComparison() {
  const features = [
    {
      category: "Core Features",
      items: [
        {
          name: "AI Detection Queries",
          free: "10/month",
          basic: "100/month",
          pro: "500/month",
          enterprise: "Custom",
          tooltip: "Number of text analyses you can perform each month",
        },
        {
          name: "Text Volume Limit",
          free: "1,500 words",
          basic: "5,000 words",
          pro: "15,000 words",
          enterprise: "50,000+ words",
          tooltip: "Maximum words per analysis",
        },
        {
          name: "Detection Accuracy",
          free: "Standard",
          basic: "Enhanced",
          pro: "Premium",
          enterprise: "Premium+",
          tooltip: "Higher tiers use more sophisticated detection algorithms",
        },
        {
          name: "File Upload Formats",
          free: ".txt only",
          basic: ".txt, .pdf, .docx",
          pro: "All formats",
          enterprise: "All formats",
          tooltip: "File types supported for upload",
        },
      ],
    },
    {
      category: "Humanization Tools",
      items: [
        {
          name: "Basic Paraphrasing",
          free: "3/month",
          basic: "50/month",
          pro: "Unlimited",
          enterprise: "Unlimited",
          tooltip: "Reword text while maintaining meaning",
        },
        {
          name: "Style Adjustment",
          free: false,
          basic: true,
          pro: true,
          enterprise: true,
          tooltip: "Modify writing style to match human patterns",
        },
        {
          name: "Vocabulary Enhancement",
          free: false,
          basic: false,
          pro: true,
          enterprise: true,
          tooltip: "Enhance vocabulary with more nuanced expressions",
        },
        {
          name: "AI Section Fixing",
          free: false,
          basic: false,
          pro: true,
          enterprise: true,
          tooltip: "Target and rewrite sections likely to be flagged by AI detectors",
        },
      ],
    },
    {
      category: "Advanced Features",
      items: [
        {
          name: "Batch Processing",
          free: false,
          basic: "Up to 5 files",
          pro: "Up to 20 files",
          enterprise: "Unlimited",
          tooltip: "Process multiple documents at once",
        },
        {
          name: "History Retention",
          free: "7 days",
          basic: "30 days",
          pro: "90 days",
          enterprise: "Unlimited",
          tooltip: "How long your analysis history is stored",
        },
        {
          name: "API Access",
          free: false,
          basic: false,
          pro: "100 calls/month",
          enterprise: "Custom",
          tooltip: "Programmatic access to detection services",
        },
        {
          name: "Custom Detection Models",
          free: false,
          basic: false,
          pro: false,
          enterprise: true,
          tooltip: "Tailored AI detection models for your specific needs",
        },
      ],
    },
    {
      category: "Support & Services",
      items: [
        {
          name: "Support",
          free: "Community",
          basic: "Email",
          pro: "Priority Email",
          enterprise: "Dedicated Manager",
          tooltip: "Level of customer support provided",
        },
        {
          name: "SLA Guarantee",
          free: false,
          basic: false,
          pro: false,
          enterprise: true,
          tooltip: "Service Level Agreement with guaranteed uptime and response times",
        },
        {
          name: "Custom Reporting",
          free: false,
          basic: false,
          pro: "Basic",
          enterprise: "Advanced",
          tooltip: "Generate custom reports on detection results",
        },
        {
          name: "User Management",
          free: "1 user",
          basic: "5 users",
          pro: "20 users",
          enterprise: "Unlimited",
          tooltip: "Number of user accounts included",
        },
      ],
    },
  ]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Feature Comparison</h2>
        <p className="text-muted-foreground mt-2">Detailed breakdown of what's included in each plan</p>
      </div>

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-4 px-6 font-medium">Feature</th>
                  <th className="text-center py-4 px-4 font-medium">Free</th>
                  <th className="text-center py-4 px-4 font-medium">Basic</th>
                  <th className="text-center py-4 px-4 font-medium">Pro</th>
                  <th className="text-center py-4 px-4 font-medium">Enterprise</th>
                </tr>
              </thead>
              <tbody>
                {features.map((category) => (
                  <>
                    <tr key={category.category} className="bg-muted/50">
                      <td colSpan={5} className="py-2 px-6 font-medium">
                        {category.category}
                      </td>
                    </tr>
                    {category.items.map((feature) => (
                      <tr key={feature.name} className="border-b">
                        <td className="py-3 px-6">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center">
                                  {feature.name}
                                  <HelpCircle className="ml-1 h-3.5 w-3.5 text-muted-foreground" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-xs">
                                <p>{feature.tooltip}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </td>
                        <td className="text-center py-3 px-4">{renderFeatureValue(feature.free)}</td>
                        <td className="text-center py-3 px-4">{renderFeatureValue(feature.basic)}</td>
                        <td className="text-center py-3 px-4">{renderFeatureValue(feature.pro)}</td>
                        <td className="text-center py-3 px-4">{renderFeatureValue(feature.enterprise)}</td>
                      </tr>
                    ))}
                  </>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

function renderFeatureValue(value: boolean | string) {
  if (value === true) {
    return <Check className="mx-auto h-4 w-4 text-primary" />
  } else if (value === false) {
    return <X className="mx-auto h-4 w-4 text-muted-foreground/70" />
  } else {
    return <span className="text-sm">{value}</span>
  }
}
