"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { AIDetectionModal } from "./ai-detection-modal";
import { Badge } from "./ui/badge";

export default function AiDetectionTool() {
  const [text, setText] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAnalyze = () => {
    if (!text.trim() || text.length < 100) return;
    setIsModalOpen(true);
  };

  return (
    <div className="p-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <Textarea
            placeholder="Paste text here to analyze for AI-generated content..."
            className="min-h-[200px] resize-none"
            value={text}
            onChange={(e) => setText(e.target.value)}
          />
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              {text ? `${text.length} characters` : "Paste text to analyze"}
              {text.length > 0 && text.length < 100 && (
                <Badge
                  variant="outline"
                  className="ml-2 text-amber-500 border-amber-200 bg-amber-50 dark:bg-amber-900/20 dark:border-amber-700"
                >
                  Minimum 100 characters required
                </Badge>
              )}
            </div>
            <Button
              onClick={handleAnalyze}
              disabled={!text.trim() || text.length < 100}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              Analyze Text
            </Button>
          </div>
        </div>
      </div>

      {/* Detection modal */}
      <AIDetectionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        text={text}
      />
    </div>
  );
}
