import type { Metada<PERSON> } from "next"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { CalendarIcon, FileText, Trash2 } from "lucide-react"

export const metadata: Metadata = {
  title: "Analysis History - StudentAIDetector",
  description:
    "View and manage your past AI content analyses. Track your detection history and review previous results.",
  alternates: {
    canonical: "/history",
  },
  openGraph: {
    title: "Analysis History - StudentAIDetector",
    description:
      "View and manage your past AI content analyses. Track your detection history and review previous results.",
    url: "/history",
    type: "website",
  },
  robots: {
    index: false,
    follow: true,
  },
}

export default function HistoryPage() {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container py-8 md:py-12">
        <div className="flex flex-col gap-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h1 className="text-2xl sm:text-3xl font-bold">Analysis History</h1>
            <Button variant="outline">Clear History</Button>
          </div>

          <div className="grid gap-6">
            {/* This would be populated from a database in a real application */}
            <HistoryItem
              title="Essay on Climate Change"
              date="March 30, 2025"
              score={0.82}
              preview="Climate change is one of the most pressing issues facing our planet today. The scientific consensus is clear that human activities..."
            />
            <HistoryItem
              title="Research Paper Draft"
              date="March 29, 2025"
              score={0.24}
              preview="The effects of sleep deprivation on cognitive performance have been studied extensively in recent years. This paper examines..."
            />
            <HistoryItem
              title="Book Report"
              date="March 27, 2025"
              score={0.67}
              preview="To Kill a Mockingbird by Harper Lee explores themes of racial injustice and moral growth in the American South. The novel follows..."
            />
          </div>
        </div>
      </div>
      <Footer />
    </main>
  )
}

interface HistoryItemProps {
  title: string
  date: string
  score: number
  preview: string
}

function HistoryItem({ title, date, score, preview }: HistoryItemProps) {
  const getScoreColor = (score: number) => {
    if (score < 0.3) return "text-green-500"
    if (score < 0.7) return "text-amber-500"
    return "text-red-500"
  }

  const getScoreLabel = (score: number) => {
    if (score < 0.3) return "Likely Human"
    if (score < 0.7) return "Possibly AI"
    return "Likely AI"
  }

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div>
            <CardTitle className="text-lg sm:text-xl">{title}</CardTitle>
            <CardDescription className="flex items-center mt-1">
              <CalendarIcon className="h-3 w-3 mr-1" />
              {date}
            </CardDescription>
          </div>
          <div className={`font-bold ${getScoreColor(score)}`}>
            {getScoreLabel(score)} ({(score * 100).toFixed(0)}%)
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground line-clamp-2">{preview}</p>
        <div className="flex flex-wrap gap-2 mt-4">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            View Details
          </Button>
          <Button variant="ghost" size="sm" className="flex items-center gap-1 text-destructive hover:text-destructive">
            <Trash2 className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
