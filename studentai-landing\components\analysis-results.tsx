"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, CheckCircle, Info } from "lucide-react"

interface AnalysisResultsProps {
  result: {
    score: number
    highlightedText: string
    suspiciousSections: {
      text: string
      score: number
      startIndex: number
      endIndex: number
    }[]
  }
}

export function AnalysisResults({ result }: AnalysisResultsProps) {
  const [activeTab, setActiveTab] = useState("summary")

  const getScoreColor = (score: number) => {
    if (score < 0.3) return "text-green-500"
    if (score < 0.7) return "text-amber-500"
    return "text-red-500"
  }

  const getScoreLabel = (score: number) => {
    if (score < 0.3) return "Likely Human"
    if (score < 0.7) return "Possibly AI"
    return "Likely AI"
  }

  const getScoreIcon = (score: number) => {
    if (score < 0.3) return <CheckCircle className="h-5 w-5 text-green-500" />
    if (score < 0.7) return <Info className="h-5 w-5 text-amber-500" />
    return <AlertTriangle className="h-5 w-5 text-red-500" />
  }

  const renderHighlightedText = () => {
    const text = result.highlightedText
    const sections = [...result.suspiciousSections].sort((a, b) => a.startIndex - b.startIndex)

    if (sections.length === 0) return <p>{text}</p>

    const parts = []
    let lastIndex = 0

    sections.forEach((section, i) => {
      // Add text before the suspicious section
      if (section.startIndex > lastIndex) {
        parts.push(<span key={`normal-${i}`}>{text.substring(lastIndex, section.startIndex)}</span>)
      }

      // Add the suspicious section with highlighting
      const highlightClass = section.score > 0.7 ? "bg-red-100 dark:bg-red-900/30" : "bg-amber-100 dark:bg-amber-900/30"

      parts.push(
        <span
          key={`highlight-${i}`}
          className={`${highlightClass} px-1 rounded`}
          title={`AI probability: ${(section.score * 100).toFixed(0)}%`}
        >
          {text.substring(section.startIndex, section.endIndex)}
        </span>,
      )

      lastIndex = section.endIndex
    })

    // Add any remaining text
    if (lastIndex < text.length) {
      parts.push(<span key="normal-last">{text.substring(lastIndex)}</span>)
    }

    return <p className="whitespace-pre-wrap break-words">{parts}</p>
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle>Analysis Results</CardTitle>
          <div className="flex items-center gap-2">
            {getScoreIcon(result.score)}
            <span className={`font-bold ${getScoreColor(result.score)}`}>{getScoreLabel(result.score)}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="highlighted">Highlighted Text</TabsTrigger>
            <TabsTrigger value="sections">Suspicious Sections</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4 mt-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">AI Probability Score</span>
                <span className={`text-sm font-bold ${getScoreColor(result.score)}`}>
                  {(result.score * 100).toFixed(1)}%
                </span>
              </div>
              <Progress value={result.score * 100} className="h-2" />
            </div>

            <div className="pt-4 space-y-2">
              <h4 className="text-sm font-medium">Analysis Summary</h4>
              <div className="p-4 bg-muted rounded-md">
                <p className="text-sm">
                  {result.score < 0.3
                    ? "This text appears to be primarily human-written. The analysis detected minimal patterns consistent with AI-generated content."
                    : result.score < 0.7
                      ? "This text contains some patterns consistent with AI-generated content. It may be partially written or edited by AI tools."
                      : "This text shows strong indicators of being AI-generated. Multiple patterns typical of AI writing were detected throughout the content."}
                </p>
              </div>
            </div>

            <div className="pt-2">
              <h4 className="text-sm font-medium mb-2">Key Indicators</h4>
              <ul className="space-y-2">
                <li className="flex items-start gap-2 text-sm">
                  <div className="mt-0.5 h-2 w-2 rounded-full bg-primary" />
                  {result.score < 0.3
                    ? "Natural language variations and inconsistencies typical of human writing"
                    : "Consistent phrasing and predictable sentence structures"}
                </li>
                <li className="flex items-start gap-2 text-sm">
                  <div className="mt-0.5 h-2 w-2 rounded-full bg-primary" />
                  {result.score < 0.3
                    ? "Unique expressions and personal voice indicators"
                    : "Repetitive patterns in paragraph structure and transitions"}
                </li>
                <li className="flex items-start gap-2 text-sm">
                  <div className="mt-0.5 h-2 w-2 rounded-full bg-primary" />
                  {result.score < 0.3
                    ? "Varied sentence complexity and natural flow"
                    : "Unnaturally balanced sentence lengths and vocabulary usage"}
                </li>
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="highlighted" className="mt-4">
            <div className="p-4 bg-muted rounded-md max-h-[400px] overflow-y-auto">{renderHighlightedText()}</div>
          </TabsContent>

          <TabsContent value="sections" className="mt-4">
            <div className="space-y-4">
              {result.suspiciousSections.length > 0 ? (
                result.suspiciousSections.map((section, index) => (
                  <div key={index} className="border rounded-md p-4 space-y-2">
                    <div className="flex justify-between items-center flex-wrap gap-2">
                      <h4 className="text-sm font-medium">Section {index + 1}</h4>
                      <Badge variant={section.score > 0.7 ? "destructive" : "outline"}>
                        {(section.score * 100).toFixed(1)}% AI Probability
                      </Badge>
                    </div>
                    <p className="text-sm bg-muted p-2 rounded whitespace-pre-wrap break-words">{section.text}</p>
                  </div>
                ))
              ) : (
                <p className="text-center text-muted-foreground py-8">No suspicious sections detected</p>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
