"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  getKeywordRankings,
  getPagePerformance,
  getBacklinkProfile,
  generateSeoRecommendations,
} from "@/lib/seo-monitoring"
import { ArrowDown, ArrowUp, Minus } from "lucide-react"

export default function SeoDashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [keywordRankings, setKeywordRankings] = useState([])
  const [pagePerformance, setPagePerformance] = useState([])
  const [backlinkProfile, setBacklinkProfile] = useState(null)
  const [recommendations, setRecommendations] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchData() {
      try {
        const [keywords, pages, backlinks] = await Promise.all([
          getKeywordRankings(),
          getPagePerformance(),
          getBacklinkProfile(),
        ])

        setKeywordRankings(keywords)
        setPagePerformance(pages)
        setBacklinkProfile(backlinks)
        setRecommendations(generateSeoRecommendations())
        setIsLoading(false)
      } catch (error) {
        console.error("Error fetching SEO data:", error)
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  if (isLoading) {
    return (
      <main className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container py-12 flex-1 flex items-center justify-center">
          <p>Loading SEO dashboard...</p>
        </div>
        <Footer />
      </main>
    )
  }

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container py-8">
        <h1 className="text-3xl font-bold mb-6">SEO Performance Dashboard</h1>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="keywords">Keywords</TabsTrigger>
            <TabsTrigger value="pages">Pages</TabsTrigger>
            <TabsTrigger value="backlinks">Backlinks</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 mt-6">
            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Organic Traffic (30 days)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3,720</div>
                  <p className="text-xs text-muted-foreground">+12.5% from last month</p>
                  <div className="h-[80px] mt-4">{/* Chart would go here */}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average Position</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">8.3</div>
                  <p className="text-xs text-muted-foreground">-1.2 from last month</p>
                  <div className="h-[80px] mt-4">{/* Chart would go here */}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">3.8%</div>
                  <p className="text-xs text-muted-foreground">+0.5% from last month</p>
                  <div className="h-[80px] mt-4">{/* Chart would go here */}</div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Top SEO Recommendations</CardTitle>
                <CardDescription>Based on current performance data</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {recommendations.slice(0, 5).map((recommendation, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="mt-1 h-2 w-2 rounded-full bg-primary" />
                      <span>{recommendation}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="keywords" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Keyword Rankings</CardTitle>
                <CardDescription>Current positions for target keywords</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Keyword</th>
                        <th className="text-center py-3 px-4">Position</th>
                        <th className="text-center py-3 px-4">Change</th>
                        <th className="text-left py-3 px-4">URL</th>
                        <th className="text-center py-3 px-4">Last Updated</th>
                      </tr>
                    </thead>
                    <tbody>
                      {keywordRankings.map((keyword, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-3 px-4">{keyword.keyword}</td>
                          <td className="text-center py-3 px-4">{keyword.position}</td>
                          <td className="text-center py-3 px-4">
                            {keyword.previousPosition === null ? (
                              <span className="text-muted-foreground">New</span>
                            ) : keyword.previousPosition > keyword.position ? (
                              <div className="flex items-center justify-center text-green-500">
                                <ArrowUp className="h-4 w-4 mr-1" />
                                {keyword.previousPosition - keyword.position}
                              </div>
                            ) : keyword.previousPosition < keyword.position ? (
                              <div className="flex items-center justify-center text-red-500">
                                <ArrowDown className="h-4 w-4 mr-1" />
                                {keyword.position - keyword.previousPosition}
                              </div>
                            ) : (
                              <div className="flex items-center justify-center text-muted-foreground">
                                <Minus className="h-4 w-4" />
                              </div>
                            )}
                          </td>
                          <td className="py-3 px-4 text-sm truncate max-w-[200px]">{keyword.url}</td>
                          <td className="text-center py-3 px-4 text-sm text-muted-foreground">{keyword.lastUpdated}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="pages" className="space-y-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Page Performance</CardTitle>
                <CardDescription>Traffic and engagement metrics by page</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">URL</th>
                        <th className="text-center py-3 px-4">Organic Traffic</th>
                        <th className="text-center py-3 px-4">CTR</th>
                        <th className="text-center py-3 px-4">Avg. Position</th>
                        <th className="text-center py-3 px-4">Impressions</th>
                        <th className="text-center py-3 px-4">Conv. Rate</th>
                      </tr>
                    </thead>
                    <tbody>
                      {pagePerformance.map((page, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-3 px-4 text-sm truncate max-w-[200px]">{page.url}</td>
                          <td className="text-center py-3 px-4">{page.organicTraffic}</td>
                          <td className="text-center py-3 px-4">{page.clickThroughRate}%</td>
                          <td className="text-center py-3 px-4">{page.averagePosition}</td>
                          <td className="text-center py-3 px-4">{page.impressions}</td>
                          <td className="text-center py-3 px-4">{page.conversionRate}%</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="backlinks" className="space-y-6 mt-6">
            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Backlinks</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{backlinkProfile.totalBacklinks}</div>
                  <p className="text-xs text-muted-foreground">
                    +{backlinkProfile.newBacklinksLast30Days} in last 30 days
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Unique Domains</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{backlinkProfile.uniqueDomains}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Domain Authority</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{backlinkProfile.domainAuthority}</div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Top Linking Domains</CardTitle>
                <CardDescription>Sites with the most backlinks to StudentAIDetector</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Domain</th>
                        <th className="text-center py-3 px-4">Authority</th>
                        <th className="text-center py-3 px-4">Links</th>
                      </tr>
                    </thead>
                    <tbody>
                      {backlinkProfile.topLinkingDomains.map((domain, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-3 px-4">{domain.domain}</td>
                          <td className="text-center py-3 px-4">{domain.authority}</td>
                          <td className="text-center py-3 px-4">{domain.links}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      <Footer />
    </main>
  )
}
