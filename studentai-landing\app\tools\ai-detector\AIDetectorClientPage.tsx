"use client";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  FileText,
  Shield,
  Zap,
  ArrowRight,
  MessageSquare,
  BookOpen,
  Server,
  Copy,
} from "lucide-react";
import AiDetectionTool from "@/components/ai-detection-tool"; // Import your AI detection tool
import { Breadcrumb } from "@/components/breadcrumb";
import Link from "next/link";
import { generateToolSchema } from "@/lib/seo-utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function AIDetectorClientPage() {
  const toolSchema = generateToolSchema(
    "AI Detector Tool",
    "Identify AI-generated content with high accuracy. Detects text written by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and other AI models.",
    "https://studentaidetector.com/tools/ai-detector"
  );

  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-8">
        <Breadcrumb />
      </div>

      {/* Hero Section */}
      <section className="bg-muted/50 py-12 md:py-20">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="space-y-2 text-center">
              <Badge variant="outline" className="mb-2">
                AI Detection Tool
              </Badge>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Advanced AI Content Detector
              </h1>
              <p className="text-xl text-muted-foreground">
                Identify AI-generated content with industry-leading accuracy
              </p>
            </div>
            <p className="text-muted-foreground text-center max-w-2xl">
              Our multi-layered detection technology identifies content created
              by ChatGPT, GPT-4, Claude, and other AI models with over 99%
              accuracy. Perfect for educators verifying student work, content
              managers ensuring quality, and writers checking their own content.
            </p>
          </div>
        </div>
      </section>

      {/* AI Detection Tool Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <Card className="w-full mx-auto">
            <CardHeader className="pb-2">
              <CardTitle>AI Detection Tool</CardTitle>
              <CardDescription>
                Paste text to check if it was written by AI
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AiDetectionTool /> {/* Using your AiDetectionTool component */}
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="bg-muted/50 py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Key Features
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our advanced detection technology offers multiple capabilities
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card className="bg-white dark:bg-gray-800/90">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
                    <Shield className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="font-bold">99% Accuracy</h3>
                  <p className="text-sm text-muted-foreground">
                    Industry-leading detection rates for all major AI models
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800/90">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30">
                    <Zap className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="font-bold">Fast Results</h3>
                  <p className="text-sm text-muted-foreground">
                    Get analysis results in seconds, not minutes
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800/90">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30">
                    <FileText className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h3 className="font-bold">Detailed Reports</h3>
                  <p className="text-sm text-muted-foreground">
                    Section-by-section analysis with confidence scores
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800/90">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-2">
                  <div className="p-2 rounded-full bg-amber-100 dark:bg-amber-900/30">
                    <CheckCircle className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                  </div>
                  <h3 className="font-bold">Multi-Model Detection</h3>
                  <p className="text-sm text-muted-foreground">
                    Detects GPT-4, Claude, Gemini, Llama, and more
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Related Tools Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Related Tools
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Explore our other tools to enhance your content workflow
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>AI Humanizer</CardTitle>
                <CardDescription>
                  Transform AI-generated text to sound more natural
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <p className="text-muted-foreground mb-4">
                  Make AI-written content sound more human while maintaining
                  your original meaning and bypassing detection.
                </p>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/tools/humanizer">
                  <Button variant="outline" className="w-full">
                    <span>Try AI Humanizer</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>

            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>Batch Processing</CardTitle>
                <CardDescription>
                  Analyze multiple documents simultaneously
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <p className="text-muted-foreground mb-4">
                  Process multiple documents at once to save time and increase
                  your productivity with our batch processing tool.
                </p>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/tools/batch-processing">
                  <Button variant="outline" className="w-full">
                    <span>Use Batch Processing</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>

            <Card className="flex flex-col">
              <CardHeader>
                <CardTitle>API Access</CardTitle>
                <CardDescription>
                  Integrate AI detection into your applications
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1">
                <p className="text-muted-foreground mb-4">
                  Access our AI detection capabilities programmatically through
                  our API to build custom integrations for your workflow.
                </p>
              </CardContent>
              <div className="p-6 pt-0">
                <Link href="/tools/api">
                  <Button variant="outline" className="w-full">
                    <span>Explore API</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24 bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="container px-4 md:px-6">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold tracking-tight">
                Ready to detect AI content at scale?
              </h2>
              <p className="text-lg text-blue-100">
                Our premium plans include additional features like batch
                processing, API access, and dedicated support to help you
                integrate AI detection into your workflow.
              </p>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="bg-white/20 p-1.5 rounded-full mt-0.5">
                    <CheckCircle className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">
                      Process up to 1,000 documents per month
                    </p>
                    <p className="text-sm text-blue-100">
                      Scale your detection capabilities
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-white/20 p-1.5 rounded-full mt-0.5">
                    <CheckCircle className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">Access our detection API</p>
                    <p className="text-sm text-blue-100">
                      Integrate with your existing systems
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="bg-white/20 p-1.5 rounded-full mt-0.5">
                    <CheckCircle className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <p className="font-medium">Priority support response</p>
                    <p className="text-sm text-blue-100">
                      Get help when you need it most
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Link href="https://app.studentaidetector.com/signup">
                  <Button
                    size="lg"
                    className="w-full sm:w-auto bg-white hover:bg-gray-100 text-blue-700"
                  >
                    Start Free Trial
                  </Button>
                </Link>
                <Link href="/pricing">
                  <Button
                    size="lg"
                    variant="outline"
                    className="w-full sm:w-auto border-white text-white hover:bg-white/10"
                  >
                    View Pricing
                  </Button>
                </Link>
              </div>
            </div>

            <div className="bg-white/10 p-6 md:p-8 rounded-xl border border-white/20 backdrop-blur-sm">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-full">
                    <Server className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold">Enterprise Features</h3>
                </div>
                <Badge className="bg-white/20 hover:bg-white/30 text-white">
                  Premium
                </Badge>
              </div>

              <div className="space-y-5">
                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                  <Copy className="h-5 w-5 text-blue-200" />
                  <div>
                    <p className="font-medium">Bulk Detection</p>
                    <p className="text-sm text-blue-100">
                      Upload multiple files at once
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                  <BookOpen className="h-5 w-5 text-blue-200" />
                  <div>
                    <p className="font-medium">Detailed Analytics</p>
                    <p className="text-sm text-blue-100">
                      Track usage and detection results
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-blue-200" />
                  <div>
                    <p className="font-medium">Priority Support</p>
                    <p className="text-sm text-blue-100">
                      Get help within 24 hours
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-white/10 rounded-lg">
                  <Shield className="h-5 w-5 text-blue-200" />
                  <div>
                    <p className="font-medium">Enhanced Security</p>
                    <p className="text-sm text-blue-100">
                      SOC 2 and FERPA compliant
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-12 md:py-16 bg-muted/30">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-3xl font-bold tracking-tight mb-2">
              Frequently Asked Questions
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Common questions about our AI detection technology
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1">
                <AccordionTrigger className="text-left font-medium">
                  How accurate is your AI detection technology?
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  Our AI detection technology achieves industry-leading accuracy
                  rates: 99% accuracy for GPT-4 generated content, 98% accuracy
                  for ChatGPT content, 97% accuracy for Claude and other AI
                  models. These rates have been validated through extensive
                  testing and independent verification. However, we acknowledge
                  that no AI detection system is perfect, and we continuously
                  update our algorithms to maintain high accuracy as AI models
                  evolve.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-2">
                <AccordionTrigger className="text-left font-medium">
                  How does your AI detector work?
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  Our AI detector uses a proprietary multi-layered approach that
                  combines neural network analysis, linguistic pattern
                  recognition, and probabilistic modeling. The system analyzes
                  various aspects of text, including statistical patterns,
                  syntactic structures, token predictability, and semantic
                  consistency to identify characteristics typical of
                  AI-generated content. Unlike simpler detectors, we don't rely
                  on a single method but instead use a cross-verification system
                  that combines multiple detection algorithms to reduce false
                  positives and increase accuracy.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-3">
                <AccordionTrigger className="text-left font-medium">
                  Can your detector identify content from all AI writing tools?
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  Our detector is trained to identify content from all major AI
                  writing models, including GPT-3.5, GPT-4, Claude, Bard, and
                  others. We continuously update our system to recognize new AI
                  models as they emerge. While we maintain high accuracy across
                  different AI systems, our detection rates may vary slightly
                  depending on the specific model used and how the AI output has
                  been modified. We're committed to staying ahead of the curve
                  as AI technology evolves.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-4">
                <AccordionTrigger className="text-left font-medium">
                  How much text do I need to submit for accurate detection?
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  For optimal accuracy, we recommend submitting at least 300
                  words (approximately one double-spaced page). While our system
                  can analyze shorter texts, the accuracy increases with longer
                  samples as this provides more linguistic patterns for our
                  algorithms to analyze. For texts under 100 words, we still
                  provide analysis but include a confidence rating to indicate
                  the reliability of the results.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-5">
                <AccordionTrigger className="text-left font-medium">
                  Do you store the text I submit for analysis?
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  We temporarily store submitted text to process your request
                  and provide results. This data is automatically deleted after
                  30 days. We never use your submitted content for training our
                  models without explicit permission, and we maintain strict
                  data privacy protocols. For educational institutions and
                  enterprise customers, we offer enhanced privacy options,
                  including immediate data deletion after analysis and custom
                  data retention policies.
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-6">
                <AccordionTrigger className="text-left font-medium">
                  What if a student disputes the AI detection results?
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground">
                  We recommend using our AI detector as one tool in a broader
                  academic integrity approach, not as the sole determinant. Our
                  detailed reports provide specific evidence of AI patterns,
                  which can facilitate constructive discussions with students.
                  For disputed cases, we offer an appeal process where our team
                  can provide a more in-depth analysis. We also provide
                  educational resources to help institutions develop fair
                  policies around AI use and detection.
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </div>

          <div className="text-center mt-10">
            <p className="text-muted-foreground mb-4">Still have questions?</p>
            <Link href="/contact">
              <Button>Contact Support</Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(toolSchema),
        }}
      />
    </main>
  );
}
