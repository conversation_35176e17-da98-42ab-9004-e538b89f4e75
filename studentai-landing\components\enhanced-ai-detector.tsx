"use client";

import { useState } from "react";
import {
  Shield,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  Copy,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";

export default function EnhancedAiDetector() {
  const [text, setText] = useState("");
  const [result, setResult] = useState<null | {
    score: number;
    verdict: string;
  }>(null);
  const [loading, setLoading] = useState(false);
  const [copied, setCopied] = useState(false);

  // Detect content
  const detectContent = () => {
    if (text.length < 10) return;

    setLoading(true);
    // Simulate API call with timeout
    setTimeout(() => {
      // Generate a mock result - in production this would come from your API
      const score = Math.random() * 0.7 + 0.3; // Between 0.3 and 1.0
      let verdict = "";

      if (score > 0.85) verdict = "Highly likely AI-generated";
      else if (score > 0.65) verdict = "Likely AI-generated";
      else if (score > 0.4) verdict = "Possibly AI-generated";
      else verdict = "Likely human-written";

      setResult({ score, verdict });
      setLoading(false);
    }, 1500);
  };

  // Copy to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  // Reset everything
  const resetDetector = () => {
    setText("");
    setResult(null);
  };

  return (
    <div className="w-full">
      {/* Text input area with animated border */}
      <div className="relative mb-4 group/detector">
        {/* Animated gradient border */}
        <div className="absolute -inset-0.5 bg-gradient-to-r from-sky-400 via-blue-500 to-indigo-500 rounded-lg blur opacity-30 group-hover/detector:opacity-70 transition duration-1000 group-hover/detector:duration-200 animate-gradient-x"></div>

        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Paste text here to analyze for AI-generated content..."
          className="w-full min-h-[150px] p-4 bg-white dark:bg-gray-900/90 dark:text-white text-gray-700 rounded-lg border border-gray-200 dark:border-gray-700/80 relative z-10 backdrop-blur-sm focus:ring-2 focus:ring-blue-500 focus:outline-none resize-none"
          disabled={loading}
        />

        {/* Character count */}
        <div className="absolute bottom-3 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono bg-white/80 dark:bg-gray-800/80 px-2 py-0.5 rounded">
          {text.length} chars
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex gap-2">
          <Button
            onClick={detectContent}
            disabled={text.length < 10 || loading}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white gap-2 relative overflow-hidden"
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Analyzing...</span>
              </>
            ) : (
              <>
                <Shield className="h-4 w-4" />
                <span>Detect AI Content</span>
              </>
            )}

            {/* Animated loading bar */}
            {loading && (
              <motion.div
                className="absolute bottom-0 left-0 h-0.5 bg-white"
                initial={{ width: "0%" }}
                animate={{ width: "100%" }}
                transition={{ duration: 1.5, ease: "linear" }}
              />
            )}
          </Button>

          <Button
            variant="outline"
            onClick={resetDetector}
            disabled={loading || (!text && !result)}
            className="border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300"
          >
            Reset
          </Button>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={copyToClipboard}
          disabled={!text || loading}
          className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
        >
          {copied ? (
            <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
          ) : (
            <Copy className="h-4 w-4 mr-1" />
          )}
          {copied ? "Copied!" : "Copy"}
        </Button>
      </div>

      {/* Result display */}
      <AnimatePresence>
        {result && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="rounded-lg overflow-hidden"
          >
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-900/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-gray-900 dark:text-white">
                  Detection Results
                </h3>
                <div
                  className={`px-2 py-1 rounded text-xs font-medium ${
                    result.score > 0.7
                      ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                      : result.score > 0.4
                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                      : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                  }`}
                >
                  {result.score > 0.7
                    ? "High AI Probability"
                    : result.score > 0.4
                    ? "Medium AI Probability"
                    : "Low AI Probability"}
                </div>
              </div>

              <div className="mb-4">
                <div className="flex justify-between text-sm mb-1">
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    AI Detection Score
                  </span>
                  <span
                    className={`font-bold ${
                      result.score > 0.7
                        ? "text-red-600 dark:text-red-400"
                        : result.score > 0.4
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-green-600 dark:text-green-400"
                    }`}
                  >
                    {Math.round(result.score * 100)}%
                  </span>
                </div>

                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${result.score * 100}%` }}
                    transition={{ duration: 0.8, delay: 0.1 }}
                    className={`h-full rounded-full ${
                      result.score > 0.7
                        ? "bg-red-500"
                        : result.score > 0.4
                        ? "bg-yellow-500"
                        : "bg-green-500"
                    }`}
                  />
                </div>
              </div>

              <div className="flex items-start gap-2 p-3 rounded bg-gray-100 dark:bg-gray-800/80">
                {result.score > 0.7 ? (
                  <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                ) : result.score > 0.4 ? (
                  <AlertCircle className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
                ) : (
                  <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                )}
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    {result.verdict}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {result.score > 0.7
                      ? "This text shows strong indicators of being generated by an AI language model like GPT-4 or similar."
                      : result.score > 0.4
                      ? "This text contains some patterns consistent with AI-generated content, but may be mixed with human writing."
                      : "This text exhibits characteristics typically associated with human-written content."}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
