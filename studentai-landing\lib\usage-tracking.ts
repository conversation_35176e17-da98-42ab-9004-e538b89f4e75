export interface UsageStats {
  detectionQueries: {
    used: number
    limit: number
    lastReset: string
  }
  humanizationTools: {
    used: number
    limit: number
    lastReset: string
  }
  apiCalls: {
    used: number
    limit: number
    lastReset: string
  }
  batchProcessing: {
    used: number
    limit: number
    lastReset: string
  }
}

export interface PlanLimits {
  detectionQueries: number
  wordLimit: number
  humanizationTools: number | "unlimited"
  fileFormats: string[]
  batchProcessing: number | false
  historyRetention: number // days
  apiCalls: number | false
  users: number
}

// Plan limits for each subscription tier
export const PLAN_LIMITS: Record<string, PlanLimits> = {
  free: {
    detectionQueries: 10,
    wordLimit: 1500,
    humanizationTools: 3,
    fileFormats: [".txt"],
    batchProcessing: false,
    historyRetention: 7,
    apiCalls: false,
    users: 1,
  },
  basic: {
    detectionQueries: 100,
    wordLimit: 5000,
    humanizationTools: 50,
    fileFormats: [".txt", ".pdf", ".docx"],
    batchProcessing: 5,
    historyRetention: 30,
    apiCalls: false,
    users: 5,
  },
  pro: {
    detectionQueries: 500,
    wordLimit: 15000,
    humanizationTools: "unlimited",
    fileFormats: ["all"],
    batchProcessing: 20,
    historyRetention: 90,
    apiCalls: 100,
    users: 20,
  },
  enterprise: {
    detectionQueries: 10000,
    wordLimit: 50000,
    humanizationTools: "unlimited",
    fileFormats: ["all"],
    batchProcessing: 100,
    historyRetention: 365,
    apiCalls: 10000,
    users: 100,
  },
}

// In a real application, this would interact with a database
export class UsageTracker {
  // Track usage for a specific user
  static async trackDetectionQuery(userId: string): Promise<boolean> {
    // In a real app, this would:
    // 1. Get the user's current usage from the database
    // 2. Check if they've exceeded their limit
    // 3. If not, increment their usage and return true
    // 4. If they have, return false

    console.log(`Tracking detection query for user ${userId}`)
    return true
  }

  // Track humanization tool usage
  static async trackHumanizationTool(userId: string, toolType: string): Promise<boolean> {
    console.log(`Tracking humanization tool usage (${toolType}) for user ${userId}`)
    return true
  }

  // Track API call
  static async trackApiCall(apiKey: string, endpoint: string): Promise<boolean> {
    console.log(`Tracking API call to ${endpoint} with key ${apiKey}`)
    return true
  }

  // Get current usage stats for a user
  static async getUserUsage(userId: string): Promise<UsageStats> {
    // In a real app, this would fetch from a database
    return {
      detectionQueries: {
        used: 5,
        limit: 100,
        lastReset: new Date().toISOString(),
      },
      humanizationTools: {
        used: 2,
        limit: 50,
        lastReset: new Date().toISOString(),
      },
      apiCalls: {
        used: 0,
        limit: 0,
        lastReset: new Date().toISOString(),
      },
      batchProcessing: {
        used: 0,
        limit: 5,
        lastReset: new Date().toISOString(),
      },
    }
  }

  // Reset usage counters (typically done monthly)
  static async resetUsage(userId: string): Promise<void> {
    console.log(`Resetting usage for user ${userId}`)
    // In a real app, this would reset counters in the database
  }

  // Check if a user can perform an action based on their current usage
  static async canPerformAction(
    userId: string,
    actionType: "detection" | "humanization" | "api" | "batch",
  ): Promise<boolean> {
    const usage = await this.getUserUsage(userId)

    switch (actionType) {
      case "detection":
        return usage.detectionQueries.used < usage.detectionQueries.limit
      case "humanization":
        return usage.humanizationTools.used < usage.humanizationTools.limit
      case "api":
        return usage.apiCalls.used < usage.apiCalls.limit
      case "batch":
        return usage.batchProcessing.used < usage.batchProcessing.limit
      default:
        return false
    }
  }
}
