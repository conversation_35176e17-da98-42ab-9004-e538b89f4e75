"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function AcademicIntegrityDashboard() {
  const [activeTab, setActiveTab] = useState("features")

  // Mock data for aggregate statistics
  const overallAccuracy = 94.8
  const falsePositiveRate = 3.2
  const continuousImprovement = 1.2

  // Mock data for accuracy comparison
  const accuracyComparisonData = [
    { label: "StudentAIDetector", accuracy: 94.8, falsePositive: 3.2 },
    { label: "Alternative A", accuracy: 54.2, falsePositive: 8.1 },
    { label: "Alternative B", accuracy: 50.1, falsePositive: 10.5 },
  ]

  return (
    <div className="space-y-6">
      {/* Aggregate Statistics */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Overall Accuracy</CardTitle>
            <CardDescription>Based on our comprehensive testing across diverse writing samples</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{overallAccuracy}%</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">False Positive Rate</CardTitle>
            <CardDescription>
              Industry-leading low rate of incorrectly flagging human content as AI-generated
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{falsePositiveRate}%</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Continuous Improvement</CardTitle>
            <CardDescription>Accuracy improvement in the last quarter through model refinement</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">+{continuousImprovement}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Accuracy Comparison Chart */}
      <Card>
        <CardHeader>
          <CardTitle>AI Detection Accuracy Comparison</CardTitle>
          <CardDescription>Comparing StudentAIDetector to other AI detection systems</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr>
                  <th className="text-left">System</th>
                  <th className="text-center">Accuracy (%)</th>
                  <th className="text-center">False Positive Rate (%)</th>
                </tr>
              </thead>
              <tbody>
                {accuracyComparisonData.map((item) => (
                  <tr key={item.label}>
                    <td className="py-2">{item.label}</td>
                    <td className="text-center">{item.accuracy}</td>
                    <td className="text-center">{item.falsePositive}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Our Methodology Section */}
      <Card>
        <CardHeader>
          <CardTitle>Our Methodology</CardTitle>
          <CardDescription>An overview of our AI detection process</CardDescription>
        </CardHeader>
        <CardContent>
          <p>
            StudentAIDetector uses a multi-layered approach to identify AI-generated content. Our system analyzes
            linguistic patterns, sentence structures, and statistical features that differentiate AI-generated text from
            human writing. We continuously update our algorithms to stay ahead of evolving AI technology.
          </p>
        </CardContent>
      </Card>

      {/* Addressing False Positives Section */}
      <Card>
        <CardHeader>
          <CardTitle>Addressing False Positives</CardTitle>
          <CardDescription>
            Our commitment to reducing false positives, especially for non-native English writers and neurodiverse
            students
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3">
              <TabsTrigger value="features">Features</TabsTrigger>
              <TabsTrigger value="appeals">Appeals Process</TabsTrigger>
              <TabsTrigger value="research">Ongoing Research</TabsTrigger>
            </TabsList>
            <TabsContent value="features" className="mt-4">
              <p>
                We employ several features to minimize false positives, including sensitivity settings, detailed reports
                with highlighted sections, and a focus on identifying patterns rather than individual words or phrases.
              </p>
            </TabsContent>
            <TabsContent value="appeals" className="mt-4">
              <p>
                If you believe a false positive has occurred, we provide a clear and easy-to-use appeals process. Our
                team of experts will review the submission and provide a determination within 24 hours.
              </p>
            </TabsContent>
            <TabsContent value="research" className="mt-4">
              <p>
                We are committed to ongoing research and development to improve the accuracy of our AI detection system
                and reduce the risk of false positives. We regularly publish our findings and collaborate with
                educational institutions to ensure our tools are fair and effective.
              </p>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
