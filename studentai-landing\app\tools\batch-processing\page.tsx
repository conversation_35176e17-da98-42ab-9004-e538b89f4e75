import type { Metadata } from "next"
import { generateMetaTags } from "@/lib/seo-utils"
import BatchProcessingClientPage from "./BatchProcessingClientPage"

export const metadata: Metadata = generateMetaTags({
  title: "Batch Processing Tool - Analyze Multiple Documents at Once",
  description:
    "Analyze multiple documents simultaneously with our batch processing tool. Save time and increase efficiency with StudentAIDetector.",
  keywords: [
    "batch processing tool",
    "analyze multiple documents",
    "AI detection batch processing",
    "content analysis batch",
    "bulk AI detection",
  ],
  canonical: "/tools/batch-processing",
})

export default function BatchProcessingPage() {
  return <BatchProcessingClientPage />
}
