"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart2,
  Search,
  Sparkles,
  AlertCircle,
  CheckCircle,
  FileText,
  RotateCw,
  ArrowRight,
  Cpu,
  User,
  ExternalLink,
  ZoomIn,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";

export default function VisualProof() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeExample, setActiveExample] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [showResult, setShowResult] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Intersection Observer to trigger animations when section is visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = sectionRef.current;
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  // Examples of AI-generated and human-written content
  const examples = [
    {
      type: "AI-Generated Essay",
      content: `Renewable energy sources represent a critical pathway to addressing climate change and securing our planet's future. Wind and solar power, in particular, have seen remarkable advancements in efficiency and cost-effectiveness over the past decade, making them increasingly viable alternatives to fossil fuels. These technologies not only reduce carbon emissions but also create new economic opportunities and enhance energy independence. As we continue to develop and implement renewable energy solutions, it is essential to consider the broader ecological and social implications of our energy choices. By transitioning to a more sustainable energy infrastructure, we can mitigate the worst effects of climate change while building a more resilient and equitable society.`,
      aiProbability: 92,
      author: "ChatGPT",
      highlights: [
        { start: 0, end: 89, confidence: "high" },
        { start: 90, end: 216, confidence: "medium" },
        { start: 217, end: 329, confidence: "high" },
        { start: 330, end: 489, confidence: "medium" },
      ],
      patterns: [
        "Generic language",
        "Perfect grammar",
        "Lack of personal perspective",
        "Formal structure",
      ],
    },
    {
      type: "Human-Written Article",
      content: `I've been researching climate change solutions for nearly 15 years now, and I'm still struck by how complex the renewable energy transition actually is. Wind farms might seem like an obvious solution, but try telling that to the local communities who've spent years fighting against turbine installations that they say ruin their countryside views! And don't get me started on the rare earth minerals needed for solar panels – we rarely talk about the environmental impact of mining those. Last summer, I visited a small town in Germany that powers itself entirely on local energy sources. What surprised me most wasn't the technology (which was impressive), but the community's pride in what they'd built together. That's something you can't engineer.`,
      aiProbability: 12,
      author: "Prof. Sarah Johnson",
      highlights: [
        { start: 127, end: 135, confidence: "low" },
        { start: 242, end: 260, confidence: "low" },
      ],
      patterns: [
        "Personal anecdotes",
        "Varied sentence structure",
        "Informal language",
        "Specific details",
      ],
    },
    {
      type: "Mixed Content",
      content: `Artificial Intelligence has rapidly transformed numerous industries in recent years. From healthcare to finance, the implementation of AI technologies has increased efficiency and enabled new capabilities that were previously impossible. My own experience with AI began in 2018 when I joined a startup developing machine learning solutions for small businesses. We faced countless challenges and setbacks - our first algorithm was honestly terrible! But through persistence and late nights (fueled by way too much coffee), we eventually created something that actually worked. The potential of AI continues to expand as computational power increases and algorithms become more sophisticated, though we must carefully consider the ethical implications of these powerful technologies.`,
      aiProbability: 45,
      author: "Mixed Sources",
      highlights: [
        { start: 0, end: 83, confidence: "high" },
        { start: 84, end: 190, confidence: "low" },
        { start: 191, end: 320, confidence: "low" },
        { start: 321, end: 467, confidence: "high" },
      ],
      patterns: [
        "Contrast between formal and informal sections",
        "Shifting perspective",
        "Inconsistent voice",
      ],
    },
  ];

  // Function to simulate analysis
  const runAnalysis = () => {
    setIsAnalyzing(true);
    setAnalysisProgress(0);
    setShowResult(false);

    const interval = setInterval(() => {
      setAnalysisProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsAnalyzing(false);
          setShowResult(true);
          return 100;
        }
        return prev + 2;
      });
    }, 50);

    return () => clearInterval(interval);
  };

  return (
    <section
      ref={sectionRef}
      className="w-full py-16 md:py-28 bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden"
    >
      {/* Decorative background elements */}
      <div className="absolute top-0 right-1/4 w-72 h-72 bg-purple-100/30 dark:bg-purple-900/10 rounded-full blur-3xl -z-10" />
      <div className="absolute bottom-0 left-1/3 w-64 h-64 bg-blue-100/30 dark:bg-blue-900/10 rounded-full blur-3xl -z-10" />

      {/* Code-like abstract background pattern */}
      <div className="absolute inset-0 -z-5 opacity-5 dark:opacity-10 overflow-hidden">
        <div className="w-full h-full flex flex-wrap content-start">
          {[...Array(40)].map((_, i) => (
            <div
              key={i}
              className="text-xs font-mono text-blue-900 dark:text-blue-300"
              style={{
                position: "absolute",
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: Math.random() * 0.5 + 0.2,
                transform: `rotate(${Math.random() * 360}deg)`,
              }}
            >
              {
                ["0100", "1010", "AI?", "0011", "1001", "ML", "0101"][
                  Math.floor(Math.random() * 7)
                ]
              }
            </div>
          ))}
        </div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative">
        <div
          className={`text-center mb-12 ${
            isVisible ? "animate-fade-in" : "opacity-0"
          }`}
        >
          <div className="inline-flex items-center gap-2 mb-2 px-4 py-1.5 bg-blue-100/80 dark:bg-blue-900/30 backdrop-blur-sm rounded-full border border-blue-200/40 dark:border-blue-700/40 shadow-sm">
            <ZoomIn className="h-4 w-4 text-blue-500 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              See It In Action
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mt-4">
            Visual Proof Of Accuracy
          </h2>

          <p className="max-w-[800px] mx-auto mt-4 text-gray-600 dark:text-gray-300 md:text-xl/relaxed">
            See our AI detection technology in action with real examples of
            AI-generated and human-written content.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 max-w-6xl mx-auto mb-12">
          {examples.map((example, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.2 + i * 0.2 }}
            >
              <Card
                className={`h-full transition-all duration-300 hover:shadow-lg ${
                  activeExample === i
                    ? "ring-2 ring-blue-500 dark:ring-blue-400"
                    : "hover:scale-[1.02]"
                }`}
                onClick={() => setActiveExample(i)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Badge
                      variant="outline"
                      className={`px-2.5 py-0.5 ${
                        example.type.includes("AI")
                          ? "border-red-200 dark:border-red-800 text-red-700 dark:text-red-400"
                          : example.type.includes("Human")
                          ? "border-green-200 dark:border-green-800 text-green-700 dark:text-green-400"
                          : "border-amber-200 dark:border-amber-800 text-amber-700 dark:text-amber-400"
                      }`}
                    >
                      {example.type}
                    </Badge>

                    <div className="flex items-center gap-1 text-sm">
                      {example.type.includes("AI") ? (
                        <Cpu className="h-3.5 w-3.5 text-red-500" />
                      ) : example.type.includes("Human") ? (
                        <User className="h-3.5 w-3.5 text-green-500" />
                      ) : (
                        <FileText className="h-3.5 w-3.5 text-amber-500" />
                      )}
                      <span className="font-medium">{example.author}</span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-700 dark:text-gray-300 mb-4 h-[150px] overflow-y-auto relative">
                    {showResult && activeExample === i ? (
                      <div className="relative">
                        {example.content.split("").map((char, index) => {
                          const highlight = example.highlights.find(
                            (h) => index >= h.start && index <= h.end
                          );
                          return (
                            <span
                              key={index}
                              className={
                                highlight
                                  ? `bg-red-100 dark:bg-red-900/30 ${
                                      highlight.confidence === "high"
                                        ? "text-red-700 dark:text-red-300"
                                        : "text-red-600/70 dark:text-red-300/70"
                                    }`
                                  : ""
                              }
                            >
                              {char}
                            </span>
                          );
                        })}
                      </div>
                    ) : (
                      <p>{example.content}</p>
                    )}
                  </div>

                  {showResult && activeExample === i && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">AI Probability</span>
                        <span className="font-bold">
                          {example.aiProbability}%
                        </span>
                      </div>
                      <Progress
                        value={example.aiProbability}
                        className={`h-2 ${
                          example.aiProbability > 70
                            ? "bg-red-100 dark:bg-red-900/30"
                            : example.aiProbability < 30
                            ? "bg-green-100 dark:bg-green-900/30"
                            : "bg-amber-100 dark:bg-amber-900/30"
                        }`}
                        indicatorClassName={
                          example.aiProbability > 70
                            ? "bg-red-500 dark:bg-red-400"
                            : example.aiProbability < 30
                            ? "bg-green-500 dark:bg-green-400"
                            : "bg-amber-500 dark:bg-amber-400"
                        }
                      />

                      <div className="mt-4">
                        <p className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
                          Detected patterns:
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {example.patterns.map((pattern, idx) => (
                            <Badge
                              key={idx}
                              variant="secondary"
                              className="text-xs"
                            >
                              {pattern}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        <div className="flex flex-col items-center">
          <Button
            size="lg"
            onClick={runAnalysis}
            className="relative overflow-hidden group"
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <>
                <RotateCw className="h-4 w-4 mr-2 animate-spin" />
                Analyzing
              </>
            ) : showResult ? (
              <>
                <BarChart2 className="h-4 w-4 mr-2" />
                View Another Analysis
              </>
            ) : (
              <>
                <Search className="h-4 w-4 mr-2" />
                Analyze Sample
              </>
            )}
          </Button>

          {isAnalyzing && (
            <Progress value={analysisProgress} className="w-40 h-1 mt-3" />
          )}
        </div>
      </div>
    </section>
  );
}
