import type React from "react";
import type { Metada<PERSON> } from "next";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Wand2, Shield, ArrowRight } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: "AI Detection Tools - StudentAIDetector",
  description:
    "Our powerful AI detector and humanizer tools help educators identify AI-written content and convert AI text to human-like writing.",
  keywords: [
    "AI detection tools",
    "AI text humanizer",
    "academic integrity tools",
    "AI content detection",
  ],
  alternates: {
    canonical: "/tools",
  },
  openGraph: {
    title: "AI Detection Tools - StudentAIDetector",
    description:
      "Our powerful AI detector and humanizer tools help educators identify AI-written content and convert AI text to human-like writing.",
    url: "/tools",
    type: "website",
  },
};

export default function ToolsPage() {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container py-12 pt-28 max-w-6xl">
        {/* Hero section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Essential Tools for Educators
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Powerful AI detection and humanization tools designed specifically
            for academic environments
          </p>
        </div>

        {/* Tools section */}
        <div className="grid gap-8 md:grid-cols-2 mb-16">
          <ToolCard
            icon={<Shield className="h-6 w-6" />}
            title="AI Text Detector"
            description="Identify AI-generated content with our advanced detection engine that uses over 85 linguistic pattern markers for 99.4% accuracy."
            features={[
              "Real-time content analysis",
              "Identifies specific AI models used",
              "Detailed pattern reports",
              "Supports 30+ languages",
            ]}
            plan="free"
            href="/"
            cta="Detect AI Content"
            color="blue"
          />

          <ToolCard
            icon={<Wand2 className="h-6 w-6" />}
            title="AI Humanizer"
            description="Transform AI-generated text into natural-sounding human writing while preserving the original meaning and educational value."
            features={[
              "Preserves original meaning",
              "Removes AI linguistic patterns",
              "Adjusts vocabulary and syntax",
              "Creates natural-sounding text",
            ]}
            plan="basic"
            href="/pricing"
            cta="Get Started"
            color="purple"
          />
        </div>

        {/* Feature comparison */}
      </div>
      <Footer />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "ItemList",
            itemListElement: [
              {
                "@type": "ListItem",
                position: 1,
                name: "AI Text Detector",
                description:
                  "Identify AI-generated content with our advanced detection engine that uses over 85 linguistic pattern markers for 99.4% accuracy.",
                url: "https://studentaidetector.com/",
              },
              {
                "@type": "ListItem",
                position: 2,
                name: "AI Humanizer",
                description:
                  "Transform AI-generated text into natural-sounding human writing while preserving the original meaning and educational value.",
                url: "https://studentaidetector.com/pricing",
              },
            ],
          }),
        }}
      />
    </main>
  );
}

interface ToolCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  plan: "free" | "basic" | "pro";
  href: string;
  cta: string;
  color: "blue" | "purple";
}

function ToolCard({
  icon,
  title,
  description,
  features,
  plan,
  href,
  cta,
  color,
}: ToolCardProps) {
  return (
    <Card className="flex flex-col overflow-hidden border-0 shadow-lg">
      <div
        className={`h-2 ${color === "blue" ? "bg-blue-600" : "bg-purple-600"}`}
      />
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className={`p-2.5 rounded-lg ${
                color === "blue"
                  ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                  : "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400"
              }`}
            >
              {icon}
            </div>
            <CardTitle className="text-xl">{title}</CardTitle>
          </div>
          <Badge
            variant={
              plan === "free"
                ? "outline"
                : plan === "basic"
                ? "secondary"
                : "default"
            }
            className={`${
              plan === "free"
                ? "border-blue-200 text-blue-700 dark:border-blue-800 dark:text-blue-300"
                : plan === "basic"
                ? "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300"
                : ""
            }`}
          >
            {plan === "free" ? "Free" : plan === "basic" ? "Basic" : "Pro"}
          </Badge>
        </div>
        <CardDescription className="pt-3 text-base">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1">
        <div className="mt-2 space-y-2">
          {features.map((feature, i) => (
            <div key={i} className="flex items-start">
              <div
                className={`mr-2 mt-1 h-1.5 w-1.5 rounded-full ${
                  color === "blue" ? "bg-blue-500" : "bg-purple-500"
                }`}
              />
              <span className="text-sm">{feature}</span>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="pt-4">
        <Link href={href} className="w-full">
          <Button
            className={`w-full ${
              color === "blue"
                ? "bg-blue-600 hover:bg-blue-700 text-white"
                : "bg-purple-600 hover:bg-purple-700 text-white"
            }`}
            size="lg"
          >
            {cta}
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}
