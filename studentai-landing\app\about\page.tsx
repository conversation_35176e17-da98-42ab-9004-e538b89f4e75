import type { <PERSON><PERSON><PERSON> } from "next";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CheckCircle, FileText, Shield, Zap, Award, Globe } from "lucide-react";
import { generateMetaTags } from "@/lib/seo-utils";
import Link from "next/link";
import Image from "next/image";
import aiDetectionTimeline from "@/public/images/about/ai-detection-timeline.png";
import howAiDetectionWorks from "@/public/images/about/how-modern-ai-detection-works.png";

export const metadata: Metadata = generateMetaTags({
  title: "About StudentAIDetector - Our Mission and Technology",
  description:
    "Learn about StudentAIDetector's mission to preserve academic integrity in the age of AI. Discover how our technology works to detect AI-generated content and improve writing.",
  keywords: [
    "about StudentAIDetector",
    "AI detection technology",
    "academic integrity mission",
  ],
  canonical: "/about",
});

export default function AboutPage() {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      {/* Hero Section */}
      <section className="bg-muted/50 py-12 md:py-20">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto space-y-4 text-center">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              About StudentAIDetector
            </h1>
            <p className="text-xl text-muted-foreground">
              Preserving academic integrity in the age of AI
            </p>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto space-y-6">
            <h2 className="text-2xl md:text-3xl font-bold">Our Mission</h2>
            <p className="text-muted-foreground text-lg">
              StudentAIDetector was created to help educators, institutions, and
              students navigate the complex landscape of AI-generated content.
              Our mission is to promote academic integrity while acknowledging
              the evolving role of AI in education.
            </p>
            <p className="text-muted-foreground text-lg">
              We believe in creating tools that foster honest academic work and
              help maintain the value of human creativity, critical thinking,
              and original expression in educational settings.
            </p>
            <p className="text-muted-foreground text-lg">
              At the same time, we recognize that AI tools can be valuable
              learning aids when used responsibly. That's why we offer both
              detection and improvement tools—to help users identify AI content
              and learn to use AI ethically and effectively.
            </p>
          </div>
        </div>
      </section>

      {/* Core Values Section */}
      <section className="bg-muted/50 py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-3xl font-bold mb-2">
              Our Core Values
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              The principles that guide our work and development
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader>
                <div className="p-2 rounded-full bg-primary/10 w-fit mb-2">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Privacy First</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We prioritize user privacy and data security. All text
                  analysis is performed securely, and we never store or share
                  your content without explicit permission.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="p-2 rounded-full bg-primary/10 w-fit mb-2">
                  <CheckCircle className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Accuracy Focused</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Our detection algorithms are continuously improved to provide
                  the most accurate assessment of AI-generated content,
                  minimizing false positives.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="p-2 rounded-full bg-primary/10 w-fit mb-2">
                  <FileText className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Educational Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Beyond detection, we provide insights that help educators
                  understand patterns in AI-generated text and guide students
                  toward authentic learning.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="p-2 rounded-full bg-primary/10 w-fit mb-2">
                  <Zap className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Cutting-Edge Technology</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We leverage the latest advancements in natural language
                  processing and machine learning to stay ahead of evolving AI
                  text generation capabilities.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto space-y-6">
            <h2 className="text-2xl md:text-3xl font-bold">Our Story</h2>
            <p className="text-muted-foreground text-lg">
              StudentAIDetector was founded in 2023 by a team of educators, AI
              researchers, and software developers who recognized the profound
              impact that generative AI tools like ChatGPT would have on
              education.
            </p>
            <p className="text-muted-foreground text-lg">
              As AI writing tools became increasingly sophisticated, we saw both
              the opportunities and challenges they presented. While these tools
              could help students learn and create, they also raised concerns
              about academic integrity and the development of essential writing
              and critical thinking skills.
            </p>
            <p className="text-muted-foreground text-lg">
              We created StudentAIDetector to address these challenges—providing
              educators with reliable tools to identify AI-generated content
              while also helping students and writers improve their AI-assisted
              work to make it more original, authentic, and valuable.
            </p>
            <p className="text-muted-foreground text-lg">
              Today, our platform serves thousands of educators, students, and
              content professionals worldwide, evolving alongside AI technology
              to ensure academic integrity in the digital age.
            </p>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="bg-muted/50 py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl mx-auto space-y-6">
            <h2 className="text-2xl md:text-3xl font-bold">
              How Our Technology Works
            </h2>
            <p className="text-muted-foreground text-lg">
              StudentAIDetector uses a sophisticated multi-layered approach to
              analyze text:
            </p>

            <Image
              src={aiDetectionTimeline || "/placeholder.svg"}
              alt="Timeline of AI detection development"
              width={800}
              height={400}
              className="w-full h-auto mb-6"
            />

            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="p-2 rounded-full bg-primary/10 mt-1">
                  <Shield className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-1">
                    Pattern Recognition Algorithms
                  </h3>
                  <p className="text-muted-foreground">
                    Our system identifies statistical patterns common in
                    AI-generated text that humans typically don't produce. These
                    include consistent sentence structures, predictable
                    transitions, and other subtle patterns.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="p-2 rounded-full bg-primary/10 mt-1">
                  <FileText className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-1">
                    Linguistic Analysis
                  </h3>
                  <p className="text-muted-foreground">
                    We examine sentence structure, vocabulary usage, and
                    stylistic consistency to identify the telltale signs of AI
                    writing. Human writing typically shows more variation and
                    personality than AI-generated content.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="p-2 rounded-full bg-primary/10 mt-1">
                  <Zap className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-1">
                    Contextual Evaluation
                  </h3>
                  <p className="text-muted-foreground">
                    Our system assesses the coherence and relevance of content
                    within its intended purpose. AI models sometimes generate
                    text that sounds good but lacks the specific context or
                    nuanced understanding that human writers provide.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="p-2 rounded-full bg-primary/10 mt-1">
                  <CheckCircle className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-1">
                    Comparative Benchmarking
                  </h3>
                  <p className="text-muted-foreground">
                    We compare text against known AI models and their output
                    characteristics. Our system is continuously updated to
                    recognize content from the latest AI models, including
                    ChatGPT, GPT-4, Claude, and others.
                  </p>
                </div>
              </div>
            </div>

            <Image
              src={howAiDetectionWorks || "/placeholder.svg"}
              alt="How AI detection works"
              width={800}
              height={600}
              className="w-full h-auto mb-6"
            />

            <p className="text-muted-foreground text-lg mt-6">
              The result is a comprehensive probability score that indicates the
              likelihood of AI involvement in content creation, along with
              specific highlighted sections that show the strongest indicators.
            </p>
          </div>
        </div>
      </section>

      {/* Team Section */}

      {/* Recognition Section */}
      <section className="bg-muted/50 py-12 md:py-16">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-3xl font-bold mb-2">
              Recognition & Partners
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Trusted by leading educational institutions and organizations
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <div className="p-2 rounded-full bg-primary/10 w-fit mb-2">
                  <Award className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Awards & Recognition</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                    <span>
                      2024 EdTech Breakthrough Award for "Best AI Detection
                      Solution"
                    </span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                    <span>
                      Featured in Education Weekly's "Top 10 Tools for Academic
                      Integrity"
                    </span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                    <span>2023 Innovation in Education Technology Award</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="p-2 rounded-full bg-primary/10 w-fit mb-2">
                  <Globe className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Educational Partners</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                    <span>
                      Member of the Academic Integrity Technology Consortium
                    </span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                    <span>
                      Partner with 500+ educational institutions worldwide
                    </span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                    <span>
                      Collaborator with the Center for Digital Ethics in
                      Education
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 md:py-16">
        <div className="container px-4 md:px-6 text-center">
          <h2 className="text-3xl font-bold tracking-tight mb-4">
            Join Us in Preserving Academic Integrity
          </h2>
          <p className="text-xl mb-6 max-w-2xl mx-auto text-muted-foreground">
            Start using StudentAIDetector today and be part of the solution
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="https://app.studentaidetector.com/login">
              <Button size="lg">Try For Free</Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline">
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
