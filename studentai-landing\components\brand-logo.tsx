import Image from "next/image"

interface BrandLogoProps {
  size?: "sm" | "md" | "lg"
  showText?: boolean
}

export function BrandLogo({ size = "md", showText = true }: BrandLogoProps) {
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8",
    lg: "w-10 h-10",
  }

  const iconSize = {
    sm: 24,
    md: 32,
    lg: 40,
  }

  const textSize = {
    sm: "text-base",
    md: "text-xl",
    lg: "text-2xl",
  }

  return (
    <div className="flex items-center gap-2">
      <div className={`${sizeClasses[size]} relative`}>
        <Image
          src="/icon.png"
          alt="StudentAIDetector logo"
          width={iconSize[size]}
          height={iconSize[size]}
          className="w-full h-full"
        />
      </div>
      {showText && (
        <span className={`font-bold ${textSize[size]} text-foreground`}>
          Student<span className="text-primary">AI</span>Detector
        </span>
      )}
    </div>
  )
}
