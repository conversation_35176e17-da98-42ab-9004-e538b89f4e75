"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"

interface SettingsState {
  similarityThreshold: number
  compareWithPrevious: boolean
  checkWebSources: boolean
  checkForCollusion: boolean
  checkForAiGenerated: boolean
  reportFormat: string
  emailNotifications: boolean
  notificationEmail: string
  retentionPeriod: string
}

export function Settings() {
  const { toast } = useToast()

  const [settings, setSettings] = useState<SettingsState>({
    similarityThreshold: 30,
    compareWithPrevious: true,
    checkWebSources: true,
    checkForCollusion: true,
    checkForAiGenerated: true,
    reportFormat: "pdf",
    emailNotifications: false,
    notificationEmail: "",
    retentionPeriod: "3months",
  })

  useEffect(() => {
    // Load settings from localStorage if available
    const savedSettings = localStorage.getItem("academicIntegritySettings")
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings)
        setSettings({ ...settings, ...parsedSettings })
      } catch (err) {
        console.error("Error loading settings:", err)
      }
    }
  }, [])

  const handleSaveSettings = () => {
    // Save settings to localStorage
    localStorage.setItem("academicIntegritySettings", JSON.stringify(settings))

    toast({
      title: "Settings saved",
      description: "Your academic integrity monitoring preferences have been updated.",
    })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Analysis Settings</CardTitle>
          <CardDescription>Configure how submissions are analyzed for academic integrity issues</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="similarity-threshold">Similarity Threshold (%)</Label>
              <span>{settings.similarityThreshold}%</span>
            </div>
            <Slider
              id="similarity-threshold"
              min={5}
              max={95}
              step={5}
              value={[settings.similarityThreshold]}
              onValueChange={(value) => setSettings({ ...settings, similarityThreshold: value[0] })}
            />
            <p className="text-xs text-muted-foreground">
              Text with similarity above this threshold will be flagged as potential integrity issues
            </p>
          </div>

          <Separator />

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="compare-previous">Compare with Previous Submissions</Label>
                <p className="text-xs text-muted-foreground">
                  Check for similarities with other student submissions in your history
                </p>
              </div>
              <Switch
                id="compare-previous"
                checked={settings.compareWithPrevious}
                onCheckedChange={(checked) => setSettings({ ...settings, compareWithPrevious: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="check-web">Check Web Sources</Label>
                <p className="text-xs text-muted-foreground">
                  Compare text against web sources including academic journals, Wikipedia, etc.
                </p>
              </div>
              <Switch
                id="check-web"
                checked={settings.checkWebSources}
                onCheckedChange={(checked) => setSettings({ ...settings, checkWebSources: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="check-collusion">Check for Collusion</Label>
                <p className="text-xs text-muted-foreground">Identify potential collaboration between students</p>
              </div>
              <Switch
                id="check-collusion"
                checked={settings.checkForCollusion}
                onCheckedChange={(checked) => setSettings({ ...settings, checkForCollusion: checked })}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="check-ai">Check for AI-Generated Content</Label>
                <p className="text-xs text-muted-foreground">
                  Integrate with AI detection to identify content written by AI tools
                </p>
              </div>
              <Switch
                id="check-ai"
                checked={settings.checkForAiGenerated}
                onCheckedChange={(checked) => setSettings({ ...settings, checkForAiGenerated: checked })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Button onClick={handleSaveSettings}>Save Settings</Button>
    </div>
  )
}
