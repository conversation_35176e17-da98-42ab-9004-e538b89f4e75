import Image from "next/image";
import {
  AlertTriangle,
  Users,
  TrendingUp,
  ChartBar,
  Briefcase,
  Newspaper,
  BookOpen,
  ShoppingBag,
} from "lucide-react";
import { useState } from "react";

export default function ProblemSection() {
  const [activeTab, setActiveTab] = useState("marketing");

  // Case studies data for different industries
  const caseStudies = {
    marketing: {
      icon: (
        <ShoppingBag className="h-5 w-5 text-blue-600 dark:text-blue-400" />
      ),
      title: "Meet Alex's Challenge",
      content:
        "<PERSON> leads content marketing at a growing SaaS company. Recently, their team started using AI to scale content production. While the AI-written pieces were SEO-optimized, engagement metrics were down 32%. Readers could sense something was off, and the authentic brand voice was missing.",
      challenge:
        "AI-generated marketing content was being flagged by search engines, undermining years of organic traffic growth and brand trust.",
      quote:
        "We need AI to scale our content strategy, but we need to know when it's crossing the line between helpful assistant and replacement for human creativity.",
      person: "<PERSON>, Marketing Director",
    },
    publishing: {
      icon: (
        <Newspaper className="h-5 w-5 text-purple-600 dark:text-purple-400" />
      ),
      title: "Meet Jordan's Challenge",
      content:
        "<PERSON>, an editor at a digital publication, noticed a trend: freelance submissions were becoming suspiciously polished and uniform. After investigation, it became clear many writers were submitting mostly AI-generated work while charging for human-created content.",
      challenge:
        "Without reliable AI detection, the publication was paying premium rates for machine-generated content that readers quickly abandoned.",
      quote:
        "I'm all for tools that enhance creativity, but we need to distinguish between AI-assisted improvement and complete automation of creative work.",
      person: "Jordan, Senior Editor",
    },
    education: {
      icon: <BookOpen className="h-5 w-5 text-amber-600 dark:text-amber-400" />,
      title: "Meet Jessica's Challenge",
      content:
        "Jessica, a university professor, noticed something odd about student submissions. The writing was technically perfect—well-structured arguments—but something felt off. The personal voice she knew from classroom discussions was missing.",
      challenge:
        "AI-generated content was slipping through traditional plagiarism checks, undermining her ability to accurately assess student learning.",
      quote:
        "I want to embrace technology, not fight it. But I need to know when I'm reading a student's authentic work versus an AI's creation.",
      person: "Jessica, University Professor",
    },
    business: {
      icon: (
        <Briefcase className="h-5 w-5 text-green-600 dark:text-green-400" />
      ),
      title: "Meet Miguel's Challenge",
      content:
        "Miguel's legal firm received proposals from multiple consulting agencies that seemed oddly similar. After deeper analysis, he realized they contained identical phrases and reasoning patterns, despite coming from competing companies.",
      challenge:
        "Differentiating between authentic business communications and AI-generated proposals became impossible without specialized tools.",
      quote:
        "We need a reliable way to verify the authenticity of critical business documents. The stakes are simply too high in our industry.",
      person: "Miguel, Legal Director",
    },
  };

  return (
    <section className="w-full py-16 md:py-28 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 overflow-hidden relative">
      {/* Background decorative elements */}
      <div className="absolute right-0 top-1/3 w-64 h-64 bg-blue-100/20 dark:bg-blue-900/10 rounded-full blur-3xl -z-10" />
      <div className="absolute left-20 bottom-1/4 w-72 h-72 bg-purple-100/30 dark:bg-purple-900/10 rounded-full blur-3xl -z-10" />

      <div className="container mx-auto px-4 md:px-6 relative">
        {/* Enhanced section header with animated accent */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 mb-2 px-4 py-1.5 bg-blue-100/80 dark:bg-blue-900/30 rounded-full animate-fade-in">
            <AlertTriangle className="h-4 w-4 text-blue-500 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Universal Challenge
            </span>
          </div>
          <h2
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white max-w-3xl mx-auto animate-reveal"
            style={{ animationDelay: "0.2s" }}
          >
            The AI Content Authentication Challenge
          </h2>
          <div
            className="h-1 bg-gradient-to-r from-blue-500 to-purple-500 w-[80px] mx-auto mt-4 rounded-full animate-reveal"
            style={{ animationDelay: "0.6s" }}
          />
        </div>

        <div className="grid md:grid-cols-2 gap-10 lg:gap-16 items-center">
          {/* Left column: Multiple industry case studies with tab selection */}
          <div
            className="space-y-6 animate-fade-in"
            style={{ animationDelay: "0.3s" }}
          >
            {/* Industry selector tabs */}
            <div className="flex flex-wrap gap-2 mb-4">
              <button
                onClick={() => setActiveTab("marketing")}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === "marketing"
                    ? "bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200"
                    : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
              >
                Marketing
              </button>
              <button
                onClick={() => setActiveTab("publishing")}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === "publishing"
                    ? "bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200"
                    : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
              >
                Publishing
              </button>
              <button
                onClick={() => setActiveTab("education")}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === "education"
                    ? "bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-200"
                    : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
              >
                Education
              </button>
              <button
                onClick={() => setActiveTab("business")}
                className={`px-3 py-2 rounded-full text-sm font-medium transition-all ${
                  activeTab === "business"
                    ? "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200"
                    : "bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
              >
                Business
              </button>
            </div>

            <div className="p-6 backdrop-blur-sm bg-white/70 dark:bg-gray-800/70 rounded-xl shadow-lg border border-gray-100/60 dark:border-gray-700/40">
              <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white flex items-center gap-2">
                <span
                  className={`p-2 rounded-md ${
                    activeTab === "marketing"
                      ? "bg-blue-100 dark:bg-blue-900/50"
                      : activeTab === "publishing"
                      ? "bg-purple-100 dark:bg-purple-900/50"
                      : activeTab === "education"
                      ? "bg-amber-100 dark:bg-amber-900/50"
                      : "bg-green-100 dark:bg-green-900/50"
                  }`}
                >
                  {caseStudies[activeTab].icon}
                </span>
                {caseStudies[activeTab].title}
              </h3>

              <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                {caseStudies[activeTab].content}
              </p>

              <div
                className={`p-4 rounded-lg my-4 border-l-4 ${
                  activeTab === "marketing"
                    ? "bg-blue-50 dark:bg-blue-900/20 border-blue-500 dark:border-blue-400"
                    : activeTab === "publishing"
                    ? "bg-purple-50 dark:bg-purple-900/20 border-purple-500 dark:border-purple-400"
                    : activeTab === "education"
                    ? "bg-amber-50 dark:bg-amber-900/20 border-amber-500 dark:border-amber-400"
                    : "bg-green-50 dark:bg-green-900/20 border-green-500 dark:border-green-400"
                }`}
              >
                <p
                  className={`font-medium ${
                    activeTab === "marketing"
                      ? "text-blue-700 dark:text-blue-300"
                      : activeTab === "publishing"
                      ? "text-purple-700 dark:text-purple-300"
                      : activeTab === "education"
                      ? "text-amber-700 dark:text-amber-300"
                      : "text-green-700 dark:text-green-300"
                  }`}
                >
                  {caseStudies[activeTab].challenge}
                </p>
              </div>

              <blockquote
                className={`border-l-4 pl-4 italic my-6 text-gray-600 dark:text-gray-400 ${
                  activeTab === "marketing"
                    ? "border-blue-500 dark:border-blue-400"
                    : activeTab === "publishing"
                    ? "border-purple-500 dark:border-purple-400"
                    : activeTab === "education"
                    ? "border-amber-500 dark:border-amber-400"
                    : "border-green-500 dark:border-green-400"
                }`}
              >
                "{caseStudies[activeTab].quote}"
                <footer className="text-sm mt-2 font-medium">
                  — {caseStudies[activeTab].person}
                </footer>
              </blockquote>
            </div>

            <div
              className="backdrop-blur-sm bg-white/70 dark:bg-gray-800/70 p-6 rounded-xl shadow-lg border border-gray-100/60 dark:border-gray-700/40 animate-reveal"
              style={{ animationDelay: "0.5s" }}
            >
              <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white flex items-center gap-2">
                <span className="bg-indigo-100 dark:bg-indigo-900/50 p-2 rounded-md">
                  <TrendingUp className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </span>
                The Growing Concern
              </h3>
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                As AI content generation becomes increasingly sophisticated,
                organizations across all industries face a common challenge:
                distinguishing between human and AI-created content. From
                marketing and publishing to education and business, the need for
                reliable AI detection has never been more urgent.
              </p>
            </div>
          </div>

          {/* Right column: Cross-industry statistics with visual appeal */}
          <div
            className="relative animate-fade-in"
            style={{ animationDelay: "0.6s" }}
          >
            <div className="absolute -z-10 w-full h-full bg-gradient-to-br from-blue-100/40 to-purple-100/30 dark:from-blue-900/20 dark:to-purple-900/10 blur-xl rounded-xl transform translate-x-4 translate-y-4" />

            <div className="backdrop-blur-sm bg-white/80 dark:bg-gray-800/80 rounded-xl shadow-lg border border-gray-100/60 dark:border-gray-700/40 overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 text-center">
                  AI Content Adoption Across Industries
                </h3>

                <div className="relative h-[300px] w-full">
                  {/* Animated chart bars */}
                  <div className="absolute inset-0 flex items-end pb-4 px-4">
                    <div className="h-full flex flex-col justify-between items-start pr-4 py-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Marketing
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Publishing
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Education
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Business
                      </span>
                    </div>
                    <div className="flex-1 flex flex-col gap-6 justify-between">
                      <div className="h-12 w-full flex items-center">
                        <div
                          className="h-6 bg-gradient-to-r from-blue-400 to-blue-600 dark:from-blue-500 dark:to-blue-700 rounded-r-lg animate-chart-bar-horizontal-1"
                          style={{ width: "0%" }}
                        ></div>
                        <span
                          className="ml-2 text-xs text-gray-600 dark:text-gray-300 opacity-0 animate-fade-in"
                          style={{ animationDelay: "1.3s" }}
                        >
                          78%
                        </span>
                      </div>
                      <div className="h-12 w-full flex items-center">
                        <div
                          className="h-6 bg-gradient-to-r from-purple-400 to-purple-600 dark:from-purple-500 dark:to-purple-700 rounded-r-lg animate-chart-bar-horizontal-2"
                          style={{ width: "0%" }}
                        ></div>
                        <span
                          className="ml-2 text-xs text-gray-600 dark:text-gray-300 opacity-0 animate-fade-in"
                          style={{ animationDelay: "1.6s" }}
                        >
                          65%
                        </span>
                      </div>
                      <div className="h-12 w-full flex items-center">
                        <div
                          className="h-6 bg-gradient-to-r from-amber-400 to-amber-600 dark:from-amber-500 dark:to-amber-700 rounded-r-lg animate-chart-bar-horizontal-3"
                          style={{ width: "0%" }}
                        ></div>
                        <span
                          className="ml-2 text-xs text-gray-600 dark:text-gray-300 opacity-0 animate-fade-in"
                          style={{ animationDelay: "1.9s" }}
                        >
                          72%
                        </span>
                      </div>
                      <div className="h-12 w-full flex items-center">
                        <div
                          className="h-6 bg-gradient-to-r from-green-400 to-green-600 dark:from-green-500 dark:to-green-700 rounded-r-lg animate-chart-bar-horizontal-4"
                          style={{ width: "0%" }}
                        ></div>
                        <span
                          className="ml-2 text-xs text-gray-600 dark:text-gray-300 opacity-0 animate-fade-in"
                          style={{ animationDelay: "2.2s" }}
                        >
                          59%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
                  Cross-Industry AI Content Challenges
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-5 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/30 dark:to-blue-800/20 flex flex-col items-center justify-center transform transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in-delay-1">
                    <div className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 mb-1">
                      67%
                    </div>
                    <div className="text-center text-sm text-gray-600 dark:text-gray-300">
                      struggle to detect AI-generated content
                    </div>
                  </div>

                  <div className="p-5 rounded-lg bg-gradient-to-br from-purple-50 to-purple-100/50 dark:from-purple-900/30 dark:to-purple-800/20 flex flex-col items-center justify-center transform transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in-delay-2">
                    <div className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600 dark:from-purple-400 dark:to-indigo-400 mb-1">
                      82%
                    </div>
                    <div className="text-center text-sm text-gray-600 dark:text-gray-300">
                      reported AI content quality concerns
                    </div>
                  </div>

                  <div className="p-5 rounded-lg bg-gradient-to-br from-amber-50 to-amber-100/50 dark:from-amber-900/30 dark:to-amber-800/20 flex flex-col items-center justify-center transform transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in-delay-1">
                    <div className="flex items-baseline">
                      <span className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-amber-600 to-red-600 dark:from-amber-400 dark:to-red-400">
                        4.3
                      </span>
                      <span className="text-sm text-amber-600 dark:text-amber-400 ml-1">
                        hrs
                      </span>
                    </div>
                    <div className="text-center text-sm text-gray-600 dark:text-gray-300">
                      weekly spent reviewing AI content
                    </div>
                  </div>

                  <div className="p-5 rounded-lg bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/30 dark:to-green-800/20 flex flex-col items-center justify-center transform transition-all duration-300 hover:scale-105 hover:shadow-md animate-fade-in-delay-2">
                    <div className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-teal-600 dark:from-green-400 dark:to-teal-400 mb-1">
                      91%
                    </div>
                    <div className="text-center text-sm text-gray-600 dark:text-gray-300">
                      want reliable AI detection tools
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Updated animation styles */}
      <style jsx global>{`
        @keyframes chart-bar-horizontal-1 {
          0% {
            width: 0%;
          }
          100% {
            width: 78%;
          }
        }
        @keyframes chart-bar-horizontal-2 {
          0% {
            width: 0%;
          }
          100% {
            width: 65%;
          }
        }
        @keyframes chart-bar-horizontal-3 {
          0% {
            width: 0%;
          }
          100% {
            width: 72%;
          }
        }
        @keyframes chart-bar-horizontal-4 {
          0% {
            width: 0%;
          }
          100% {
            width: 59%;
          }
        }

        .animate-chart-bar-horizontal-1 {
          animation: chart-bar-horizontal-1 1.2s forwards 1s;
        }
        .animate-chart-bar-horizontal-2 {
          animation: chart-bar-horizontal-2 1.2s forwards 1.3s;
        }
        .animate-chart-bar-horizontal-3 {
          animation: chart-bar-horizontal-3 1.2s forwards 1.6s;
        }
        .animate-chart-bar-horizontal-4 {
          animation: chart-bar-horizontal-4 1.2s forwards 1.9s;
        }
      `}</style>
    </section>
  );
}
