"use client"

import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { useState } from "react"
import Link from "next/link"

interface Integration {
  name: string
  description: string
  votes: number
}

const initialIntegrations: Integration[] = [
  // Learning Management Systems
  { name: "Canvas", description: "Integrate with Canvas LMS for assignment analysis.", votes: 0 },
  { name: "Blackboard", description: "Seamless integration with Blackboard Learn.", votes: 0 },
  { name: "<PERSON><PERSON><PERSON>", description: "Connect with your Moodle courses for AI detection.", votes: 0 },

  // Writing and Productivity
  { name: "Microsoft Word", description: "Analyze documents directly within Microsoft Word.", votes: 0 },
  { name: "Google Docs", description: "Analyze documents directly in Google Docs.", votes: 0 },
  { name: "Dropbox", description: "Access and analyze files stored in Dropbox.", votes: 0 },
  { name: "<PERSON>lack", description: "Share AI detection results and collaborate with your team.", votes: 0 },

  // SEO and Content Marketing
  { name: "Semrush", description: "Improve SEO by ensuring content authenticity with Semrush.", votes: 0 },
  { name: "Ahrefs", description: "Analyze content performance and originality with Ahrefs.", votes: 0 },
  {
    name: "Google Search Console",
    description: "Monitor your site's search performance and identify AI content.",
    votes: 0,
  },
  {
    name: "Google Analytics",
    description: "Track user engagement and content effectiveness with Google Analytics.",
    votes: 0,
  },
  { name: "HubSpot", description: "Verify content authenticity in your HubSpot marketing workflows.", votes: 0 },

  // Academic Tools
  { name: "Zotero", description: "Manage citations and analyze research papers with Zotero.", votes: 0 },
  { name: "Mendeley", description: "Organize research and analyze documents with Mendeley.", votes: 0 },
  { name: "Overleaf", description: "Check LaTeX documents for AI-generated content with Overleaf.", votes: 0 },

  // Code Editors
  { name: "VS Code", description: "Analyze code comments and documentation within VS Code.", votes: 0 },
  { name: "Sublime Text", description: "Check code comments and documentation within Sublime Text.", votes: 0 },

  // Other
  { name: "Gmail", description: "Analyze email content for AI-generated text.", votes: 0 },
  { name: "Evernote", description: "Analyze notes and documents stored in Evernote.", votes: 0 },
  { name: "Notion", description: "Analyze text content within your Notion workspaces.", votes: 0 },
]

export default function IntegrationsClientPage() {
  const [integrations, setIntegrations] = useState(initialIntegrations)

  const handleVote = (name: string) => {
    setIntegrations((prevIntegrations) =>
      prevIntegrations.map((integration) =>
        integration.name === name ? { ...integration, votes: integration.votes + 1 } : integration,
      ),
    )
  }

  return (
    <main className="flex min-h-screen flex-col">
      <Navbar />

      <div className="flex-1 py-12 md:py-24">
        <div className="container px-4 md:px-6">
          <div className="text-center space-y-4 mb-12">
            <h1 className="text-4xl font-bold tracking-tight">Integrations</h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Connect StudentAIDetector with your favorite tools and platforms. Vote for the integrations you want to
              see next!
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
            {integrations.map((integration) => (
              <Card key={integration.name} className="overflow-hidden">
                <CardHeader>
                  <CardTitle>{integration.name}</CardTitle>
                  <CardDescription>{integration.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col justify-between">
                  <div className="flex items-center justify-between">
                    <Button onClick={() => handleVote(integration.name)}>Vote</Button>
                    <span className="text-sm text-muted-foreground">{integration.votes} votes</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Roadmap Section */}
          <div className="mt-24">
            <h2 className="text-3xl font-bold text-center mb-8">Upcoming Integrations & Features</h2>
            <div className="space-y-4">
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-lg mb-2">Grammarly Integration</h3>
                <p className="text-muted-foreground">
                  We're working on a seamless integration with Grammarly to combine their writing assistance with our AI
                  detection capabilities.
                </p>
              </div>
              <div className="border rounded-lg p-4">
                <h3 className="font-medium text-lg mb-2">API Enhancements</h3>
                <p className="text-muted-foreground">
                  We're improving our API to offer more flexibility and control over AI detection and humanization
                  processes.
                </p>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="mt-24 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of students, educators, and content creators who trust StudentAIDetector.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/signup">Sign Up Now</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/contact">Contact Sales</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
