import { type NextRequest, NextResponse } from "next/server"
import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"

// This would be a real API endpoint in a production application
// For now, we'll simulate the AI detection with a mock implementation

export async function POST(req: NextRequest) {
  try {
    const { text, sensitivity = 0.5 } = await req.json()

    if (!text || text.trim().length === 0) {
      return NextResponse.json({ error: "Text is required" }, { status: 400 })
    }

    // In a real implementation, we would use a sophisticated AI model
    // For this demo, we'll use a simple approach with the AI SDK

    // First, analyze the text with a prompt to determine AI probability
    const { text: analysisResult } = await generateText({
      model: openai("gpt-4o"),
      system: `You are an AI detection expert. Analyze the following text and determine the probability (0.0 to 1.0) that it was written by an AI. 
      Consider patterns like repetitive phrasing, unnatural transitions, overly consistent tone, and lack of personal voice.
      Respond ONLY with a JSON object in this format: 
      { 
        "score": 0.X, 
        "suspiciousSections": [
          {"text": "excerpt of suspicious text", "score": 0.X, "startIndex": N, "endIndex": N}
        ]
      }`,
      prompt: `Text to analyze: "${text}"`,
    })

    // Parse the result (in a real app, we'd have more robust parsing)
    let result
    try {
      result = JSON.parse(analysisResult)
    } catch (e) {
      // Fallback to a mock result if parsing fails
      result = {
        score: Math.random() * 0.5 + 0.3,
        suspiciousSections: [
          {
            text: text.substring(0, Math.min(100, text.length)),
            score: Math.random() * 0.5 + 0.5,
            startIndex: 0,
            endIndex: Math.min(100, text.length),
          },
        ],
      }
    }

    // Adjust based on sensitivity setting
    const adjustedResult = {
      score: Math.min(1, result.score * (1 + (sensitivity - 0.5))),
      highlightedText: text,
      suspiciousSections: result.suspiciousSections.map((section: any) => ({
        ...section,
        score: Math.min(1, section.score * (1 + (sensitivity - 0.5))),
      })),
    }

    return NextResponse.json(adjustedResult)
  } catch (error) {
    console.error("Error analyzing text:", error)
    return NextResponse.json({ error: "Failed to analyze text" }, { status: 500 })
  }
}
