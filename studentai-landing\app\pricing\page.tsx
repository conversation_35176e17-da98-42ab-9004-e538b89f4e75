import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import type { Metadata } from "next";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { PricingFaq } from "@/components/pricing/pricing-faq";
import { PricingComparison } from "@/components/pricing/pricing-comparison";
import { EnterprisePricing } from "@/components/pricing/enterprise-pricing";
import { ApiPricing } from "@/components/pricing/api-pricing";
import { AddOns } from "@/components/pricing/add-ons";
import { Breadcrumb } from "@/components/breadcrumb";
import { generateMetaTags } from "@/lib/seo-utils";
// Remove CreditPricingTable import and add PricingSection import
import PricingSection from "@/components/pricing-section";
import { InstitutionalPricing } from "@/components/pricing/institutional-pricing";

export const metadata: Metadata = generateMetaTags({
  title: "Pricing Plans - AI Detection & Humanization Tool Subscriptions",
  description:
    "Choose the right StudentAIDetector plan for your needs. From free basic detection to comprehensive AI analysis tools with our Pro plan. Affordable options for educators, students, and content creators.",
  keywords: [
    "AI detector pricing",
    "AI humanizer cost",
    "AI detection subscription",
    "AI content detector plans",
    "academic integrity tools pricing",
    "AI text checker subscription",
    "AI detection free trial",
    "AI humanizer subscription",
    "AI detection tool cost",
    "AI content analysis pricing",
  ],
  canonical: "/pricing",
});

export default function PricingPage() {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      {/* <div className="container py-8">
        <Breadcrumb />
      </div> */}

      {/* Hero Section with PricingSection */}
      <section className="bg-muted/50 pt-24">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-10">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
              Simple, Transparent Pricing
            </h1>
            <p className="text-muted-foreground text-lg max-w-3xl mx-auto mt-4">
              Choose the plan that works best for you or your institution. All
              plans include our core AI detection features.
            </p>
          </div>
        </div>

        {/* Replace CreditPricingTable with PricingSection */}
        <PricingSection />
      </section>

      {/* Feature Comparison */}
      <section className="py-12 md:py-24">
        <div className="container px-4 md:px-6">
          <PricingComparison />
        </div>
      </section>

      {/* Enterprise Pricing */}
      {/* <section className="bg-muted/50 py-12 md:py-24">
        <div className="container px-4 md:px-6">
          <EnterprisePricing />
        </div>
      </section> */}

      {/* API Pricing */}
      {/* <section className="py-12 md:py-24">
        <div className="container px-4 md:px-6">
          <ApiPricing />
        </div>
      </section> */}

      {/* Institutional Pricing */}
      {/* <section className="bg-muted/50 py-12 md:py-24">
        <div className="container px-4 md:px-6">
          <InstitutionalPricing />
        </div>
      </section> */}

      {/* Add-Ons */}
      {/* <section className="bg-muted/50 py-12 md:py-24">
        <div className="container px-4 md:px-6">
          <AddOns />
        </div>
      </section> */}

      {/* FAQ Section */}
      {/* <section className="py-12 md:py-24">
        <div className="container px-4 md:px-6">
          <PricingFaq />
        </div>
      </section> */}

      {/* CTA Section */}
      <section className="bg-primary text-primary-foreground py-12 md:py-24">
        <div className="container px-4 md:px-6 text-center">
          <h2 className="text-3xl font-bold tracking-tight mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl opacity-90 mb-6 max-w-2xl mx-auto">
            Join thousands of students, educators, and content creators who
            trust StudentAIDetector.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" asChild>
              <Link href="https://app.studentaidetector.com/signup">
                Sign Up Now
              </Link>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10"
              asChild
            >
              <Link href="/contact">Contact Sales</Link>
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  );
}
