import Link from "next/link";
import type { <PERSON>ada<PERSON> } from "next";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowRight,
  CheckCircle,
  Users,
  BookOpen,
  LucideNewspaper,
  GraduationCap,
} from "lucide-react";

export const metadata: Metadata = {
  title: "AI Detection Solutions for Every Need | StudentAIDetector",
  description:
    "Discover specialized AI detection solutions for educators, students, bloggers, university admissions, and more. Find the perfect AI detection approach for your specific needs.",
  keywords:
    "AI detection solutions, AI detector for educators, AI detector for students, AI detector for bloggers, AI content detection use cases",
};

export default function UseCaseHubPage() {
  return (
    <>
      <Navbar />
      <div className="container mx-auto px-4 py-12 pt-28">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              AI Detection Solutions for{" "}
              <span className="text-primary">Every Need</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Specialized tools and approaches for different audiences and use
              cases
            </p>
          </div>

          <div className="prose dark:prose-invert max-w-none mb-12">
            <p className="text-lg mb-6">
              AI-generated content presents unique challenges for different
              audiences. Whether you're an educator concerned about academic
              integrity, a student ensuring your work meets guidelines, or a
              content creator maintaining authenticity, StudentAIDetector offers
              specialized solutions for your specific needs.
            </p>

            <p className="mb-8">
              Explore our tailored approaches to AI detection across different
              use cases. Each solution addresses the unique challenges and
              requirements of different user groups.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-16">
            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-l-4 border-blue-500 h-full flex flex-col">
                <div className="bg-blue-50 dark:bg-blue-950/20 p-3 flex items-center gap-2">
                  <div className="bg-blue-100 dark:bg-blue-900/30 p-1.5 rounded-md">
                    <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    AI Detection for Educators
                  </h2>
                </div>
                <CardContent className="flex-1 flex flex-col pt-6">
                  <p className="text-muted-foreground mb-6">
                    Maintain academic integrity in your classroom with tools
                    designed specifically for teachers and professors. Detect
                    AI-written assignments and guide students toward responsible
                    AI use.
                  </p>
                  <div className="mt-2 mb-6">
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Batch process entire classroom assignments</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>LMS integration for streamlined workflows</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>
                          Evidence-based reports for student discussions
                        </span>
                      </li>
                    </ul>
                  </div>
                  <div className="mt-auto">
                    <Button
                      variant="ghost"
                      className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/20 p-0 h-auto"
                      asChild
                    >
                      <Link
                        href="/for-educators"
                        className="flex items-center gap-1"
                      >
                        Learn more about our educator solutions
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>

            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-l-4 border-purple-500 h-full flex flex-col">
                <div className="bg-purple-50 dark:bg-purple-950/20 p-3 flex items-center gap-2">
                  <div className="bg-purple-100 dark:bg-purple-900/30 p-1.5 rounded-md">
                    <BookOpen className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    AI Detection for Students
                  </h2>
                </div>
                <CardContent className="flex-1 flex flex-col pt-6">
                  <p className="text-muted-foreground mb-6">
                    Ensure your assignments meet your institution's AI
                    guidelines. Check your work before submission and learn how
                    to properly incorporate AI assistance while maintaining
                    academic integrity.
                  </p>
                  <div className="mt-2 mb-6">
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Pre-submission AI content checking</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Learn proper AI citation guidelines</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Guidance on responsible AI use in education</span>
                      </li>
                    </ul>
                  </div>
                  <div className="mt-auto">
                    <Button
                      variant="ghost"
                      className="text-purple-600 hover:text-purple-800 hover:bg-purple-50 dark:hover:bg-purple-900/20 p-0 h-auto"
                      asChild
                    >
                      <Link
                        href="/for-students"
                        className="flex items-center gap-1"
                      >
                        Learn more about our student solutions
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>

            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-l-4 border-amber-500 h-full flex flex-col">
                <div className="bg-amber-50 dark:bg-amber-950/20 p-3 flex items-center gap-2">
                  <div className="bg-amber-100 dark:bg-amber-900/30 p-1.5 rounded-md">
                    <LucideNewspaper className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    AI Detection for Bloggers
                  </h2>
                </div>
                <CardContent className="flex-1 flex flex-col pt-6">
                  <p className="text-muted-foreground mb-6">
                    Protect your blog from AI content penalties and maintain
                    your authentic voice. Ensure your content meets search
                    engine guidelines while still leveraging AI tools
                    responsibly.
                  </p>
                  <div className="mt-2 mb-6">
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Detect AI content before publishing</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Maintain SEO-friendly content standards</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Preserve your authentic writing voice</span>
                      </li>
                    </ul>
                  </div>
                  <div className="mt-auto">
                    <Button
                      variant="ghost"
                      className="text-amber-600 hover:text-amber-800 hover:bg-amber-50 dark:hover:bg-amber-900/20 p-0 h-auto"
                      asChild
                    >
                      <Link
                        href="/for-writers"
                        className="flex items-center gap-1"
                      >
                        Learn more about our blogger solutions
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>

            <Card className="border overflow-hidden hover:shadow-md transition-all">
              <div className="border-l-4 border-green-500 h-full flex flex-col">
                <div className="bg-green-50 dark:bg-green-950/20 p-3 flex items-center gap-2">
                  <div className="bg-green-100 dark:bg-green-900/30 p-1.5 rounded-md">
                    <GraduationCap className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <h2 className="text-xl font-semibold">
                    AI Detection for University Admissions
                  </h2>
                </div>
                <CardContent className="flex-1 flex flex-col pt-6">
                  <p className="text-muted-foreground mb-6">
                    Ensure authenticity in application essays and personal
                    statements. Maintain fairness in the admissions process with
                    reliable AI detection tools designed for admissions
                    officers.
                  </p>
                  <div className="mt-2 mb-6">
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Verify authenticity of personal statements</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Batch process large volumes of applications</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                        <span>Create fair evaluation standards</span>
                      </li>
                    </ul>
                  </div>
                  <div className="mt-auto">
                    <Button
                      variant="ghost"
                      className="text-green-600 hover:text-green-800 hover:bg-green-50 dark:hover:bg-green-900/20 p-0 h-auto"
                      asChild
                    >
                      <Link
                        href="/for-educators#admissions"
                        className="flex items-center gap-1"
                      >
                        Learn more about our admissions solutions
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </div>
            </Card>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl p-8 mb-16 border border-blue-100 dark:border-blue-900/50">
            <h2 className="text-2xl font-semibold mb-6">
              Why Choose Specialized AI Detection Solutions?
            </h2>
            <div className="prose dark:prose-invert max-w-none">
              <p>
                Different contexts require different approaches to AI detection.
                Our specialized solutions offer:
              </p>
              <div className="grid md:grid-cols-2 gap-4 mt-4">
                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Contextual Analysis
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Detection calibrated for specific types of content with
                    different baseline expectations and thresholds
                  </p>
                </div>
                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Relevant Features
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Tools and workflows designed for your specific needs,
                    focusing on what matters most for your use case
                  </p>
                </div>
                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Tailored Guidance
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Best practices and implementation advice specifically
                    created for your context and requirements
                  </p>
                </div>
                <div className="bg-white dark:bg-gray-800/50 p-4 rounded-lg">
                  <h3 className="text-lg font-medium flex items-center gap-2 mb-2">
                    <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                    Appropriate Metrics
                  </h3>
                  <p className="text-muted-foreground text-sm">
                    Results and reporting that matter for your use case, with
                    insights that drive meaningful decisions
                  </p>
                </div>
              </div>
              <p className="mt-6">
                By choosing a solution designed specifically for your needs,
                you'll get more relevant results and a more effective
                implementation of AI detection in your workflow.
              </p>
            </div>
          </div>

          <div className="mb-16">
            <h2 className="text-2xl font-bold mb-6">
              More AI Detection Use Cases
            </h2>
            <div className="grid md:grid-cols-2 gap-4 md:gap-6">
              <Link
                href="/use-cases/academic-research-ai-detection"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">
                  AI Detection for Academic Research
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link
                href="/use-cases/marketing-agencies-ai-detection"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">
                  AI Detection for Marketing Agencies
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link
                href="/use-cases/publishers-ai-detection"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">AI Detection for Publishers</span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link
                href="/use-cases/online-course-creators-ai-detection"
                className="p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 flex items-center justify-between group transition-colors"
              >
                <span className="font-medium">
                  AI Detection for Online Course Creators
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-8 border">
            <h2 className="text-2xl font-semibold mb-4">
              Looking for Tool Comparisons?
            </h2>
            <p className="text-muted-foreground mb-6">
              If you're evaluating different AI detection tools, check out our
              comprehensive comparison guides:
            </p>
            <div className="grid md:grid-cols-2 gap-4">
              <Link
                href="/compare/studentaidetector-vs-gptzero"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  StudentAIDetector vs GPTZero
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link
                href="/compare/studentaidetector-vs-turnitin"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  StudentAIDetector vs Turnitin
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link
                href="/compare/studentaidetector-vs-originality"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  StudentAIDetector vs Originality.AI
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
              <Link
                href="/compare/studentaidetector-vs-quillbot"
                className="flex items-center justify-between bg-white dark:bg-gray-800 p-4 rounded-lg border hover:border-blue-200 dark:hover:border-blue-900 group transition-colors"
              >
                <span className="font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  StudentAIDetector vs QuillBot
                </span>
                <ArrowRight className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
              </Link>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}
