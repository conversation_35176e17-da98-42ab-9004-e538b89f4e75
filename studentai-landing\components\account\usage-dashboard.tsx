"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import type { UsageStats } from "@/lib/usage-tracking"
import type { SubscriptionDetails } from "@/lib/subscription-manager"
import { AlertCircle, CheckCircle, PlusCircle } from "lucide-react"
import Link from "next/link"

interface UsageDashboardProps {
  initialUsage: UsageStats
  subscription: SubscriptionDetails
}

export function UsageDashboard({ initialUsage, subscription }: UsageDashboardProps) {
  const [usage, setUsage] = useState<UsageStats>(initialUsage)

  // In a real app, you might fetch updated usage data periodically
  useEffect(() => {
    // Example of how you might fetch updated usage data
    const fetchUsage = async () => {
      // const response = await fetch('/api/usage');
      // const data = await response.json();
      // setUsage(data);
    }

    // fetchUsage();
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const getProgressColor = (used: number, limit: number) => {
    const percentage = (used / limit) * 100
    if (percentage > 90) return "bg-red-500"
    if (percentage > 75) return "bg-amber-500"
    return ""
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Usage Overview</CardTitle>
          <CardDescription>
            Current billing period: {formatDate(subscription.currentPeriodStart)} to{" "}
            {formatDate(subscription.currentPeriodEnd)}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">AI Detection Queries</h3>
                <p className="text-sm text-muted-foreground">
                  {usage.detectionQueries.used} of {usage.detectionQueries.limit} used
                </p>
              </div>
              {usage.detectionQueries.used >= usage.detectionQueries.limit * 0.8 && (
                <Link href="/account/add-credits">
                  <Button size="sm" variant="outline" className="flex items-center gap-1">
                    <PlusCircle className="h-4 w-4" />
                    Add Credits
                  </Button>
                </Link>
              )}
            </div>
            <Progress
              value={(usage.detectionQueries.used / usage.detectionQueries.limit) * 100}
              className="h-2"
              indicatorClassName={getProgressColor(usage.detectionQueries.used, usage.detectionQueries.limit)}
            />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">Humanization Tools</h3>
                <p className="text-sm text-muted-foreground">
                  {usage.humanizationTools.used} of {usage.humanizationTools.limit} used
                </p>
              </div>
              {usage.humanizationTools.used >= usage.humanizationTools.limit * 0.8 && (
                <Link href="/account/add-credits">
                  <Button size="sm" variant="outline" className="flex items-center gap-1">
                    <PlusCircle className="h-4 w-4" />
                    Add Credits
                  </Button>
                </Link>
              )}
            </div>
            <Progress
              value={(usage.humanizationTools.used / usage.humanizationTools.limit) * 100}
              className="h-2"
              indicatorClassName={getProgressColor(usage.humanizationTools.used, usage.humanizationTools.limit)}
            />
          </div>

          {usage.apiCalls.limit > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">API Calls</h3>
                  <p className="text-sm text-muted-foreground">
                    {usage.apiCalls.used} of {usage.apiCalls.limit} used
                  </p>
                </div>
                {usage.apiCalls.used >= usage.apiCalls.limit * 0.8 && (
                  <Link href="/account/add-credits">
                    <Button size="sm" variant="outline" className="flex items-center gap-1">
                      <PlusCircle className="h-4 w-4" />
                      Add API Credits
                    </Button>
                  </Link>
                )}
              </div>
              <Progress
                value={(usage.apiCalls.used / usage.apiCalls.limit) * 100}
                className="h-2"
                indicatorClassName={getProgressColor(usage.apiCalls.used, usage.apiCalls.limit)}
              />
            </div>
          )}

          {usage.batchProcessing.limit > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">Batch Processing</h3>
                  <p className="text-sm text-muted-foreground">
                    {usage.batchProcessing.used} of {usage.batchProcessing.limit} used
                  </p>
                </div>
              </div>
              <Progress
                value={(usage.batchProcessing.used / usage.batchProcessing.limit) * 100}
                className="h-2"
                indicatorClassName={getProgressColor(usage.batchProcessing.used, usage.batchProcessing.limit)}
              />
            </div>
          )}

          <div className="pt-4 flex flex-col sm:flex-row gap-4">
            <div className="flex-1 p-4 border rounded-lg">
              <div className="flex items-start gap-2">
                <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">
                    Current Plan: {subscription.plan.charAt(0).toUpperCase() + subscription.plan.slice(1)}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    Your plan renews on {formatDate(subscription.currentPeriodEnd)}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex-1 p-4 border rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <h4 className="font-medium">Usage Resets Monthly</h4>
                  <p className="text-sm text-muted-foreground">
                    All usage counters reset at the beginning of your billing cycle
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
