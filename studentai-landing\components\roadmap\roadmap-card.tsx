"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp, Thum<PERSON>Down, ThumbsUp } from "lucide-react"

type RoadmapStatus = "live" | "planned" | "backlog"

interface RoadmapCardProps {
  title: string
  description: string
  status: RoadmapStatus
  category: string
  votes: number
  rationale: string
}

export function RoadmapCard({ title, description, status, category, votes, rationale }: RoadmapCardProps) {
  const [showRationale, setShowRationale] = useState(false)
  const [voteCount, setVoteCount] = useState(votes)
  const [userVote, setUserVote] = useState<"up" | "down" | null>(null)

  const handleVote = (vote: "up" | "down") => {
    if (userVote === vote) {
      // Remove vote
      setUserVote(null)
      setVoteCount(vote === "up" ? voteCount - 1 : voteCount + 1)
    } else if (userVote === null) {
      // Add new vote
      setUserVote(vote)
      setVoteCount(vote === "up" ? voteCount + 1 : voteCount - 1)
    } else {
      // Change vote (from up to down or vice versa)
      setUserVote(vote)
      setVoteCount(vote === "up" ? voteCount + 2 : voteCount - 2)
    }
  }

  const getStatusColor = (status: RoadmapStatus) => {
    switch (status) {
      case "live":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "planned":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "backlog":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Core Detection":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
      case "User Interface":
      case "User Experience":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300"
      case "Content Processing":
        return "bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300"
      case "Content Enhancement":
        return "bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300"
      case "Integration":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "Education":
        return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300"
      case "Business":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
    }
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div className="space-y-1">
            <CardTitle className="text-lg font-semibold">{title}</CardTitle>
            <div className="flex flex-wrap gap-2">
              <Badge variant="outline" className={getStatusColor(status)}>
                {status === "live" ? "Live" : status === "planned" ? "Planned" : "Backlog"}
              </Badge>
              <Badge variant="outline" className={getCategoryColor(category)}>
                {category}
              </Badge>
            </div>
          </div>
          <div className="flex flex-col items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleVote("up")}
              className={userVote === "up" ? "text-green-600" : ""}
            >
              <ThumbsUp className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium">{voteCount}</span>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => handleVote("down")}
              className={userVote === "down" ? "text-red-600" : ""}
            >
              <ThumbsDown className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <CardDescription className="text-sm text-muted-foreground">{description}</CardDescription>
      </CardContent>
      <CardFooter className="flex flex-col items-start pt-0">
        <Button
          variant="link"
          size="sm"
          className="p-0 h-auto text-xs flex items-center gap-1"
          onClick={() => setShowRationale(!showRationale)}
        >
          {showRationale ? "Hide rationale" : "Show rationale"}
          {showRationale ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
        </Button>
        {showRationale && (
          <div className="mt-2 text-xs text-muted-foreground">
            <p>
              <strong>Why this status?</strong> {rationale}
            </p>
          </div>
        )}
      </CardFooter>
    </Card>
  )
}
