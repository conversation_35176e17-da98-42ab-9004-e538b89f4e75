"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { PageHeader } from "@/components/page-header"
import { RoadmapCard } from "@/components/roadmap/roadmap-card"
import { RoadmapStats } from "@/components/roadmap/roadmap-stats"
import { RoadmapFilter } from "@/components/roadmap/roadmap-filter"
import Link from "next/link"
import { useRouter } from "next/navigation"

export default function RoadmapPage() {
  const router = useRouter()

  return (
    <div className="container py-10 space-y-8">
      <PageHeader
        title="Product Roadmap"
        description="Explore our development plans and help shape the future of StudentAiDetector by voting on features that matter most to you."
      />

      <RoadmapStats />

      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full sm:w-auto grid-cols-4 mb-8">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="live">Live</TabsTrigger>
              <TabsTrigger value="planned">Planned</TabsTrigger>
              <TabsTrigger value="backlog">Backlog</TabsTrigger>
            </TabsList>

            <RoadmapFilter />

            <TabsContent value="all" className="space-y-10 mt-6">
              <section>
                <h2 className="text-2xl font-bold mb-6">Live Features</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <RoadmapCard
                    title="Basic AI Text Detection"
                    description="Single-method text analysis for identifying AI-generated content with confidence scoring and sensitivity adjustment controls."
                    status="live"
                    category="Core Detection"
                    votes={124}
                    rationale="Forms the core functionality but lacks the sophisticated multi-modal approach outlined in the white papers."
                  />
                  <RoadmapCard
                    title="Analysis Results Visualization"
                    description="Summary view with overall AI probability score, highlighted text view showing suspicious sections, and detailed section analysis."
                    status="live"
                    category="User Interface"
                    votes={98}
                    rationale="Provides good visualization of results but lacks the nuanced confidence calibration recommended in our technical documentation."
                  />
                  <RoadmapCard
                    title="File Upload & Processing"
                    description="Support for text input and file upload with basic file format support (.txt, .pdf, .docx) based on plan tier."
                    status="live"
                    category="Content Processing"
                    votes={87}
                    rationale="Meets basic input requirements but lacks cross-format analysis capabilities for comprehensive detection."
                  />
                  <RoadmapCard
                    title="Humanization Tools"
                    description="Basic tools to help revise AI-detected content with suggestions for making content more human-like."
                    status="live"
                    category="Content Enhancement"
                    votes={76}
                    rationale="Implements aspects of the dual functionality approach but lacks comprehensive educational resources for authentic writing development."
                  />
                  <RoadmapCard
                    title="Subscription Management"
                    description="Tiered plans with different feature sets and usage limits with usage tracking and enforcement."
                    status="live"
                    category="Business"
                    votes={52}
                    rationale="Provides necessary business functionality but could be enhanced with educational institution-specific plans."
                  />
                  <RoadmapCard
                    title="Educational Content"
                    description="Blog articles on AI detection and academic integrity with basic guidance on AI detection methods."
                    status="live"
                    category="Education"
                    votes={68}
                    rationale="Provides foundational educational content but lacks integration with the detection system itself."
                  />
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-bold mb-6">Planned Features</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <RoadmapCard
                    title="Multi-Modal Detection Engine"
                    description="Implementation of ensemble learning approach combining multiple detection algorithms with linguistic, semantic, stylistic, and structural analysis."
                    status="planned"
                    category="Core Detection"
                    votes={215}
                    rationale="Critical core technology upgrade aligned with white paper recommendations; prioritized due to direct impact on detection accuracy and false positive reduction."
                  />
                  <RoadmapCard
                    title="Appeals & Review System"
                    description="Formal process for contesting detection results with multi-stage review workflow and documentation tracking."
                    status="planned"
                    category="User Experience"
                    votes={142}
                    rationale="Essential procedural safeguard recommended in the white papers; prioritized to address ethical concerns around false positives."
                  />
                  <RoadmapCard
                    title="Educational Institution Integration"
                    description="LMS plugins for Canvas, Blackboard, and Moodle with customizable workflows for academic settings."
                    status="planned"
                    category="Integration"
                    votes={187}
                    rationale="Enables broader adoption in educational settings as recommended; prioritized based on target market needs."
                  />
                  <RoadmapCard
                    title="Enhanced Educational Resources"
                    description="Interactive writing guides, AI vs. human writing examples, and personalized improvement suggestions."
                    status="planned"
                    category="Education"
                    votes={134}
                    rationale="Implements the dual functionality approach recommended; prioritized to shift focus from punitive to educational."
                  />
                  <RoadmapCard
                    title="Continuous Improvement System"
                    description="Feedback collection from detection results, regular model retraining pipeline, and performance monitoring dashboard."
                    status="planned"
                    category="Core Detection"
                    votes={156}
                    rationale="Enables the adaptive capabilities recommended; prioritized to maintain detection effectiveness against evolving AI."
                  />
                </div>
              </section>

              <section>
                <h2 className="text-2xl font-bold mb-6">Backlog Features</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <RoadmapCard
                    title="Cross-Format Analysis"
                    description="Code analysis for programming assignments, visual element analysis for multimedia content, and document structure analysis."
                    status="backlog"
                    category="Content Processing"
                    votes={98}
                    rationale="Extends detection capabilities as recommended but requires significant development resources; placed in backlog due to complexity and dependency on multi-modal detection engine."
                  />
                  <RoadmapCard
                    title="Advanced Integration API"
                    description="Comprehensive REST API with webhook support and custom integration options."
                    status="backlog"
                    category="Integration"
                    votes={87}
                    rationale="Enhances integration capabilities as recommended but has lower immediate impact than LMS integrations; placed in backlog to prioritize educational institution-specific integrations first."
                  />
                  <RoadmapCard
                    title="Batch Processing System"
                    description="High-volume processing capabilities with bulk upload and analysis and scheduled processing."
                    status="backlog"
                    category="Content Processing"
                    votes={76}
                    rationale="Useful for large educational institutions but less critical than core detection improvements; placed in backlog due to limited immediate demand."
                  />
                  <RoadmapCard
                    title="Adversarial Testing Framework"
                    description="Systematic testing against edge cases, evaluation against latest AI models, and performance benchmarking."
                    status="backlog"
                    category="Core Detection"
                    votes={65}
                    rationale="Important for long-term quality but requires prior implementation of multi-modal detection; placed in backlog due to dependencies."
                  />
                  <RoadmapCard
                    title="Institutional Policy Framework"
                    description="Customizable policy templates, implementation guidelines, and best practices documentation."
                    status="backlog"
                    category="Education"
                    votes={54}
                    rationale="Valuable for educational institutions but secondary to technical improvements; placed in backlog to focus on core functionality first."
                  />
                  <RoadmapCard
                    title="Comprehensive Analytics"
                    description="Detection pattern analysis, usage trends and insights, and comparative benchmarking."
                    status="backlog"
                    category="Business"
                    votes={43}
                    rationale="Provides valuable insights but not critical for core functionality; placed in backlog due to lower immediate impact."
                  />
                </div>
              </section>
            </TabsContent>

            <TabsContent value="live" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <RoadmapCard
                  title="Basic AI Text Detection"
                  description="Single-method text analysis for identifying AI-generated content with confidence scoring and sensitivity adjustment controls."
                  status="live"
                  category="Core Detection"
                  votes={124}
                  rationale="Forms the core functionality but lacks the sophisticated multi-modal approach outlined in the white papers."
                />
                <RoadmapCard
                  title="Analysis Results Visualization"
                  description="Summary view with overall AI probability score, highlighted text view showing suspicious sections, and detailed section analysis."
                  status="live"
                  category="User Interface"
                  votes={98}
                  rationale="Provides good visualization of results but lacks the nuanced confidence calibration recommended in our technical documentation."
                />
                <RoadmapCard
                  title="File Upload & Processing"
                  description="Support for text input and file upload with basic file format support (.txt, .pdf, .docx) based on plan tier."
                  status="live"
                  category="Content Processing"
                  votes={87}
                  rationale="Meets basic input requirements but lacks cross-format analysis capabilities for comprehensive detection."
                />
                <RoadmapCard
                  title="Humanization Tools"
                  description="Basic tools to help revise AI-detected content with suggestions for making content more human-like."
                  status="live"
                  category="Content Enhancement"
                  votes={76}
                  rationale="Implements aspects of the dual functionality approach but lacks comprehensive educational resources for authentic writing development."
                />
                <RoadmapCard
                  title="Subscription Management"
                  description="Tiered plans with different feature sets and usage limits with usage tracking and enforcement."
                  status="live"
                  category="Business"
                  votes={52}
                  rationale="Provides necessary business functionality but could be enhanced with educational institution-specific plans."
                />
                <RoadmapCard
                  title="Educational Content"
                  description="Blog articles on AI detection and academic integrity with basic guidance on AI detection methods."
                  status="live"
                  category="Education"
                  votes={68}
                  rationale="Provides foundational educational content but lacks integration with the detection system itself."
                />
              </div>
            </TabsContent>

            <TabsContent value="planned" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <RoadmapCard
                  title="Multi-Modal Detection Engine"
                  description="Implementation of ensemble learning approach combining multiple detection algorithms with linguistic, semantic, stylistic, and structural analysis."
                  status="planned"
                  category="Core Detection"
                  votes={215}
                  rationale="Critical core technology upgrade aligned with white paper recommendations; prioritized due to direct impact on detection accuracy and false positive reduction."
                />
                <RoadmapCard
                  title="Appeals & Review System"
                  description="Formal process for contesting detection results with multi-stage review workflow and documentation tracking."
                  status="planned"
                  category="User Experience"
                  votes={142}
                  rationale="Essential procedural safeguard recommended in the white papers; prioritized to address ethical concerns around false positives."
                />
                <RoadmapCard
                  title="Educational Institution Integration"
                  description="LMS plugins for Canvas, Blackboard, and Moodle with customizable workflows for academic settings."
                  status="planned"
                  category="Integration"
                  votes={187}
                  rationale="Enables broader adoption in educational settings as recommended; prioritized based on target market needs."
                />
                <RoadmapCard
                  title="Enhanced Educational Resources"
                  description="Interactive writing guides, AI vs. human writing examples, and personalized improvement suggestions."
                  status="planned"
                  category="Education"
                  votes={134}
                  rationale="Implements the dual functionality approach recommended; prioritized to shift focus from punitive to educational."
                />
                <RoadmapCard
                  title="Continuous Improvement System"
                  description="Feedback collection from detection results, regular model retraining pipeline, and performance monitoring dashboard."
                  status="planned"
                  category="Core Detection"
                  votes={156}
                  rationale="Enables the adaptive capabilities recommended; prioritized to maintain detection effectiveness against evolving AI."
                />
              </div>
            </TabsContent>

            <TabsContent value="backlog" className="space-y-6 mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <RoadmapCard
                  title="Cross-Format Analysis"
                  description="Code analysis for programming assignments, visual element analysis for multimedia content, and document structure analysis."
                  status="backlog"
                  category="Content Processing"
                  votes={98}
                  rationale="Extends detection capabilities as recommended but requires significant development resources; placed in backlog due to complexity and dependency on multi-modal detection engine."
                />
                <RoadmapCard
                  title="Advanced Integration API"
                  description="Comprehensive REST API with webhook support and custom integration options."
                  status="backlog"
                  category="Integration"
                  votes={87}
                  rationale="Enhances integration capabilities as recommended but has lower immediate impact than LMS integrations; placed in backlog to prioritize educational institution-specific integrations first."
                />
                <RoadmapCard
                  title="Batch Processing System"
                  description="High-volume processing capabilities with bulk upload and analysis and scheduled processing."
                  status="backlog"
                  category="Content Processing"
                  votes={76}
                  rationale="Useful for large educational institutions but less critical than core detection improvements; placed in backlog due to limited immediate demand."
                />
                <RoadmapCard
                  title="Adversarial Testing Framework"
                  description="Systematic testing against edge cases, evaluation against latest AI models, and performance benchmarking."
                  status="backlog"
                  category="Core Detection"
                  votes={65}
                  rationale="Important for long-term quality but requires prior implementation of multi-modal detection; placed in backlog due to dependencies."
                />
                <RoadmapCard
                  title="Institutional Policy Framework"
                  description="Customizable policy templates, implementation guidelines, and best practices documentation."
                  status="backlog"
                  category="Education"
                  votes={54}
                  rationale="Valuable for educational institutions but secondary to technical improvements; placed in backlog to focus on core functionality first."
                />
                <RoadmapCard
                  title="Comprehensive Analytics"
                  description="Detection pattern analysis, usage trends and insights, and comparative benchmarking."
                  status="backlog"
                  category="Business"
                  votes={43}
                  rationale="Provides valuable insights but not critical for core functionality; placed in backlog due to lower immediate impact."
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <div className="bg-muted rounded-lg p-6 mt-12">
        <h2 className="text-xl font-bold mb-2">Have a feature request?</h2>
        <p className="mb-4">
          We're always looking to improve StudentAiDetector. If you have a feature idea that's not on our roadmap, we'd
          love to hear about it.
        </p>
        <Link
          href="/contact"
          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
        >
          Submit Feature Request
        </Link>
      </div>
    </div>
  )
}
