"use client";

import { useState, useRef, useEffect } from "react";
import {
  Check,
  X,
  Info,
  ChevronDown,
  Sparkles,
  Award,
  Star,
  Globe,
  BarChart3,
  Pen,
} from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { motion, AnimatePresence } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

export function FeatureComparison() {
  const [currentPlan, setCurrentPlan] = useState("standard");
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [selectedCompetitor, setSelectedCompetitor] = useState("gptzero");
  const [isCompetitorMenuOpen, setIsCompetitorMenuOpen] = useState(false);
  const comparisonRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Feature categories for better organization
  const featureCategories = [
    {
      id: "detection",
      name: "Detection Capabilities",
      description: "Core AI detection features and accuracy metrics",
    },
    {
      id: "productivity",
      name: "Productivity Tools",
      description: "Features that enhance workflow and save time",
    },
    {
      id: "advanced",
      name: "Advanced Features",
      description: "Specialized tools and premium capabilities",
    },
    {
      id: "technical",
      name: "Technical Aspects",
      description: "API, integration, and technical specifications",
    },
  ];

  // Define StudentAIDetector features
  const studentaiFeatures = {
    free: {
      "Basic AI Detection": {
        value: true,
        category: "detection",
        info: "Core AI detection capabilities",
      },
      "Detection Accuracy": {
        value: "Good",
        category: "detection",
        info: "Higher accuracy means better detection with fewer false positives",
      },
      "False Positive Rate": {
        value: "Low",
        category: "detection",
        highlight: true,
        info: "How often human text is incorrectly flagged as AI",
      },
      "Detailed Analytics": {
        value: "Basic",
        category: "detection",
        info: "In-depth insights about detected AI patterns",
      },
      "AI Humanizer": {
        value: false,
        category: "advanced",
        highlight: true,
        newFeature: true,
        info: "Modify AI-generated text to bypass other detection tools",
      },
      "Multiple Language Support": {
        value: true,
        category: "detection",
        info: "Support for languages beyond English",
      },
      "Batch Processing": {
        value: false,
        category: "productivity",
        info: "Process multiple documents simultaneously",
      },
      "API Access": {
        value: false,
        category: "technical",
        info: "Integration with other applications via API",
      },
      "Team Collaboration": {
        value: false,
        category: "productivity",
        info: "Share access with team members",
      },
      "Chrome Extension": {
        value: true,
        category: "productivity",
        info: "Browser extension for quick checking",
      },
      "Plagiarism Check": {
        value: false,
        category: "advanced",
        info: "Check for copied content from the web",
      },
      "Word Limits": {
        value: "5,000",
        category: "technical",
        formatSpecial: true,
        info: "Monthly word processing allowance",
      },
      "Real-time Analysis": {
        value: true,
        category: "productivity",
        info: "Instant results as you type or paste text",
      },
      "Custom Detection Thresholds": {
        value: false,
        category: "advanced",
        highlight: true,
        info: "Adjust sensitivity of AI detection to your needs",
      },
      "Mobile Compatibility": {
        value: true,
        category: "technical",
        info: "Fully responsive design for mobile use",
      },
      "Content Safety Checks": {
        value: false,
        category: "advanced",
        info: "Detection of harmful, inappropriate, or unsafe content",
      },
      "Grammar Checker": {
        value: false,
        category: "productivity",
        info: "Analyze text for grammatical errors",
      },
      "Paraphrasing Tool": {
        value: false,
        category: "productivity",
        info: "Rewrite content to improve or change style",
      },
      "Text Humanization": {
        value: false,
        category: "advanced",
        info: "Make AI-written content appear more human-like",
      },
      "Summarization Tools": {
        value: false,
        category: "productivity",
        info: "Automatically summarize longer content",
      },
    },
    standard: {
      "Basic AI Detection": {
        value: true,
        category: "detection",
        info: "Core AI detection capabilities",
      },
      "Detection Accuracy": {
        value: "Advanced",
        category: "detection",
        info: "Higher accuracy means better detection with fewer false positives",
      },
      "False Positive Rate": {
        value: "Very Low",
        category: "detection",
        highlight: true,
        info: "How often human text is incorrectly flagged as AI",
      },
      "Detailed Analytics": {
        value: "Advanced",
        category: "detection",
        info: "In-depth insights about detected AI patterns",
      },
      "AI Humanizer": {
        value: "Optional",
        category: "advanced",
        highlight: true,
        newFeature: true,
        info: "Modify AI-generated text to bypass other detection tools",
      },
      "Multiple Language Support": {
        value: true,
        category: "detection",
        info: "Support for languages beyond English",
      },
      "Batch Processing": {
        value: "Limited",
        category: "productivity",
        info: "Process multiple documents simultaneously",
      },
      "API Access": {
        value: "Basic",
        category: "technical",
        info: "Integration with other applications via API",
      },
      "Team Collaboration": {
        value: false,
        category: "productivity",
        info: "Share access with team members",
      },
      "Chrome Extension": {
        value: true,
        category: "productivity",
        info: "Browser extension for quick checking",
      },
      "Plagiarism Check": {
        value: false,
        category: "advanced",
        info: "Check for copied content from the web",
      },
      "Word Limits": {
        value: "50,000",
        category: "technical",
        formatSpecial: true,
        info: "Monthly word processing allowance",
      },
      "Real-time Analysis": {
        value: true,
        category: "productivity",
        info: "Instant results as you type or paste text",
      },
      "Custom Detection Thresholds": {
        value: true,
        category: "advanced",
        highlight: true,
        info: "Adjust sensitivity of AI detection to your needs",
      },
      "Mobile Compatibility": {
        value: true,
        category: "technical",
        info: "Fully responsive design for mobile use",
      },
      "Content Safety Checks": {
        value: false,
        category: "advanced",
        info: "Detection of harmful, inappropriate, or unsafe content",
      },
      "Grammar Checker": {
        value: false,
        category: "productivity",
        info: "Analyze text for grammatical errors",
      },
      "Paraphrasing Tool": {
        value: false,
        category: "productivity",
        info: "Rewrite content to improve or change style",
      },
      "Text Humanization": {
        value: "Optional",
        category: "advanced",
        info: "Make AI-written content appear more human-like",
      },
      "Summarization Tools": {
        value: false,
        category: "productivity",
        info: "Automatically summarize longer content",
      },
    },
    pro: {
      "Basic AI Detection": {
        value: true,
        category: "detection",
        info: "Core AI detection capabilities",
      },
      "Detection Accuracy": {
        value: "Highest",
        category: "detection",
        info: "Higher accuracy means better detection with fewer false positives",
      },
      "False Positive Rate": {
        value: "Lowest",
        category: "detection",
        highlight: true,
        info: "How often human text is incorrectly flagged as AI",
      },
      "Detailed Analytics": {
        value: "Comprehensive",
        category: "detection",
        info: "In-depth insights about detected AI patterns",
      },
      "AI Humanizer": {
        value: "Optional",
        category: "advanced",
        highlight: true,
        newFeature: true,
        info: "Modify AI-generated text to bypass other detection tools",
      },
      "Multiple Language Support": {
        value: true,
        category: "detection",
        info: "Support for languages beyond English",
      },
      "Batch Processing": {
        value: true,
        category: "productivity",
        info: "Process multiple documents simultaneously",
      },
      "API Access": {
        value: "Full",
        category: "technical",
        info: "Integration with other applications via API",
      },
      "Team Collaboration": {
        value: "Up to 5",
        category: "productivity",
        info: "Share access with team members",
      },
      "Chrome Extension": {
        value: true,
        category: "productivity",
        info: "Browser extension for quick checking",
      },
      "Plagiarism Check": {
        value: "Coming soon",
        category: "advanced",
        info: "Check for copied content from the web",
      },
      "Word Limits": {
        value: "200,000",
        category: "technical",
        formatSpecial: true,
        info: "Monthly word processing allowance",
      },
      "Real-time Analysis": {
        value: true,
        category: "productivity",
        info: "Instant results as you type or paste text",
      },
      "Custom Detection Thresholds": {
        value: true,
        category: "advanced",
        highlight: true,
        info: "Adjust sensitivity of AI detection to your needs",
      },
      "Mobile Compatibility": {
        value: true,
        category: "technical",
        info: "Fully responsive design for mobile use",
      },
      "Content Safety Checks": {
        value: false,
        category: "advanced",
        info: "Detection of harmful, inappropriate, or unsafe content",
      },
      "Grammar Checker": {
        value: false,
        category: "productivity",
        info: "Analyze text for grammatical errors",
      },
      "Paraphrasing Tool": {
        value: false,
        category: "productivity",
        info: "Rewrite content to improve or change style",
      },
      "Text Humanization": {
        value: "Optional",
        category: "advanced",
        info: "Make AI-written content appear more human-like",
      },
      "Summarization Tools": {
        value: false,
        category: "productivity",
        info: "Automatically summarize longer content",
      },
    },
  };

  // Available competitors with feature data
  const competitors = {
    gptzero: {
      name: "GPTZero",
      logo: "G",
      color: "gray",
      planMapping: {
        free: "free",
        standard: "essential",
        pro: "premium",
      },
      features: {
        free: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Basic", category: "detection" },
          "False Positive Rate": { value: "Moderate", category: "detection" },
          "Detailed Analytics": { value: "Limited", category: "detection" },
          "AI Humanizer": { value: false, category: "advanced" },
          "Multiple Language Support": { value: false, category: "detection" },
          "Batch Processing": { value: false, category: "productivity" },
          "API Access": { value: false, category: "technical" },
          "Team Collaboration": { value: false, category: "productivity" },
          "Chrome Extension": { value: false, category: "productivity" },
          "Plagiarism Check": { value: false, category: "advanced" },
          "Word Limits": {
            value: "10,000",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: "Limited", category: "productivity" },
          "Custom Detection Thresholds": { value: false, category: "advanced" },
          "Mobile Compatibility": { value: "Limited", category: "technical" },
          "Content Safety Checks": { value: false, category: "advanced" },
          "Grammar Checker": { value: false, category: "productivity" },
          "Paraphrasing Tool": { value: false, category: "productivity" },
          "Text Humanization": { value: false, category: "advanced" },
          "Summarization Tools": { value: false, category: "productivity" },
        },
        essential: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Basic", category: "detection" },
          "False Positive Rate": { value: "Moderate", category: "detection" },
          "Detailed Analytics": { value: "Basic", category: "detection" },
          "AI Humanizer": { value: false, category: "advanced" },
          "Multiple Language Support": { value: true, category: "detection" },
          "Batch Processing": { value: false, category: "productivity" },
          "API Access": { value: false, category: "technical" },
          "Team Collaboration": { value: false, category: "productivity" },
          "Chrome Extension": { value: true, category: "productivity" },
          "Plagiarism Check": { value: false, category: "advanced" },
          "Word Limits": {
            value: "150,000",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: false, category: "advanced" },
          "Mobile Compatibility": { value: true, category: "technical" },
          "Content Safety Checks": { value: false, category: "advanced" },
          "Grammar Checker": { value: false, category: "productivity" },
          "Paraphrasing Tool": { value: false, category: "productivity" },
          "Text Humanization": { value: false, category: "advanced" },
          "Summarization Tools": { value: false, category: "productivity" },
        },
        premium: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Advanced", category: "detection" },
          "False Positive Rate": { value: "Low", category: "detection" },
          "Detailed Analytics": { value: "Advanced", category: "detection" },
          "AI Humanizer": { value: false, category: "advanced" },
          "Multiple Language Support": { value: true, category: "detection" },
          "Batch Processing": { value: false, category: "productivity" },
          "API Access": { value: "Limited", category: "technical" },
          "Team Collaboration": { value: false, category: "productivity" },
          "Chrome Extension": { value: true, category: "productivity" },
          "Plagiarism Check": { value: true, category: "advanced" },
          "Word Limits": {
            value: "300,000",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: false, category: "advanced" },
          "Mobile Compatibility": { value: true, category: "technical" },
          "Content Safety Checks": { value: false, category: "advanced" },
          "Grammar Checker": { value: false, category: "productivity" },
          "Paraphrasing Tool": { value: false, category: "productivity" },
          "Text Humanization": { value: false, category: "advanced" },
          "Summarization Tools": { value: false, category: "productivity" },
        },
        professional: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Advanced", category: "detection" },
          "False Positive Rate": { value: "Low", category: "detection" },
          "Detailed Analytics": { value: "Advanced", category: "detection" },
          "AI Humanizer": { value: false, category: "advanced" },
          "Multiple Language Support": { value: true, category: "detection" },
          "Batch Processing": { value: true, category: "productivity" },
          "API Access": { value: true, category: "technical" },
          "Team Collaboration": { value: true, category: "productivity" },
          "Chrome Extension": { value: true, category: "productivity" },
          "Plagiarism Check": { value: true, category: "advanced" },
          "Word Limits": {
            value: "500,000",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: true, category: "advanced" },
          "Mobile Compatibility": { value: true, category: "technical" },
          "Content Safety Checks": { value: true, category: "advanced" },
          "Grammar Checker": { value: false, category: "productivity" },
          "Paraphrasing Tool": { value: false, category: "productivity" },
          "Text Humanization": { value: false, category: "advanced" },
          "Summarization Tools": { value: false, category: "productivity" },
        },
      },
    },
    originality: {
      name: "Originality.ai",
      logo: "O",
      color: "orange",
      planMapping: {
        free: "paygo",
        standard: "pro",
        pro: "enterprise",
      },
      features: {
        paygo: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Advanced", category: "detection" },
          "False Positive Rate": { value: "Moderate", category: "detection" },
          "Detailed Analytics": { value: "Basic", category: "detection" },
          "AI Humanizer": { value: false, category: "advanced" },
          "Multiple Language Support": { value: true, category: "detection" },
          "Batch Processing": { value: false, category: "productivity" },
          "API Access": { value: false, category: "technical" },
          "Team Collaboration": { value: false, category: "productivity" },
          "Chrome Extension": { value: false, category: "productivity" },
          "Plagiarism Check": { value: true, category: "advanced" },
          "Word Limits": {
            value: "300,000 (one-time)",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: false, category: "advanced" },
          "Mobile Compatibility": { value: "Limited", category: "technical" },
          "Content Safety Checks": { value: false, category: "advanced" },
          "Grammar Checker": { value: "Basic", category: "productivity" },
          "Paraphrasing Tool": { value: false, category: "productivity" },
          "Text Humanization": { value: false, category: "advanced" },
          "Summarization Tools": { value: false, category: "productivity" },
        },
        pro: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Advanced", category: "detection" },
          "False Positive Rate": { value: "Low", category: "detection" },
          "Detailed Analytics": { value: "Advanced", category: "detection" },
          "AI Humanizer": { value: false, category: "advanced" },
          "Multiple Language Support": { value: true, category: "detection" },
          "Batch Processing": { value: true, category: "productivity" },
          "API Access": { value: false, category: "technical" },
          "Team Collaboration": { value: "Limited", category: "productivity" },
          "Chrome Extension": { value: true, category: "productivity" },
          "Plagiarism Check": { value: true, category: "advanced" },
          "Word Limits": {
            value: "200,000",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: true, category: "advanced" },
          "Mobile Compatibility": { value: true, category: "technical" },
          "Content Safety Checks": { value: "Basic", category: "advanced" },
          "Grammar Checker": { value: "Basic", category: "productivity" },
          "Paraphrasing Tool": { value: false, category: "productivity" },
          "Text Humanization": { value: false, category: "advanced" },
          "Summarization Tools": { value: false, category: "productivity" },
        },
        enterprise: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Advanced", category: "detection" },
          "False Positive Rate": { value: "Low", category: "detection" },
          "Detailed Analytics": {
            value: "Comprehensive",
            category: "detection",
          },
          "AI Humanizer": { value: false, category: "advanced" },
          "Multiple Language Support": { value: true, category: "detection" },
          "Batch Processing": { value: true, category: "productivity" },
          "API Access": { value: true, category: "technical" },
          "Team Collaboration": {
            value: "Unlimited",
            category: "productivity",
          },
          "Chrome Extension": { value: true, category: "productivity" },
          "Plagiarism Check": { value: true, category: "advanced" },
          "Word Limits": {
            value: "1,500,000",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: true, category: "advanced" },
          "Mobile Compatibility": { value: true, category: "technical" },
          "Content Safety Checks": { value: "Advanced", category: "advanced" },
          "Grammar Checker": { value: "Advanced", category: "productivity" },
          "Paraphrasing Tool": { value: false, category: "productivity" },
          "Text Humanization": { value: false, category: "advanced" },
          "Summarization Tools": { value: false, category: "productivity" },
        },
      },
    },
    quillbot: {
      name: "Quillbot",
      logo: "Q",
      color: "green",
      planMapping: {
        free: "free",
        standard: "premium",
        pro: "premium",
      },
      features: {
        free: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Basic", category: "detection" },
          "False Positive Rate": { value: "Moderate", category: "detection" },
          "Detailed Analytics": { value: "Limited", category: "detection" },
          "AI Humanizer": { value: true, category: "advanced" },
          "Multiple Language Support": {
            value: "Limited",
            category: "detection",
          },
          "Batch Processing": { value: false, category: "productivity" },
          "API Access": { value: false, category: "technical" },
          "Team Collaboration": { value: false, category: "productivity" },
          "Chrome Extension": { value: true, category: "productivity" },
          "Plagiarism Check": { value: false, category: "advanced" },
          "Word Limits": {
            value: "1,200 (AI Detection)",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: false, category: "advanced" },
          "Mobile Compatibility": { value: "Limited", category: "technical" },
          "Content Safety Checks": { value: false, category: "advanced" },
          "Grammar Checker": { value: "Basic", category: "productivity" },
          "Paraphrasing Tool": {
            value: "Limited (125 words)",
            category: "productivity",
          },
          "Text Humanization": { value: "Basic", category: "advanced" },
          "Summarization Tools": { value: "Basic", category: "productivity" },
        },
        premium: {
          "Basic AI Detection": { value: true, category: "detection" },
          "Detection Accuracy": { value: "Advanced", category: "detection" },
          "False Positive Rate": { value: "Low", category: "detection" },
          "Detailed Analytics": { value: "Advanced", category: "detection" },
          "AI Humanizer": { value: true, category: "advanced" },
          "Multiple Language Support": { value: true, category: "detection" },
          "Batch Processing": { value: true, category: "productivity" },
          "API Access": { value: false, category: "technical" },
          "Team Collaboration": { value: false, category: "productivity" },
          "Chrome Extension": { value: true, category: "productivity" },
          "Plagiarism Check": { value: true, category: "advanced" },
          "Word Limits": {
            value: "Unlimited (AI Detection)",
            category: "technical",
            formatSpecial: true,
          },
          "Real-time Analysis": { value: true, category: "productivity" },
          "Custom Detection Thresholds": { value: false, category: "advanced" },
          "Mobile Compatibility": { value: true, category: "technical" },
          "Content Safety Checks": { value: false, category: "advanced" },
          "Grammar Checker": { value: "Advanced", category: "productivity" },
          "Paraphrasing Tool": { value: "Unlimited", category: "productivity" },
          "Text Humanization": { value: "Advanced", category: "advanced" },
          "Summarization Tools": {
            value: "Advanced",
            category: "productivity",
          },
        },
      },
    },
  };

  // Detect when component is visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const section = comparisonRef.current;
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  // Handle clicks outside the competitor menu to close it
  useEffect(() => {
    function handleClickOutside(event) {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsCompetitorMenuOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuRef]);

  // Plan selection options
  const planOptions = [
    { id: "free", label: "Free" },
    { id: "standard", label: "Standard" },
    { id: "pro", label: "Pro" },
  ];

  // Available competitors for dropdown
  const availableCompetitors = [
    { id: "gptzero", name: "GPTZero", color: "gray", logo: "G" },
    { id: "originality", name: "Originality.ai", color: "orange", logo: "O" },
    { id: "quillbot", name: "Quillbot", color: "green", logo: "Q" },
  ];

  // Get current competitor
  const currentCompetitor = competitors[selectedCompetitor];

  // Get mapped plan name for current competitor
  const competitorPlanName = currentCompetitor.planMapping[currentPlan];

  // Toggle feature category expansion
  const toggleCategory = (categoryId) => {
    setExpandedCategory(expandedCategory === categoryId ? null : categoryId);
  };

  // Get all feature names by merging both StudentAI and competitor features
  const getAllFeatureNames = () => {
    const featureNames = new Set();

    // Add StudentAI features
    Object.keys(studentaiFeatures[currentPlan]).forEach((name) => {
      featureNames.add(name);
    });

    // Add competitor features
    Object.keys(currentCompetitor.features[competitorPlanName]).forEach(
      (name) => {
        featureNames.add(name);
      }
    );

    return Array.from(featureNames);
  };

  // Get features by category
  const getFeaturesByCategory = (categoryId) => {
    const allFeatureNames = getAllFeatureNames();

    return allFeatureNames.filter((featureName) => {
      const studentaiFeature = studentaiFeatures[currentPlan][featureName];
      const competitorFeature =
        currentCompetitor.features[competitorPlanName][featureName];

      // Check if either feature exists and matches the category
      return (
        (studentaiFeature && studentaiFeature.category === categoryId) ||
        (competitorFeature && competitorFeature.category === categoryId)
      );
    });
  };

  // Calculate advantage score
  const calculateAdvantageScore = () => {
    let studentaiAdvantages = 0;
    let competitorAdvantages = 0;

    const allFeatureNames = getAllFeatureNames();

    allFeatureNames.forEach((featureName) => {
      const studentaiFeature = studentaiFeatures[currentPlan][featureName];
      const competitorFeature =
        currentCompetitor.features[competitorPlanName][featureName];

      if (!studentaiFeature || !competitorFeature) return;

      const studentaiValue = studentaiFeature.value;
      const competitorValue = competitorFeature.value;

      // Compare boolean features
      if (
        typeof studentaiValue === "boolean" &&
        typeof competitorValue === "boolean"
      ) {
        if (studentaiValue && !competitorValue) studentaiAdvantages++;
        if (!studentaiValue && competitorValue) competitorAdvantages++;
      }
      // Compare string-based features
      else if (
        typeof studentaiValue === "string" &&
        typeof competitorValue === "string"
      ) {
        // Check for advanced/highest/very low/lowest vs basic/limited/moderate
        if (
          (studentaiValue.includes("Advanced") ||
            studentaiValue.includes("Highest") ||
            studentaiValue.includes("Very Low") ||
            studentaiValue.includes("Lowest") ||
            studentaiValue.includes("Full") ||
            studentaiValue.includes("Comprehensive")) &&
          (competitorValue.includes("Basic") ||
            competitorValue.includes("Limited") ||
            competitorValue.includes("Moderate"))
        ) {
          studentaiAdvantages++;
        }

        // Check for the reverse
        if (
          (competitorValue.includes("Advanced") ||
            competitorValue.includes("Highest") ||
            competitorValue.includes("Very Low") ||
            competitorValue.includes("Lowest") ||
            competitorValue.includes("Full") ||
            competitorValue.includes("Comprehensive") ||
            competitorValue.includes("Unlimited")) &&
          (studentaiValue.includes("Basic") ||
            studentaiValue.includes("Limited") ||
            studentaiValue.includes("Moderate"))
        ) {
          competitorAdvantages++;
        }
      }

      // Special case for "Optional" features that competitor lacks
      if (studentaiValue === "Optional" && competitorValue === false) {
        studentaiAdvantages++;
      }

      // Special case for features StudentAI doesn't have (like paraphrasing)
      if (studentaiValue === false && competitorValue !== false) {
        competitorAdvantages++;
      }
    });

    return {
      studentai: studentaiAdvantages,
      competitor: competitorAdvantages,
    };
  };

  const advantageScore = calculateAdvantageScore();

  // Get the competitor's badge/styling colors
  const getCompetitorColor = (competitor) => {
    switch (competitor) {
      case "quillbot":
        return {
          bg: "bg-green-100 dark:bg-green-900/30",
          text: "text-green-600 dark:text-green-400",
          border: "border-green-100 dark:border-green-900/30",
          darkBg: "dark:bg-green-900/30",
          check: "text-green-600 dark:text-green-400",
        };
      case "originality":
        return {
          bg: "bg-orange-100 dark:bg-orange-900/30",
          text: "text-orange-600 dark:text-orange-400",
          border: "border-orange-100 dark:border-orange-900/30",
          darkBg: "dark:bg-orange-900/30",
          check: "text-orange-600 dark:text-orange-400",
        };
      default:
        return {
          bg: "bg-gray-100 dark:bg-gray-700",
          text: "text-gray-600 dark:text-gray-400",
          border: "border-gray-200 dark:border-gray-700",
          darkBg: "dark:bg-gray-700",
          check: "text-gray-600 dark:text-gray-400",
        };
    }
  };

  const competitorColors = getCompetitorColor(selectedCompetitor);

  return (
    <motion.div
      ref={comparisonRef}
      initial={{ opacity: 0 }}
      animate={isVisible ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 0.6 }}
      className="w-full"
    >
      <div className="relative mb-6 overflow-hidden rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 border border-blue-100 dark:border-blue-800/40 shadow-sm">
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400 to-indigo-500"></div>
        <div className="absolute -right-10 -top-10 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl"></div>

        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h3 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white flex items-center">
              <Award className="text-blue-500 dark:text-blue-400 mr-2 h-5 w-5" />
              Feature Comparison
            </h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
              See how StudentAIDetector compares to {currentCompetitor.name}{" "}
              across key features
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-end sm:items-center gap-3 sm:gap-4">
            <div className="flex items-center">
              <div className="text-sm text-gray-600 dark:text-gray-300 mr-2">
                Compare with:
              </div>
              <div ref={menuRef} className="relative">
                <button
                  onClick={() => setIsCompetitorMenuOpen(!isCompetitorMenuOpen)}
                  className={`flex items-center justify-between min-w-[160px] rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-3 py-1.5 hover:bg-gray-50 dark:hover:bg-gray-750 text-sm ${competitorColors.text}`}
                >
                  <div className="flex items-center">
                    <div
                      className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${competitorColors.bg}`}
                    >
                      <span
                        className={`text-xs font-medium ${competitorColors.text}`}
                      >
                        {currentCompetitor.logo}
                      </span>
                    </div>
                    <span className="font-medium">
                      {currentCompetitor.name}
                    </span>
                  </div>
                  <ChevronDown
                    className={`ml-2 w-4 h-4 transform transition-transform duration-200 ${
                      isCompetitorMenuOpen ? "rotate-180" : ""
                    }`}
                  />
                </button>

                <AnimatePresence>
                  {isCompetitorMenuOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute right-0 z-50 mt-2 w-full origin-top rounded-lg bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700"
                    >
                      <div className="py-1">
                        {availableCompetitors.map((comp) => {
                          const compColor = getCompetitorColor(comp.id);
                          return (
                            <button
                              key={comp.id}
                              className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 dark:hover:bg-gray-750 flex items-center ${
                                selectedCompetitor === comp.id
                                  ? "bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                                  : "text-gray-700 dark:text-gray-300"
                              }`}
                              onClick={() => {
                                setSelectedCompetitor(comp.id);
                                setIsCompetitorMenuOpen(false);
                              }}
                            >
                              <div
                                className={`w-5 h-5 rounded-full flex items-center justify-center mr-2 ${compColor.bg}`}
                              >
                                <span
                                  className={`text-xs font-medium ${compColor.text}`}
                                >
                                  {comp.logo}
                                </span>
                              </div>
                              {comp.name}
                              {selectedCompetitor === comp.id && (
                                <Check className="ml-auto w-4 h-4 text-blue-500" />
                              )}
                            </button>
                          );
                        })}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm border border-gray-200 dark:border-gray-700 flex">
              {planOptions.map((option) => (
                <button
                  key={option.id}
                  onClick={() => setCurrentPlan(option.id)}
                  className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                    currentPlan === option.id
                      ? "bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300"
                      : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-6 flex items-center justify-center space-x-8 md:space-x-16">
          <div className="flex flex-col items-center">
            <div className="bg-white dark:bg-gray-800 w-14 h-14 rounded-full shadow-md border border-blue-100 dark:border-blue-900/50 flex items-center justify-center mb-2">
              <Image
                src="/icon.png"
                alt="StudentAIDetector"
                width={32}
                height={32}
              />
            </div>
            <div className="text-center">
              <div className="font-medium text-blue-600 dark:text-blue-400">
                StudentAIDetector
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)}{" "}
                Plan
              </div>
            </div>
          </div>

          <div className="text-center text-gray-400 dark:text-gray-500 text-sm font-medium">
            VS
          </div>

          <AnimatePresence mode="wait">
            <motion.div
              key={selectedCompetitor}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="flex flex-col items-center"
            >
              <div
                className={`w-14 h-14 rounded-full shadow-md border flex items-center justify-center mb-2 bg-white dark:bg-gray-800 ${
                  selectedCompetitor === "quillbot"
                    ? "border-green-100 dark:border-green-900/30"
                    : selectedCompetitor === "originality"
                    ? "border-orange-100 dark:border-orange-900/30"
                    : "border-gray-200 dark:border-gray-700"
                }`}
              >
                {selectedCompetitor === "quillbot" ? (
                  <Pen className="h-6 w-6 text-green-600 dark:text-green-400" />
                ) : (
                  <span
                    className={`text-xl font-bold ${competitorColors.text}`}
                  >
                    {currentCompetitor.logo}
                  </span>
                )}
              </div>
              <div className="text-center">
                <div className={`font-medium ${competitorColors.text}`}>
                  {currentCompetitor.name}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {competitorPlanName === "premium" &&
                  selectedCompetitor === "quillbot"
                    ? "Premium Plan"
                    : competitorPlanName.charAt(0).toUpperCase() +
                      competitorPlanName
                        .slice(1)
                        .replace(/([A-Z])/g, " $1")
                        .trim() +
                      " Plan"}
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Feature Categories */}
      <AnimatePresence mode="wait">
        <motion.div
          key={`categories-${selectedCompetitor}-${currentPlan}`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          {featureCategories.map((category) => (
            <motion.div
              key={`${selectedCompetitor}-${category.id}`}
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{
                duration: 0.5,
                delay: featureCategories.indexOf(category) * 0.1,
              }}
              className="border border-gray-200 dark:border-gray-800 rounded-xl overflow-hidden bg-white dark:bg-gray-950 shadow-sm"
            >
              <div
                className={`px-6 py-4 flex justify-between items-center cursor-pointer ${
                  expandedCategory === category.id
                    ? "bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/10 dark:to-indigo-900/10"
                    : "bg-white dark:bg-gray-900"
                }`}
                onClick={() => toggleCategory(category.id)}
              >
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {category.name}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {category.description}
                  </p>
                </div>
                <div
                  className={`transition-transform duration-300 ${
                    expandedCategory === category.id ? "rotate-180" : ""
                  }`}
                >
                  <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                </div>
              </div>

              <AnimatePresence>
                {expandedCategory === category.id && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3, ease: "easeInOut" }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 py-4">
                      <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-800">
                        <table className="w-full">
                          <thead>
                            <tr className="bg-gray-50 dark:bg-gray-900">
                              <th className="py-3 px-4 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Feature
                              </th>
                              <th className="py-3 px-4 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/4">
                                StudentAIDetector
                              </th>
                              <th className="py-3 px-4 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider w-1/4">
                                {currentCompetitor.name}
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-800">
                            {getFeaturesByCategory(category.id).map(
                              (featureName, index) => {
                                const studentaiFeature = studentaiFeatures[
                                  currentPlan
                                ][featureName] || { value: "N/A" };
                                const competitorFeature = currentCompetitor
                                  .features[competitorPlanName][
                                  featureName
                                ] || { value: "N/A" };

                                // Highlight rows where either has an advantage
                                const highlightStudentAI =
                                  studentaiFeature.highlight ||
                                  (studentaiFeature.value !== false &&
                                    competitorFeature.value === false);

                                const highlightCompetitor =
                                  (studentaiFeature.value === false &&
                                    competitorFeature.value !== false) ||
                                  (selectedCompetitor === "quillbot" &&
                                    (featureName === "Paraphrasing Tool" ||
                                      featureName === "Text Humanization" ||
                                      featureName === "Summarization Tools"));

                                return (
                                  <tr
                                    key={featureName}
                                    className={`${
                                      highlightStudentAI
                                        ? "bg-blue-50 dark:bg-blue-900/10"
                                        : highlightCompetitor
                                        ? competitorColors.bg.replace(
                                            "bg-",
                                            "bg-opacity-15 bg-"
                                          )
                                        : index % 2 === 0
                                        ? "bg-white dark:bg-gray-950"
                                        : "bg-gray-50 dark:bg-gray-900"
                                    } hover:bg-gray-50 dark:hover:bg-gray-900/60 transition-colors`}
                                  >
                                    <td className="py-4 px-4">
                                      <div className="flex items-center flex-wrap gap-1.5">
                                        <span className="font-medium text-gray-900 dark:text-white">
                                          {featureName}
                                        </span>
                                        {highlightStudentAI && (
                                          <Badge className="bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-800">
                                            StudentAI Advantage
                                          </Badge>
                                        )}
                                        {selectedCompetitor === "quillbot" &&
                                          (featureName ===
                                            "Paraphrasing Tool" ||
                                            featureName ===
                                              "Text Humanization" ||
                                            featureName ===
                                              "Summarization Tools") && (
                                            <Badge
                                              className={`${competitorColors.bg} ${competitorColors.text} border-${currentCompetitor.color}-200 dark:border-${currentCompetitor.color}-800`}
                                            >
                                              {currentCompetitor.name} Specialty
                                            </Badge>
                                          )}
                                        {studentaiFeature.newFeature && (
                                          <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-300 dark:border-green-800">
                                            Exclusive
                                          </Badge>
                                        )}
                                        {studentaiFeature.info && (
                                          <TooltipProvider>
                                            <Tooltip>
                                              <TooltipTrigger asChild>
                                                <button className="flex items-center justify-center w-4 h-4 rounded-full bg-gray-100 dark:bg-gray-800">
                                                  <Info className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                                                </button>
                                              </TooltipTrigger>
                                              <TooltipContent
                                                side="top"
                                                className="max-w-xs"
                                              >
                                                <p>{studentaiFeature.info}</p>
                                              </TooltipContent>
                                            </Tooltip>
                                          </TooltipProvider>
                                        )}
                                      </div>
                                    </td>
                                    <td className="py-4 px-4 text-center">
                                      <motion.div
                                        initial={{ scale: 0.8, opacity: 0 }}
                                        animate={{ scale: 1, opacity: 1 }}
                                        transition={{ duration: 0.2 }}
                                        key={`studentai-${featureName}-${currentPlan}`}
                                      >
                                        {renderFeatureValue(
                                          studentaiFeature.value,
                                          studentaiFeature
                                        )}
                                      </motion.div>
                                    </td>
                                    <td className="py-4 px-4 text-center">
                                      <motion.div
                                        initial={{ scale: 0.8, opacity: 0 }}
                                        animate={{ scale: 1, opacity: 1 }}
                                        transition={{ duration: 0.2 }}
                                        key={`${selectedCompetitor}-${featureName}-${competitorPlanName}`}
                                      >
                                        {renderFeatureValue(
                                          competitorFeature.value,
                                          competitorFeature,
                                          selectedCompetitor
                                        )}
                                      </motion.div>
                                    </td>
                                  </tr>
                                );
                              }
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </motion.div>
      </AnimatePresence>

      {/* Special QuillBot Notes - Only show when QuillBot is selected */}
      {selectedCompetitor === "quillbot" && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="mt-6 p-5 rounded-xl bg-green-50/60 dark:bg-green-900/10 border border-green-100 dark:border-green-800/30"
        >
          <h4 className="flex items-center text-green-700 dark:text-green-400 font-semibold mb-2">
            <Info className="h-4 w-4 mr-2" />
            About Quillbot Comparison
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Quillbot is primarily a paraphrasing and writing tool with an AI
            detection feature, while StudentAIDetector is specialized in AI
            content detection. Quillbot offers additional writing features that
            aren't the main focus of StudentAIDetector.
          </p>

          <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-start">
              <div className="flex h-5 w-5 bg-green-100 dark:bg-green-900/40 rounded-full items-center justify-center mr-2 mt-0.5">
                <Check className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                <span className="font-medium text-gray-700 dark:text-gray-200">
                  Quillbot's Strengths:
                </span>{" "}
                Paraphrasing, summarization, and writing enhancement tools
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex h-5 w-5 bg-blue-100 dark:bg-blue-900/40 rounded-full items-center justify-center mr-2 mt-0.5">
                <Check className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">
                <span className="font-medium text-gray-700 dark:text-gray-200">
                  StudentAIDetector's Strengths:
                </span>{" "}
                Specialized AI detection with higher accuracy and lower false
                positives
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Legend */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="mt-6 p-4 rounded-lg bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-800"
      >
        <div className="text-sm text-gray-500 dark:text-gray-400">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="flex items-center">
              <Check className="h-4 w-4 text-green-500 mr-2" />
              <span>Feature available</span>
            </div>
            <div className="flex items-center">
              <X className="h-4 w-4 text-gray-300 dark:text-gray-700 mr-2" />
              <span>Not available</span>
            </div>
            <div className="flex items-center">
              <span className="text-blue-600 dark:text-blue-400 font-medium mr-2">
                Optional
              </span>
              <span>Add-on feature</span>
            </div>
            <div className="flex items-center">
              <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 mr-2">
                Advantage
              </Badge>
              <span>Superior feature</span>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Additional Information */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="mt-8 text-center"
      >
        <p className="text-sm text-gray-500 dark:text-gray-400">
          <Globe className="inline-block w-4 h-4 mr-1 mb-0.5" />
          Feature comparison last updated June 2023. Visit the official websites
          for the most current information.
        </p>
      </motion.div>
    </motion.div>
  );
}

// Enhanced helper function to render feature values with appropriate styling
function renderFeatureValue(value, feature, competitor = null) {
  // Get competitor-specific styling
  const getCompetitorStyle = (competitor) => {
    switch (competitor) {
      case "quillbot":
        return {
          bg: "bg-green-100 dark:bg-green-900/30",
          text: "text-green-600 dark:text-green-400",
        };
      case "originality":
        return {
          bg: "bg-orange-100 dark:bg-orange-900/30",
          text: "text-orange-600 dark:text-orange-400",
        };
      default:
        return {
          bg: "bg-green-100 dark:bg-green-900/30",
          text: "text-green-600 dark:text-green-400",
        };
    }
  };

  const style = competitor
    ? getCompetitorStyle(competitor)
    : {
        bg: "bg-green-100 dark:bg-green-900/30",
        text: "text-green-600 dark:text-green-400",
      };

  if (value === "N/A") {
    return <span className="text-gray-400 dark:text-gray-500">—</span>;
  } else if (value === true) {
    return (
      <div className="flex items-center justify-center">
        <div
          className={`w-6 h-6 rounded-full ${
            competitor ? style.bg : "bg-green-100 dark:bg-green-900/30"
          } flex items-center justify-center`}
        >
          <Check
            className={`h-3.5 w-3.5 ${
              competitor ? style.text : "text-green-600 dark:text-green-400"
            }`}
          />
        </div>
      </div>
    );
  } else if (value === false) {
    return (
      <div className="flex items-center justify-center">
        <div className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
          <X className="h-3.5 w-3.5 text-gray-400 dark:text-gray-600" />
        </div>
      </div>
    );
  } else if (value === "Optional") {
    return (
      <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium">
        Optional
      </div>
    );
  } else if (value === "Coming soon") {
    // Continuing from "Coming soon" condition
    return (
      <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 font-medium">
        Coming soon
      </div>
    );
  } else if (value === "Limited") {
    return (
      <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 font-medium">
        Limited
      </div>
    );
  } else if (value && typeof value === "string" && value.includes("Limited")) {
    // For values like "Limited (125 words)"
    return (
      <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 font-medium">
        {value}
      </div>
    );
  } else if (
    typeof value === "string" &&
    (value.includes("Good") ||
      value.includes("Advanced") ||
      value.includes("Highest") ||
      value.includes("Very Low") ||
      value.includes("Lowest") ||
      value.includes("Comprehensive") ||
      value.includes("Unlimited"))
  ) {
    return (
      <div
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs ${
          competitor ? style.bg : "bg-green-100 dark:bg-green-900/30"
        } ${
          competitor ? style.text : "text-green-700 dark:text-green-300"
        } font-medium`}
      >
        {value}
      </div>
    );
  } else if (
    typeof value === "string" &&
    (value.includes("Basic") ||
      value.includes("Low") ||
      value.includes("Up to"))
  ) {
    return (
      <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium">
        {value}
      </div>
    );
  } else if (typeof value === "string" && value.includes("Moderate")) {
    return (
      <div className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300 font-medium">
        {value}
      </div>
    );
  } else if (feature?.formatSpecial && typeof value === "string") {
    // Format for word count - check for special quillbot "Unlimited" value
    const isUnlimited = value.includes("Unlimited");
    return (
      <div
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs ${
          isUnlimited
            ? competitor === "quillbot"
              ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300"
              : "bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300"
            : "bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300"
        } font-medium`}
      >
        {value}
      </div>
    );
  } else {
    return <span className="text-gray-700 dark:text-gray-300">{value}</span>;
  }
}
