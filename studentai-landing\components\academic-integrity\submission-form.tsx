"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Upload, FileText, Shield } from "lucide-react"
import { SubmissionResult } from "@/components/academic-integrity/submission-result"
import { useToast } from "@/hooks/use-toast"

export function SubmissionForm() {
  const [inputMethod, setInputMethod] = useState("text")
  const [text, setText] = useState("")
  const [title, setTitle] = useState("")
  const [author, setAuthor] = useState("")
  const [course, setCourse] = useState("")
  const [submissionDate, setSubmissionDate] = useState("")
  const [files, setFiles] = useState<File[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const [progressValue, setProgressValue] = useState(0)
  const [progressStatus, setProgressStatus] = useState("")
  const [similarityThreshold, setSimilarityThreshold] = useState([30])
  const [compareWithPrevious, setCompareWithPrevious] = useState(true)
  const [checkWebSources, setCheckWebSources] = useState(true)
  const [checkForCollusion, setCheckForCollusion] = useState(true)
  const [result, setResult] = useState<any | null>(null)
  const { toast } = useToast()

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value)
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFiles(Array.from(e.target.files))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    setIsProcessing(true)
    setProgressStatus("Analyzing...")
    setProgressValue(0)

    // Simulate analysis progress
    const interval = setInterval(() => {
      setProgressValue((oldProgress) => {
        const newProgress = oldProgress + 10
        if (newProgress >= 100) {
          clearInterval(interval)
          return 100
        }
        return newProgress
      })
    }, 300)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 3000))

    clearInterval(interval)
    setIsProcessing(false)
    setProgressStatus("Analysis Complete")

    // Mock result data
    const mockResult = {
      overallScore: 78,
      plagiarismScore: 15,
      collusionScore: 8,
      aiGeneratedScore: 55,
      sources: [
        { url: "https://example.com/article1", similarity: 15, title: "Example Article 1" },
        { url: "https://example.com/article2", similarity: 8, title: "Example Article 2" },
      ],
      similarSubmissions: [{ id: "sub123", similarity: 8, student: "John Doe", date: "2025-03-15" }],
      highlightedText: text || "Sample text from uploaded file...",
      suspiciousSections: [
        {
          text: "This section appears to be copied from an external source.",
          startIndex: 50,
          endIndex: 120,
          type: "plagiarism",
          similarity: 92,
        },
        {
          text: "This section shows patterns consistent with AI-generated content.",
          startIndex: 200,
          endIndex: 350,
          type: "ai-generated",
          similarity: 88,
        },
      ],
    }

    setResult(mockResult)

    // Save to local storage
    try {
      const newSubmission = {
        id: Date.now().toString(),
        title: title || "Untitled Submission",
        author: author || "Unknown Author",
        course: course || "General",
        date: new Date().toISOString(),
        similarityScore: mockResult.overallScore,
        flagged: mockResult.overallScore > 50,
      }

      const savedHistory = localStorage.getItem("academicIntegrityHistory")
      const history = savedHistory ? JSON.parse(savedHistory) : []
      history.push(newSubmission)
      localStorage.setItem("academicIntegrityHistory", JSON.stringify(history))

      toast({
        title: "Analysis complete",
        description: "Your submission has been analyzed and saved to history.",
      })
    } catch (err) {
      console.error("Error saving to history:", err)
      toast({
        title: "Error saving history",
        description: "There was an error saving your submission history.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <Tabs value={inputMethod} onValueChange={setInputMethod} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="text">Text Input</TabsTrigger>
          <TabsTrigger value="file">File Upload</TabsTrigger>
        </TabsList>
        <TabsContent value="text" className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="text-input">Enter text to analyze</Label>
            <Textarea
              id="text-input"
              placeholder="Paste student submission text here..."
              className="min-h-[200px]"
              value={text}
              onChange={handleTextChange}
              disabled={isProcessing}
            />
          </div>
        </TabsContent>
        <TabsContent value="file" className="space-y-4 mt-4">
          <div className="space-y-2">
            <Label htmlFor="file-input">Upload files</Label>
            <Input
              id="file-input"
              type="file"
              multiple
              accept=".txt,.pdf,.doc,.docx"
              className="hidden"
              onChange={handleFileChange}
              disabled={isProcessing}
            />
            <Button asChild variant="outline">
              <label htmlFor="file-input">
                <Upload className="mr-2 h-4 w-4" />
                Choose Files
              </label>
            </Button>
            {files.length > 0 && (
              <div className="mt-4 space-y-2">
                <Label>Selected files ({files.length})</Label>
                <div className="max-h-[150px] overflow-y-auto space-y-2">
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                      <FileText className="h-4 w-4 text-primary" />
                      <span className="text-sm truncate flex-1">{file.name}</span>
                      <span className="text-xs text-muted-foreground">{(file.size / 1024).toFixed(1)} KB</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      <div className="space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">Submission Title</Label>
            <Input type="text" id="title" placeholder="Assignment Title" onChange={(e) => setTitle(e.target.value)} />
          </div>
          <div className="space-y-2">
            <Label htmlFor="author">Student Name</Label>
            <Input type="text" id="author" placeholder="Student Name" onChange={(e) => setAuthor(e.target.value)} />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="course">Course</Label>
            <Select onValueChange={setCourse}>
              <SelectTrigger id="course">
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ENG101">ENG101: Introduction to Composition</SelectItem>
                <SelectItem value="HIST200">HIST200: Modern World History</SelectItem>
                <SelectItem value="CS350">CS350: Data Structures</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="submission-date">Submission Date</Label>
            <Input type="date" id="submission-date" onChange={(e) => setSubmissionDate(e.target.value)} />
          </div>
        </div>
      </div>

      <Button type="submit" onClick={handleSubmit} disabled={isProcessing} className="w-full">
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {progressStatus}
          </>
        ) : (
          <>
            <Shield className="mr-2 h-4 w-4" />
            Analyze for Academic Integrity
          </>
        )}
      </Button>

      {result && <SubmissionResult result={result} />}
    </div>
  )
}
