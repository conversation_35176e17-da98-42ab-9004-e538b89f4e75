"use client"

import { Card, CardContent } from "@/components/ui/card"

export function RoadmapStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <CardContent className="flex items-center justify-between p-4">
          <div>
            <h3 className="text-lg font-semibold">Live Features</h3>
            <p className="text-muted-foreground">Features currently available</p>
          </div>
          <div className="text-3xl font-bold text-green-600">6</div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center justify-between p-4">
          <div>
            <h3 className="text-lg font-semibold">Planned Features</h3>
            <p className="text-muted-foreground">Features actively in development</p>
          </div>
          <div className="text-3xl font-bold text-blue-600">5</div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center justify-between p-4">
          <div>
            <h3 className="text-lg font-semibold">Backlog Features</h3>
            <p className="text-muted-foreground">Features under consideration</p>
          </div>
          <div className="text-3xl font-bold text-amber-600">6</div>
        </CardContent>
      </Card>
    </div>
  )
}
