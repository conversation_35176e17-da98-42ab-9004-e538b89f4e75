"use client";

import { useState, useEffect, useRef } from "react";
import { motion, useAnimation, useInView } from "framer-motion";
import Link from "next/link";
import {
  BarChart3,
  Fingerprint,
  AlertCircle,
  Check,
  ArrowRight,
  Layers,
  ScanSearch,
  <PERSON><PERSON><PERSON>ck,
  Gauge,
  ListFilter,
  <PERSON>rk<PERSON>
} from "lucide-react";
import { Button } from "@/components/ui/button";

// Helper function to get color-specific classes
const getColorClass = (type, color, isActive = true) => {
  const colorMap = {
    blue: {
      bg: "bg-blue-100 dark:bg-blue-900/30",
      text: "text-blue-600 dark:text-blue-400",
      border: "border-blue-200 dark:border-blue-900/30",
      fill: "bg-blue-500"
    },
    purple: {
      bg: "bg-purple-100 dark:bg-purple-900/30",
      text: "text-purple-600 dark:text-purple-400",
      border: "border-purple-200 dark:border-purple-900/30",
      fill: "bg-purple-500"
    },
    amber: {
      bg: "bg-amber-100 dark:bg-amber-900/30",
      text: "text-amber-600 dark:text-amber-400", 
      border: "border-amber-200 dark:border-amber-900/30",
      fill: "bg-amber-500"
    },
    emerald: {
      bg: "bg-emerald-100 dark:bg-emerald-900/30",
      text: "text-emerald-600 dark:text-emerald-400",
      border: "border-emerald-200 dark:border-emerald-900/30",
      fill: "bg-emerald-500"
    },
    indigo: {
      bg: "bg-indigo-100 dark:bg-indigo-900/30",
      text: "text-indigo-600 dark:text-indigo-400",
      border: "border-indigo-200 dark:border-indigo-900/30", 
      fill: "bg-indigo-500"
    },
    fuchsia: {
      bg: "bg-fuchsia-100 dark:bg-fuchsia-900/30",
      text: "text-fuchsia-600 dark:text-fuchsia-400",
      border: "border-fuchsia-200 dark:border-fuchsia-900/30",
      fill: "bg-fuchsia-500"
    }
  };

  if (!isActive) return "";
  return colorMap[color]?.[type] || "";
};

export default function AdvancedDetectionFeatures() {
  const [activeFeature, setActiveFeature] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const containerRef = useRef(null);
  const isInView = useInView(containerRef, { once: false, amount: 0.2 });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start("visible");
      
      // Auto-rotate features every 4 seconds
      const interval = setInterval(() => {
        if (!isAnimating) {
          setActiveFeature(prev => (prev === 3 ? 0 : prev + 1));
        }
      }, 4000);
      
      return () => clearInterval(interval);
    }
  }, [isInView, controls, isAnimating]);

  // Mock detection results for visualization
  const sampleText = "This essay examines the ethical implications of artificial intelligence in modern society. The rapid advancement of AI technologies has raised important questions about privacy, autonomy, and the future of human work. While these systems offer tremendous benefits, we must carefully consider their impact on various aspects of daily life.";
  
  // Example AI model detection results
  const modelDetection = [
    { model: "GPT-4", probability: 0.87, color: "blue" },
    { model: "Claude 3", probability: 0.08, color: "indigo" },
    { model: "Bard/Gemini", probability: 0.03, color: "purple" },
    { model: "Llama 3", probability: 0.02, color: "fuchsia" },
  ];
  
  // Sentence-level analysis
  const sentenceAnalysis = [
    { text: "This essay examines the ethical implications of artificial intelligence in modern society.", score: 0.65 },
    { text: "The rapid advancement of AI technologies has raised important questions about privacy, autonomy, and the future of human work.", score: 0.92 },
    { text: "While these systems offer tremendous benefits, we must carefully consider their impact on various aspects of daily life.", score: 0.78 }
  ];

  // Advanced features to showcase
  const features = [
    {
      id: "model-detection",
      title: "AI Model Detection",
      description: "Identify which specific AI model generated the content with industry-leading accuracy across all major models.",
      icon: Fingerprint,
      color: "blue",
    },
    {
      id: "sentence-analysis",
      title: "Sentence-Level Analysis",
      description: "Detailed breakdown of AI probability scores for each sentence, pinpointing exactly where AI content appears.",
      icon: ListFilter,
      color: "purple",
    },
    {
      id: "manipulation-detection",
      title: "Manipulation Detection",
      description: "Detect when AI content has been deliberately modified in attempts to bypass detection systems.",
      icon: AlertCircle,
      color: "amber",
    },
    {
      id: "multi-language",
      title: "Multi-Language Support",
      description: "Analyze content in 17 different languages with the same high accuracy as English text detection.",
      icon: ScanSearch,
      color: "emerald",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  return (
    <section className="w-full py-24 overflow-hidden relative bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Decorative elements */}
      <div className="absolute inset-0" style={{ zIndex: -1 }}>
        <div className="absolute top-40 right-[5%] w-[500px] h-[500px] opacity-20 dark:opacity-10">
          <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#4F46E5" />
                <stop offset="100%" stopColor="#7C3AED" />
              </linearGradient>
            </defs>
            <path
              fill="url(#gradient1)"
              d="M39.9,-65.7C51.1,-58.5,59.3,-46.6,64.6,-34C69.8,-21.4,72,-8.1,70.8,4.8C69.5,17.7,64.8,30.1,56.8,39.9C48.8,49.7,37.5,56.8,25.6,60.4C13.6,64,1.1,64,-11.6,62.6C-24.2,61.2,-36.9,58.3,-46.9,51.1C-56.8,43.9,-63.9,32.4,-67.4,20.1C-70.9,7.8,-70.8,-5.3,-68.5,-18.4C-66.2,-31.4,-61.8,-44.4,-52.8,-52.4C-43.7,-60.3,-30.1,-63.3,-17.1,-64.7C-4.1,-66.2,8.4,-66.1,20.2,-67.2C31.9,-68.3,43,-68.6,52.1,-63.7C61.2,-58.9,68.5,-49.1,67.6,-38.9"
            />
          </svg>
        </div>
        
        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#4444440a_1px,transparent_1px),linear-gradient(to_bottom,#4444440a_1px,transparent_1px)] bg-[size:64px_64px] dark:bg-[linear-gradient(to_right,#ffffff0a_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0a_1px,transparent_1px)]"></div>
      </div>

      <div className="container mx-auto px-4 relative">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-blue-100/80 dark:bg-blue-900/30 mb-6">
            <span className="flex h-2 w-2 rounded-full bg-blue-500"></span>
            <span className="text-xs font-medium text-blue-800 dark:text-blue-300">
              Advanced Technology
            </span>
          </div>
          
          <h2 className="text-4xl md:text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 mb-4">
            Advanced Detection Features
          </h2>
          
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Our AI detection technology goes beyond basic analysis with powerful features that deliver unparalleled precision and detailed insights
          </p>
          
          <div className="h-1 w-40 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto mt-6 rounded-full"></div>
        </div>

        {/* Main content with visualization */}
        <div ref={containerRef} className="grid lg:grid-cols-5 gap-8 mb-24">
          {/* Feature navigation sidebar - 1 column on mobile, 2 columns on large screens */}
          <div className="lg:col-span-2">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate={controls}
              className="grid sm:grid-cols-2 lg:grid-cols-1 gap-4"
            >
              {features.map((feature, index) => (
                <motion.div
                  key={feature.id}
                  variants={itemVariants}
                  className={`p-6 rounded-xl cursor-pointer transition-all duration-300 ${
                    activeFeature === index
                      ? `bg-white dark:bg-gray-800 shadow-lg border ${getColorClass("border", feature.color)}`
                      : "bg-white/60 dark:bg-gray-800/60 hover:bg-white hover:dark:bg-gray-800 border border-gray-100 dark:border-gray-700 hover:border-gray-200 dark:hover:border-gray-600"
                  }`}
                  onClick={() => {
                    setIsAnimating(true);
                    setActiveFeature(index);
                    setTimeout(() => setIsAnimating(false), 500);
                  }}
                >
                  <div className="flex items-start gap-4">
                    <div
                      className={`p-3 rounded-lg ${
                        activeFeature === index
                          ? getColorClass("bg", feature.color) + " " + getColorClass("text", feature.color)
                          : "bg-gray-100 dark:bg-gray-700/60 text-gray-500 dark:text-gray-400"
                      }`}
                    >
                      <feature.icon className="h-6 w-6" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1 flex items-center gap-2">
                        {feature.title}
                        {activeFeature === index && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className={`h-2 w-2 rounded-full ${getColorClass("fill", feature.color)}`}
                          />
                        )}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>

          {/* Feature visualization - 3 columns on large screens */}
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden h-full">
              {/* Feature display header */}
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/80 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${getColorClass("bg", features[activeFeature].color)} ${getColorClass("text", features[activeFeature].color)}`}>
                    <features[activeFeature].icon className="h-5 w-5" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {features[activeFeature].title}
                  </h3>
                </div>
                <div className="flex space-x-1.5">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
              </div>

              {/* Feature visualization content */}
              <div className="p-6">
                {/* Model Detection */}
                {activeFeature === 0 && (
                  <motion.div 
                    initial={{ opacity: 0 }} 
                    animate={{ opacity: 1 }} 
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6"
                  >
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                        Source Model Detection
                      </h4>
                      <div className="p-4 bg-gray-50 dark:bg-gray-800/80 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="space-y-3">
                          {modelDetection.map((model, index) => (
                            <div key={index} className="space-y-1">
                              <div className="flex justify-between text-sm mb-1">
                                <span className="font-medium text-gray-700 dark:text-gray-300">{model.model}</span>
                                <span className={`font-medium ${
                                  model.probability > 0.8 
                                  ? "text-red-600 dark:text-red-400" 
                                  : model.probability > 0.4 
                                  ? "text-amber-600 dark:text-amber-400" 
                                  : "text-gray-600 dark:text-gray-400"
                                }`}>
                                  {Math.round(model.probability * 100)}%
                                </span>
                              </div>
                              <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                <motion.div
                                  className={`h-full rounded-full ${getColorClass("fill", model.color)}`}
                                  initial={{ width: 0 }}
                                  animate={{ width: `${model.probability * 100}%` }}
                                  transition={{ duration: 1, delay: index * 0.2 }}
                                />
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Detection Confidence
                            </div>
                            <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-sm font-medium">
                              <span>High</span>
                              <Gauge className="h-4 w-4" />
                            </div>
                          </div>
                          <div className="mt-2">
                            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                              <motion.div 
                                className="h-full bg-gradient-to-r from-amber-500 via-red-500 to-red-600 rounded-full" 
                                initial={{ width: 0 }}
                                animate={{ width: '87%' }}
                                transition={{ duration: 1.2, delay: 0.8 }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/30 text-blue-800 dark:text-blue-300">
                      <Layers className="h-5 w-5 flex-shrink-0" />
                      <p className="text-sm">
                        Our model detection is trained on over 30 different AI models, including the latest versions of GPT-4, Claude 3, Bard/Gemini, and Llama, with continuous updates as new models emerge.
                      </p>
                    </div>
                  </motion.div>
                )}

                {/* Sentence Analysis */}
                {activeFeature === 1 && (
                  <motion.div 
                    initial={{ opacity: 0 }} 
                    animate={{ opacity: 1 }} 
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6"
                  >
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                        Sentence-Level AI Detection
                      </h4>
                      <div className="p-4 bg-gray-50 dark:bg-gray-800/80 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="space-y-4">
                          {sentenceAnalysis.map((sentence, index) => {
                            let bgClass = "bg-green-50 dark:bg-green-900/20";
                            let borderClass = "border-green-200 dark:border-green-800/30";
                            let textClass = "text-green-800 dark:text-green-300";
                            let bgBadgeClass = "bg-green-100 dark:bg-green-900/40";
                            let label = "Low AI Probability";
                            
                            if (sentence.score > 0.8) {
                              bgClass = "bg-red-50 dark:bg-red-900/20";
                              borderClass = "border-red-200 dark:border-red-800/30";
                              textClass = "text-red-800 dark:text-red-300";
                              bgBadgeClass = "bg-red-100 dark:bg-red-900/40";
                              label = "High AI Probability";
                            } else if (sentence.score > 0.7) {
                              bgClass = "bg-amber-50 dark:bg-amber-900/20";
                              borderClass = "border-amber-200 dark:border-amber-800/30";
                              textClass = "text-amber-800 dark:text-amber-300";
                              bgBadgeClass = "bg-amber-100 dark:bg-amber-900/40";
                              label = "Moderate AI Probability";
                            }
                            
                            return (
                              <motion.div 
                                key={index}
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, delay: index * 0.2 }}
                                className={`p-3 rounded-lg border ${bgClass} ${borderClass}`}
                              >
                                <div className="flex justify-between mb-2 items-center">
                                  <div className={`px-2 py-0.5 rounded text-xs font-medium ${bgBadgeClass} ${textClass}`}>
                                    {label}
                                  </div>
                                  <span className={`text-sm font-medium ${textClass}`}>
                                    {Math.round(sentence.score * 100)}%
                                  </span>
                                </div>
                                <p className="text-gray-700 dark:text-gray-300 text-sm">
                                  {sentence.text}
                                </p>
                              </motion.div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-100 dark:border-purple-800/30 text-purple-800 dark:text-purple-300">
                      <ListFilter className="h-5 w-5 flex-shrink-0" />
                      <p className="text-sm">
                        Our sentence-level analysis breaks down longer documents to pinpoint exactly which parts contain AI-generated content, helping identify partially AI-written submissions.
                      </p>
                    </div>
                  </motion.div>
                )}

                {/* Manipulation Detection */}
                {activeFeature === 2 && (
                  <motion.div 
                    initial={{ opacity: 0 }} 
                    animate={{ opacity: 1 }} 
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6"
                  >
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                        Anti-Evasion Technology
                      </h4>
                      <div className="p-4 bg-gray-50 dark:bg-gray-800/80 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="space-y-4">
                          <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                            <div className="p-3 bg-gray-100 dark:bg-gray-700/60 border-b border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              Detected Evasion Techniques
                            </div>
                            <div className="p-4">
                              <div className="space-y-3">
                                {[
                                  { technique: "Word Substitution", detected: true, confidence: 0.89 },
                                  { technique: "Syntax Manipulation", detected: true, confidence: 0.76 },
                                  { technique: "Translation Cycling", detected: false, confidence: 0.34 },
                                  { technique: "Character Insertion", detected: true, confidence: 0.92 },
                                ].map((item, i) => (
                                  <motion.div 
                                    key={i}
                                    initial={{ opacity: 0, x: -10 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ duration: 0.5, delay: i * 0.2 }}
                                    className="flex items-center justify-between p-2 border-b border-gray-100 dark:border-gray-700 last:border-0"
                                  >
                                    <div className="flex items-center">
                                      {item.detected ? (
                                        <AlertCircle className="h-4 w-4 text-amber-500 mr-2" />
                                      ) : (
                                        <Check className="h-4 w-4 text-green-500 mr-2" />
                                      )}
                                      <span className="text-gray-700 dark:text-gray-300 text-sm">
                                        {item.technique}
                                      </span>
                                    </div>
                                    <div className="flex items-center">
                                      <div className="h-2 w-16 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 mr-2">
                                        <motion.div 
                                          className={`h-full ${item.detected ? "bg-amber-500" : "bg-green-500"}`}
                                          initial={{ width: 0 }}
                                          animate={{ width: `${item.confidence * 100}%` }}
                                          transition={{ duration: 0.8, delay: 0.4 + i * 0.1 }}
                                        />
                                      </div>
                                      <span className={`text-xs font-medium ${item.detected ? "text-amber-600 dark:text-amber-400" : "text-green-600 dark:text-green-400"}`}>
                                        {Math.round(item.confidence * 100)}%
                                      </span>
                                    </div>
                                  </motion.div>
                                ))}
                              </div>
                              <div className="mt-4 p-3 bg-amber-50 dark:bg-amber-900/20 rounded border border-amber-100 dark:border-amber-800/30 text-sm text-amber-800 dark:text-amber-300">
                                <div className="flex items-start">
                                  <AlertCircle className="h-4 w-4 mt-0.5 mr-2 flex-shrink-0" />
                                  <span>
                                    Manipulation detected: This text shows signs of deliberate modification to evade AI detection systems.
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-100 dark:border-amber-800/30 text-amber-800 dark:text-amber-300">
                      <ShieldCheck className="h-5 w-5 flex-shrink-0" />
                      <p className="text-sm">
                        Our system can identify when text has been deliberately modified to evade AI detection, maintaining accuracy even against sophisticated evasion techniques.
                      </p>
                    </div>
                  </motion.div>
                )}

                {/* Multi-language Support */}
                {activeFeature === 3 && (
                  <motion.div 
                    initial={{ opacity: 0 }} 
                    animate={{ opacity: 1 }} 
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6"
                  >
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                        Multi-Language Detection
                      </h4>
                      <div className="p-4 bg-gray-50 dark:bg-gray-800/80 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {[
                            { language: "English", accuracy: 0.994, supported: true },
                            { language: "Spanish", accuracy: 0.989, supported: true },
                            { language: "French", accuracy: 0.986, supported: true },
                            { language: "German", accuracy: 0.985, supported: true },
                            { language: "Chinese", accuracy: 0.972, supported: true },
                            { language: "Arabic", accuracy: 0.968, supported: true },
                            { language: "Portuguese", accuracy: 0.985, supported: true },
                            { language: "Russian", accuracy: 0.977, supported: true },
                            { language: "Japanese", accuracy: 0.971, supported: true },
                          ].map((lang, i) => (
                            <motion.div 
                              key={i}
                              initial={{ opacity: 0, scale: 0.95 }}
                              animate={{ opacity: 1, scale: 1 }}
                              transition={{ duration: 0.4, delay: i * 0.1 }}
                              className="p-3 rounded-lg bg-white dark:bg-gray-800/95 border border-gray-200 dark:border-gray-700 flex flex-col"
                            >
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-gray-900 dark:text-white">
                                  {lang.language}
                                </span>
                                {lang.supported && (
                                  <div className="flex items-center text-emerald-600 dark:text-emerald-400 text-xs">
                                    <Check className="h-3 w-3 mr-0.5" />
                                    <span>Active</span>
                                  </div>
                                )}
                              </div>
                              <div className="mt-auto">
                                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                                  <span>Detection Accuracy</span>
                                  <span>{Math.round(lang.accuracy * 1000) / 10}%</span>
                                </div>
                                <div className="h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                                  <motion.div
                                    className="h-full bg-emerald-500 rounded-full"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${lang.accuracy * 100}%` }}
                                    transition={{ duration: 1, delay: 0.3 + i * 0.1 }}
                                  />
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                        <div className="mt-4 text-center">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            + 8 more supported languages
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3 p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg border border-emerald-100 dark:border-emerald-800/30 text-emerald-800 dark:text-emerald-300">
                      <Sparkles className="h-5 w-5 flex-shrink-0" />
                      <p className="text-sm">
                        Our system analyzes content in 17 languages with consistent accuracy, supporting international institutions and global content verification needs.
                      </p>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Stat cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
          {[
            { stat: "99.4%", label: "Detection Accuracy", icon: Gauge, color: "blue" },
            { stat: "30+", label: "AI Models Detected", icon: Layers, color: "indigo" },
            { stat: "17", label: "Supported Languages", icon: ScanSearch, color: "purple" },
            { stat: "2.8s", label: "Average Response Time", icon: BarChart3, color: "fuchsia" },
          ].map((item, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.2 + i * 0.1, duration: 0.5 }}
              className={`p-6 rounded-xl border ${getColorClass("border", item.color)} bg-white dark:bg-gray-800 shadow-md hover:shadow-lg transition-shadow`}
            >
              <div className={`p-3 rounded-lg ${getColorClass("bg", item.color, true)}/70 dark:${getColorClass("bg", item.color, true)}/20 mb-4 inline-block`}>
                <item.icon className={getColorClass("text", item.color)} />
              </div>
              <h3 className={`text-3xl font-bold ${getColorClass("text", item.color)}`}>
                {item.stat}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm">
                {item.label}
              </p>
            </motion.div>
          ))}
        </div>
        
        {/* CTA */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Ready for industry-leading AI detection?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-8">
            Get started with our advanced AI detection system and gain access to all these powerful features through our simple and intuitive interface.
          </p>
          <Button size="lg" asChild className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
            <Link href="/pricing" className="flex items-center gap-2 px-8">
              View Pricing Options
              <ArrowRight className="h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}