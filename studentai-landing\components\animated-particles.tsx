"use client";

import React from "react";

export default function AnimatedParticles() {
  return (
    <div className="particles-container absolute inset-0">
      {[...Array(20)].map((_, i) => (
        <div
          key={i}
          className="absolute rounded-full bg-blue-600/30 dark:bg-blue-400/20"
          style={{
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
            width: `${Math.random() * 10 + 2}px`,
            height: `${Math.random() * 10 + 2}px`,
            animation: `float-vertical ${
              Math.random() * 10 + 15
            }s infinite alternate ease-in-out`,
            animationDelay: `${Math.random() * 5}s`,
          }}
        ></div>
      ))}
    </div>
  );
}
