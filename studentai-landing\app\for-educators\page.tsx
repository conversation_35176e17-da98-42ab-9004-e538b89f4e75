import type { <PERSON><PERSON><PERSON> } from "next";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { CheckCircle } from "lucide-react";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";

export const metadata: Metadata = {
  title:
    "AI Detector for Teachers | Academic Integrity Tools | StudentAIDetector",
  description:
    "Powerful AI detection tools designed specifically for educators to maintain academic integrity, identify AI-generated content in student submissions, and promote fair assessment practices.",
  keywords:
    "AI detector for teachers, academic integrity tools, AI content detection, classroom AI tools, prevent AI plagiarism, education technology",
};

export default function EducatorsPage() {
  return (
    <>
      <Navbar />
      <div className="container mx-auto px-4 py-12 pt-28">
        <div className="max-w-5xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              AI Detection Tools{" "}
              <span className="text-primary">Designed for Educators</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              Maintain academic integrity and ensure fair assessment with our
              advanced AI detection technology specifically designed for
              educational environments.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="https://app.studentaidetector.com/signup">
                  Start Free Trial
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="#case-studies">View Case Studies</Link>
              </Button>
            </div>
          </div>

          {/* Key Features Section */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold mb-8 text-center">
              Tools Designed for{" "}
              <span className="text-primary">Educational Excellence</span>
            </h2>

            <div className="grid md:grid-cols-3 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle>Batch Processing</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Upload and analyze multiple student submissions
                    simultaneously, saving valuable time during assessment
                    periods.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Detailed Reports</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Receive comprehensive analysis with highlighted sections and
                    probability scores to facilitate evidence-based discussions
                    with students.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>LMS Integration</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>
                    Seamlessly integrate with popular learning management
                    systems like Canvas, Blackboard, and Moodle for streamlined
                    workflows.
                  </p>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* How It Works Section */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold mb-8 text-center">
              How Our <span className="text-primary">AI Detector</span> Works
            </h2>

            <div className="bg-muted p-8 rounded-lg">
              <ol className="space-y-6">
                <li className="flex gap-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                    1
                  </div>
                  <div>
                    <h3 className="font-bold text-lg">Upload Student Work</h3>
                    <p>
                      Upload individual assignments or batch process entire
                      class submissions through our secure platform or LMS
                      integration.
                    </p>
                  </div>
                </li>

                <li className="flex gap-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                    2
                  </div>
                  <div>
                    <h3 className="font-bold text-lg">Advanced Analysis</h3>
                    <p>
                      Our proprietary algorithm analyzes the text using multiple
                      detection methods, including linguistic patterns,
                      statistical anomalies, and neural network analysis.
                    </p>
                  </div>
                </li>

                <li className="flex gap-4">
                  <div className="bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0">
                    3
                  </div>
                  <div>
                    <h3 className="font-bold text-lg">Comprehensive Results</h3>
                    <p>
                      Receive detailed reports with AI probability scores,
                      highlighted suspicious sections, and evidence-based
                      explanations to support academic integrity conversations.
                    </p>
                  </div>
                </li>
              </ol>
            </div>
          </section>

          {/* Case Studies Section */}
          <section id="case-studies" className="mb-16">
            <h2 className="text-3xl font-bold mb-8 text-center">
              Success Stories from{" "}
              <span className="text-primary">Educational Institutions</span>
            </h2>

            <Tabs defaultValue="university">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="university">University</TabsTrigger>
                <TabsTrigger value="highschool">High School</TabsTrigger>
                <TabsTrigger value="community">Community College</TabsTrigger>
              </TabsList>

              <TabsContent value="university" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Stanford University</CardTitle>
                    <CardDescription>
                      Department of Computer Science
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-4">
                      "After implementing StudentAIDetector across our
                      programming and writing courses, we saw a 43% increase in
                      original work submissions and a significant improvement in
                      academic discussions around AI usage."
                    </p>
                    <p className="font-semibold">
                      — Dr. Jennifer Ramirez, Department Chair
                    </p>

                    <div className="mt-6 bg-muted p-4 rounded-lg">
                      <h4 className="font-bold mb-2">Results:</h4>
                      <ul className="space-y-2">
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>43% increase in original work submissions</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            92% of faculty reported more productive
                            conversations about AI ethics
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            Integrated with Canvas LMS for seamless assessment
                            workflow
                          </span>
                        </li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="highschool" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Westlake High School</CardTitle>
                    <CardDescription>
                      English and Humanities Department
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-4">
                      "StudentAIDetector has transformed how we approach writing
                      assignments. Our teachers now have concrete evidence to
                      discuss AI usage with students, turning potential
                      disciplinary situations into valuable learning
                      opportunities."
                    </p>
                    <p className="font-semibold">— Michael Chen, Principal</p>

                    <div className="mt-6 bg-muted p-4 rounded-lg">
                      <h4 className="font-bold mb-2">Results:</h4>
                      <ul className="space-y-2">
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            68% reduction in suspected AI-generated submissions
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            Teachers report 87% more confidence in assessment
                            fairness
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            Implemented school-wide AI usage policy based on
                            detection capabilities
                          </span>
                        </li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="community" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Riverside Community College</CardTitle>
                    <CardDescription>
                      Multi-Department Implementation
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="mb-4">
                      "With our diverse student population, we needed a solution
                      that would help maintain academic standards while
                      supporting students in understanding appropriate AI use.
                      StudentAIDetector provided exactly that balance."
                    </p>
                    <p className="font-semibold">
                      — Dr. Alisha Washington, Academic Dean
                    </p>

                    <div className="mt-6 bg-muted p-4 rounded-lg">
                      <h4 className="font-bold mb-2">Results:</h4>
                      <ul className="space-y-2">
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            52% increase in student self-declarations of AI
                            assistance
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            Created a campus-wide AI literacy program based on
                            detection insights
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                          <span>
                            95% of faculty reported time savings with batch
                            processing feature
                          </span>
                        </li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </section>

          {/* Academic Integrity Section */}
          <section className="mb-16">
            <h2 className="text-3xl font-bold mb-8 text-center">
              Promoting <span className="text-primary">Academic Integrity</span>
            </h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div className="bg-muted p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4">Fair Assessment</h3>
                <p className="mb-4">
                  Our AI detection tools help ensure that all students are
                  evaluated fairly, regardless of their access to AI tools or
                  technical expertise.
                </p>
                <p>
                  By identifying AI-generated content, educators can maintain
                  consistent standards and reward genuine student effort and
                  understanding.
                </p>
              </div>

              <div className="bg-muted p-6 rounded-lg">
                <h3 className="text-xl font-bold mb-4">Teaching Opportunity</h3>
                <p className="mb-4">
                  Detection isn't just about catching violations—it's an
                  opportunity to teach responsible AI usage in academic
                  contexts.
                </p>
                <p>
                  Our educator resources include discussion guides and lesson
                  plans on AI ethics and appropriate use cases for various
                  educational levels.
                </p>
              </div>
            </div>
          </section>

          {/* Pricing CTA */}
          <section className="text-center">
            <h2 className="text-3xl font-bold mb-6">
              Ready to Maintain{" "}
              <span className="text-primary">Academic Integrity</span>?
            </h2>
            <p className="text-xl mb-8 max-w-3xl mx-auto">
              Join thousands of educational institutions using StudentAIDetector
              to promote fair assessment and teach responsible AI usage.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/pricing">View Educational Pricing</Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/contact">Request Demo</Link>
              </Button>
            </div>
          </section>
        </div>
      </div>
      <Footer />
    </>
  );
}
