"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { But<PERSON> } from "@/components/ui/button";
import {
  HelpCircle,
  Search,
  MessageSquare,
  MinusCircle,
  Mail,
} from "lucide-react";

const faqs = [
  {
    question: "How accurate is your AI detection technology?",
    answer:
      "Our AI detection technology achieves industry-leading accuracy rates: 99% accuracy for GPT-4 generated content, 98% accuracy for ChatGPT content, 97% accuracy for Claude and other AI models. These rates have been validated through extensive testing and independent verification. However, we acknowledge that no AI detection system is perfect, and we continuously update our algorithms to maintain high accuracy as AI models evolve.",
  },
  {
    question: "How does your AI detector work?",
    answer:
      "Our AI detector uses a proprietary multi-layered approach that combines neural network analysis, linguistic pattern recognition, and probabilistic modeling. The system analyzes various aspects of text, including statistical patterns, syntactic structures, token predictability, and semantic consistency to identify characteristics typical of AI-generated content. Unlike simpler detectors, we don't rely on a single method but instead use a cross-verification system that combines multiple detection algorithms to reduce false positives and increase accuracy.",
  },
  {
    question: "Can your detector identify content from all AI writing tools?",
    answer:
      "Our detector is trained to identify content from all major AI writing models, including GPT-3.5, GPT-4, Claude, Bard, and others. We continuously update our system to recognize new AI models as they emerge. While we maintain high accuracy across different AI systems, our detection rates may vary slightly depending on the specific model used and how the AI output has been modified. We're committed to staying ahead of the curve as AI technology evolves.",
  },
  {
    question: "How much text do I need to submit for accurate detection?",
    answer:
      "For optimal accuracy, we recommend submitting at least 300 words (approximately one double-spaced page). While our system can analyze shorter texts, the accuracy increases with longer samples as this provides more linguistic patterns for our algorithms to analyze. For texts under 100 words, we still provide analysis but include a confidence rating to indicate the reliability of the results.",
  },
  {
    question: "Do you store the text I submit for analysis?",
    answer:
      "We temporarily store submitted text to process your request and provide results. This data is automatically deleted after 30 days. We never use your submitted content for training our models without explicit permission, and we maintain strict data privacy protocols. For educational institutions and enterprise customers, we offer enhanced privacy options, including immediate data deletion after analysis and custom data retention policies.",
  },
  {
    question: "Can I integrate your AI detector with my LMS or other systems?",
    answer:
      "Yes, we offer API access and integration options for educational institutions and businesses. Our system can be integrated with popular Learning Management Systems (LMS) like Canvas, Blackboard, and Moodle, as well as content management systems and workflow tools. We provide comprehensive documentation and technical support for integration. For enterprise customers, we offer custom integration solutions tailored to your specific needs.",
  },
  {
    question: "What if a student disputes the AI detection results?",
    answer:
      "We recommend using our AI detector as one tool in a broader academic integrity approach, not as the sole determinant. Our detailed reports provide specific evidence of AI patterns, which can facilitate constructive discussions with students. For disputed cases, we offer an appeal process where our team can provide a more in-depth analysis. We also provide educational resources to help institutions develop fair policies around AI use and detection.",
  },
  {
    question: "Do you offer educational discounts?",
    answer:
      "Yes, we offer significant discounts for educational institutions at all levels, from K-12 schools to universities. We also provide special pricing for non-profit organizations and educational institutions in developing countries. Our goal is to make our technology accessible to educators worldwide. Contact our sales team for details on educational pricing and volume discounts.",
  },
];

export default function FaqSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeQuestion, setActiveQuestion] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const sectionRef = useRef<HTMLElement>(null);

  // Intersection Observer to trigger animations when section is visible
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    const section = sectionRef.current;
    if (section) observer.observe(section);

    return () => {
      if (section) observer.unobserve(section);
    };
  }, []);

  // Filter FAQs based on search query
  const filteredFaqs =
    searchQuery.trim() === ""
      ? faqs
      : faqs.filter(
          (faq) =>
            faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
            faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
        );

  return (
    <section
      ref={sectionRef}
      className="w-full py-16 md:py-28 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 relative overflow-hidden"
    >
      {/* Decorative elements */}
      <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-100/20 dark:bg-blue-900/10 rounded-full blur-3xl -z-10" />
      <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-purple-100/20 dark:bg-purple-900/10 rounded-full blur-3xl -z-10" />

      {/* Subtle animated dots */}
      <div className="absolute inset-0 -z-5">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-blue-400/20 dark:bg-blue-400/10"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              width: `${Math.random() * 5 + 2}px`,
              height: `${Math.random() * 5 + 2}px`,
              animation: `float-vertical ${
                Math.random() * 10 + 15
              }s infinite alternate ease-in-out`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 md:px-6 relative">
        <div
          className={`text-center mb-12 ${
            isVisible ? "animate-fade-in" : "opacity-0"
          }`}
        >
          <div className="inline-flex items-center gap-2 mb-2 px-4 py-1.5 bg-blue-100/80 dark:bg-blue-900/30 backdrop-blur-sm rounded-full border border-blue-200/40 dark:border-blue-700/40 shadow-sm">
            <HelpCircle className="h-4 w-4 text-blue-500 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              Got Questions?
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mt-4">
            Frequently Asked Questions
          </h2>

          <p className="max-w-[800px] mx-auto mt-4 text-gray-600 dark:text-gray-300 md:text-xl/relaxed">
            Get answers to common questions about our AI detection tools
          </p>

          {/* Search bar */}
          <div
            className={`max-w-md mx-auto mt-8 relative ${
              isVisible ? "animate-fade-in" : "opacity-0"
            }`}
            style={{ animationDelay: "0.3s" }}
          >
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-700 rounded-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery("")}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <span className="sr-only">Clear search</span>
                  <MinusCircle className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* FAQ Accordion */}
        <div className="max-w-4xl mx-auto mt-8">
          <AnimatePresence>
            {filteredFaqs.length > 0 ? (
              <Accordion
                type="single"
                collapsible
                className="w-full"
                value={activeQuestion}
                onValueChange={setActiveQuestion}
              >
                {filteredFaqs.map((faq, index) => (
                  <motion.div
                    key={faq.question}
                    initial={{ opacity: 0, y: 20 }}
                    animate={isVisible ? { opacity: 1, y: 0 } : {}}
                    transition={{ duration: 0.4, delay: 0.1 * index }}
                    className="mb-4"
                  >
                    <AccordionItem
                      value={faq.question}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm bg-white dark:bg-gray-800"
                    >
                      <AccordionTrigger className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-750 data-[state=open]:bg-blue-50 dark:data-[state=open]:bg-blue-900/20 transition-all">
                        <span className="text-left font-medium text-gray-900 dark:text-gray-100">
                          {faq.question}
                        </span>
                      </AccordionTrigger>
                      <AccordionContent className="px-6 py-4 text-gray-600 dark:text-gray-300">
                        {faq.answer}
                      </AccordionContent>
                    </AccordionItem>
                  </motion.div>
                ))}
              </Accordion>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-center py-12"
              >
                <HelpCircle className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  No matching questions found
                </h3>
                <p className="mt-2 text-gray-500 dark:text-gray-400">
                  Try a different search term or browse all questions above
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Contact section */}
        <div className="mt-16 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isVisible ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Still have questions?
            </h3>
            <p className="mt-2 text-gray-600 dark:text-gray-300">
              Contact our support team for more information
            </p>
            <div className="mt-6 flex flex-wrap justify-center gap-4">
              <Button variant="outline" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email Support
              </Button>
              <Button className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Live Chat
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                </span>
              </Button>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
