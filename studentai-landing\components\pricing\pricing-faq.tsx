import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Card, CardContent } from "@/components/ui/card"

export function PricingFaq() {
  const faqs = [
    {
      question: "How do the usage limits work?",
      answer:
        "Each plan comes with a set number of AI detection queries per month. A query is counted each time you submit text for analysis. Word limits apply to each individual query. If you reach your monthly limit, you can purchase additional credits or upgrade your plan.",
    },
    {
      question: "Can I switch plans at any time?",
      answer:
        "Yes, you can upgrade your plan at any time, and the new features will be available immediately. When downgrading, changes will take effect at the start of your next billing cycle. Any unused credits or features from your current plan will not be carried over.",
    },
    {
      question: "Do you offer educational discounts?",
      answer:
        "Yes, we offer special pricing for educational institutions. The discount varies based on the size of the institution and number of users. Please contact our sales team for more information about educational pricing.",
    },
    {
      question: "How accurate is the AI detection?",
      answer:
        "Our AI detection algorithms are continuously improved and currently achieve over 90% accuracy in identifying AI-generated content from major models. Higher tier plans use more sophisticated detection algorithms with improved accuracy rates and fewer false positives.",
    },
    {
      question: "What payment methods do you accept?",
      answer:
        "We accept all major credit cards (Visa, Mastercard, American Express, Discover), PayPal, and purchase orders from educational institutions. Enterprise customers can also pay by wire transfer or ACH.",
    },
    {
      question: "Can I get a refund if I'm not satisfied?",
      answer:
        "We offer a 14-day money-back guarantee for all paid plans. If you're not satisfied with our service within the first 14 days, contact our support team for a full refund. After 14 days, refunds are provided on a case-by-case basis.",
    },
    {
      question: "How does the API pricing work?",
      answer:
        "API plans are billed monthly based on the number of API requests. Each plan includes a set number of requests per month. Additional requests beyond your plan limit are billed at the overage rate. API usage is calculated separately from web application usage.",
    },
    {
      question: "What happens if I exceed my usage limits?",
      answer:
        "If you reach your monthly limit for AI detection queries or humanization tools, you'll be notified and given the option to purchase additional credits or upgrade your plan. We won't automatically charge you for overages without your consent.",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Frequently Asked Questions</h2>
        <p className="text-muted-foreground mt-2">Answers to common questions about our pricing and plans</p>
      </div>

      <Card>
        <CardContent className="pt-6">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
                <AccordionContent className="text-muted-foreground">{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    </div>
  )
}
