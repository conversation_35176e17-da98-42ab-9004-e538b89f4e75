"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AlertTriangle, CheckCircle, Download, Eye, FileText, MoreHorizontal, Search, Share2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

interface ReportItem {
  id: string
  title: string
  author: string
  course: string
  date: string
  similarityScore: number
  flagged: boolean
}

export function ReportsList() {
  const { toast } = useToast()
  const [reports, setReports] = useState<ReportItem[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredReports, setFilteredReports] = useState<ReportItem[]>([])
  const [statusFilter, setStatusFilter] = useState("all")

  useEffect(() => {
    try {
      const savedHistory = localStorage.getItem("academicIntegrityHistory")
      if (savedHistory) {
        const parsedHistory = JSON.parse(savedHistory)
        setReports(parsedHistory)
        setFilteredReports(parsedHistory)
      }
    } catch (err) {
      console.error("Error loading reports:", err)
      setReports([])
      setFilteredReports([])
    }
  }, [])

  useEffect(() => {
    let filtered = [...reports]

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(
        (item) =>
          item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.course.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    // Apply status filter
    if (statusFilter === "flagged") {
      filtered = filtered.filter((item) => item.flagged)
    } else if (statusFilter === "clean") {
      filtered = filtered.filter((item) => !item.flagged)
    }

    setFilteredReports(filtered)
  }, [searchTerm, statusFilter, reports])

  const handleDownload = (id: string) => {
    toast({
      title: "Download started",
      description: "Your report is being prepared for download.",
    })
  }

  const handleShare = (id: string) => {
    toast({
      title: "Share report",
      description: "Share link copied to clipboard.",
    })
  }

  if (reports.length === 0) {
    return (
      <Card className="flex flex-col items-center justify-center p-8">
        <FileText className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium mb-2">No reports available</h3>
        <p className="text-sm text-muted-foreground text-center mb-4">
          Your integrity reports will appear here after you submit text for analysis.
        </p>
        <Link href="#submissions">
          <Button>Run New Analysis</Button>
        </Link>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="relative w-full sm:max-w-xs">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search reports..."
            className="pl-9"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="flex gap-2">
          <Tabs value={statusFilter} onValueChange={setStatusFilter} className="w-full">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="flagged">Flagged</TabsTrigger>
              <TabsTrigger value="clean">Clean</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="grid gap-4">
        {filteredReports.length > 0 ? (
          filteredReports.map((report) => (
            <Card key={report.id} className="p-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    {report.flagged ? (
                      <div className="bg-amber-100 p-1 rounded-full">
                        <AlertTriangle className="h-5 w-5 text-amber-600" />
                      </div>
                    ) : (
                      <div className="bg-green-100 p-1 rounded-full">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                    )}
                  </div>

                  <div>
                    <Link href={`/resources/academic-integrity-monitor/reports/${report.id}`}>
                      <h3 className="font-medium hover:text-primary">{report.title}</h3>
                    </Link>
                    <div className="text-sm text-muted-foreground">
                      <span>{report.author}</span>
                      <span className="mx-2">•</span>
                      <span>{report.course}</span>
                      <span className="mx-2">•</span>
                      <span>{new Date(report.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge
                    variant={
                      report.similarityScore > 50 ? "destructive" : report.similarityScore > 20 ? "outline" : "default"
                    }
                  >
                    {report.similarityScore}% Similarity
                  </Badge>

                  <div className="flex items-center">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/resources/academic-integrity-monitor/reports/${report.id}`}>
                        <Eye className="h-4 w-4 mr-1" />
                        <span className="hidden sm:inline">View</span>
                      </Link>
                    </Button>

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleDownload(report.id)}>
                          <Download className="h-4 w-4 mr-2" />
                          Download PDF
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleShare(report.id)}>
                          <Share2 className="h-4 w-4 mr-2" />
                          Share Report
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          <Card className="p-6 text-center">
            <h3 className="font-medium mb-2">No matching reports found</h3>
            <p className="text-sm text-muted-foreground">
              Try adjusting your search or filters to find what you're looking for.
            </p>
          </Card>
        )}
      </div>
    </div>
  )
}
