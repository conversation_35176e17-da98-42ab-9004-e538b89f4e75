import { Graduation<PERSON>ap, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"

export function AudienceCards() {
  const audiences = [
    {
      title: "For Educators",
      description:
        "Verify student work quickly and accurately. Identify AI-generated content and promote academic integrity.",
      icon: <GraduationCap className="h-8 w-8 text-primary" />,
      features: [
        "Detect AI-written assignments",
        "Batch process multiple submissions",
        "Generate detailed reports",
        "Support academic integrity policies",
      ],
      cta: "Learn More",
      link: "/for-educators",
    },
    {
      title: "For Students",
      description: "Improve your writing and ensure your work meets academic standards. Learn to use AI responsibly.",
      icon: <Users className="h-8 w-8 text-primary" />,
      features: [
        "Check if your work might be flagged as AI-generated",
        "Improve AI-assisted drafts to sound more natural",
        "Develop better writing habits",
        "Understand AI detection patterns",
      ],
      cta: "Learn More",
      link: "/for-students",
    },
    {
      title: "For Writers",
      description:
        "Ensure your content sounds authentic and human. Perfect for bloggers, journalists, and content creators.",
      icon: <Pencil className="h-8 w-8 text-primary" />,
      features: [
        "Humanize AI-generated drafts",
        "Improve vocabulary and style",
        "Ensure content passes AI detection",
        "Maintain a consistent voice",
      ],
      cta: "Learn More",
      link: "/for-writers",
    },
    {
      title: "For SEO & Marketing",
      description:
        "Create content that ranks well and passes AI detection. Ensure your AI-assisted content sounds natural.",
      icon: <BarChart className="h-8 w-8 text-primary" />,
      features: [
        "Avoid potential AI content penalties",
        "Humanize AI-generated marketing copy",
        "Batch process blog articles",
        "Maintain brand voice consistency",
      ],
      cta: "Learn More",
      link: "/for-marketers",
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {audiences.map((audience) => (
        <Card key={audience.title} className="flex flex-col h-full">
          <CardHeader>
            <div className="mb-4">{audience.icon}</div>
            <CardTitle>{audience.title}</CardTitle>
            <CardDescription>{audience.description}</CardDescription>
          </CardHeader>
          <CardContent className="flex-1">
            <ul className="space-y-2">
              {audience.features.map((feature) => (
                <li key={feature} className="flex items-start">
                  <div className="mr-2 mt-1 h-2 w-2 rounded-full bg-primary" />
                  <span className="text-sm">{feature}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter className="pt-0">
            <Link href={audience.link} className="w-full">
              <Button variant="outline" className="w-full">
                {audience.cta}
              </Button>
            </Link>
          </CardFooter>
        </Card>
      ))}
    </div>
  )
}
