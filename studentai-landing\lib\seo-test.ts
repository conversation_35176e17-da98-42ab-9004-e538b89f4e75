/**
 * Utility functions for testing SEO implementation
 * These would be used in a real application for testing purposes
 */

export function validateStructuredData(jsonLd: string): { valid: boolean; errors?: string[] } {
  try {
    const parsed = JSON.parse(jsonLd)

    // Basic validation
    if (!parsed["@context"] || !parsed["@type"]) {
      return {
        valid: false,
        errors: ["Missing required @context or @type properties"],
      }
    }

    // More specific validation could be added here based on the schema type

    return { valid: true }
  } catch (error) {
    return {
      valid: false,
      errors: [`Invalid JSON-LD: ${error instanceof Error ? error.message : String(error)}`],
    }
  }
}

export function validateOpenGraphTags(metaTags: Record<string, string>): { valid: boolean; errors?: string[] } {
  const requiredTags = ["og:title", "og:description", "og:type", "og:url"]
  const errors: string[] = []

  for (const tag of requiredTags) {
    if (!metaTags[tag]) {
      errors.push(`Missing required Open Graph tag: ${tag}`)
    }
  }

  if (errors.length > 0) {
    return { valid: false, errors }
  }

  return { valid: true }
}

export function testSocialMediaPreview(url: string): Promise<{ success: boolean; message: string }> {
  // In a real application, this would use a service like the Facebook Sharing Debugger API
  // or Twitter Card Validator to test how the page appears when shared
  return Promise.resolve({
    success: true,
    message: "Social media preview test completed. Check the results in the console.",
  })
}
