"use client";

import { useEffect } from "react";
import Script from "next/script";

export function ChatWidget() {
  useEffect(() => {
    // Configure chat widget
    window.ChatWidgetConfig = {
      webhook: {
        url: "https://n8n-main.uruqqo.easypanel.host/webhook/8eadb998-46cc-4ab2-991f-667792b310ff/chat",
        route: "general",
      },
      branding: {
        logo: "https://i.postimg.cc/j52qh9gt/Color-Version.png",
        name: "StudentAIDetector",
        welcomeText: "👋 Welcome to StudentAIDetector your AI writing checker!",
        responseTimeText: "Hit the button and let's chat!", // Changed to double quotes
      },
      style: {
        primaryColor: "#1D4ED8",
        secondaryColor: "#3B82F6",
        position: "right",
        backgroundColor: "#ffffff",
        fontColor: "#111827",
      },
    };
  }, []);

  return (
    <Script
      src="https://cdn.jsdelivr.net/gh/funtastic418/chat-widget@main/chat-widget.js"
      strategy="lazyOnload"
    />
  );
}

// Add TypeScript interface for the global window object
declare global {
  interface Window {
    ChatWidgetConfig: {
      webhook: {
        url: string;
        route: string;
      };
      branding: {
        logo: string;
        name: string;
        welcomeText: string;
        responseTimeText: string;
      };
      style: {
        primaryColor: string;
        secondaryColor: string;
        position: string;
        backgroundColor: string;
        fontColor: string;
      };
    };
  }
}
