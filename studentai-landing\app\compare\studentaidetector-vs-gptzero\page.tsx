import type { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { PricingComparison } from "@/components/comparison/pricing-comparison";
import { FeatureComparison } from "@/components/comparison/feature-comparison";
import { But<PERSON> } from "@/components/ui/button";
import {
  Shield,
  Scale,
  BarChart,
  Award,
  AlertTriangle,
  CreditCard,
} from "lucide-react";

export const metadata: Metadata = {
  title: "StudentAIDetector vs GPTZero | Better AI Detection Tool",
  description:
    "Compare StudentAIDetector with GPTZero and see why we offer better accuracy, more features, and better pricing for AI content detection and humanization.",
  keywords:
    "AI detection comparison, StudentAIDetector vs GPTZero, AI text checker comparison, AI detector accuracy, AI content detection pricing",
};

export default function ComparisonPage() {
  const advantages = [
    {
      icon: Shield,
      title: "Superior Detection Accuracy",
      description:
        "Our advanced algorithms detect AI-generated content with higher precision and fewer false positives than GPTZero.",
    },
    {
      icon: Award,
      title: "AI Humanizer Feature",
      description:
        "Unique to StudentAIDetector, our AI humanizer transforms AI-generated text to bypass other detection tools while maintaining content integrity.",
    },
    {
      icon: Scale,
      title: "Better Value & Pricing",
      description:
        "Get more words per dollar with our straightforward pricing structure - up to 2x the content analysis compared to GPTZero's plans.",
    },
    {
      icon: BarChart,
      title: "Detailed Analytics",
      description:
        "Gain comprehensive insights with our detailed scoring system that explains exactly why text appears AI-generated.",
    },
    {
      icon: AlertTriangle,
      title: "Lower False Positive Rate",
      description:
        "Our algorithms are specifically designed to minimize false positives, ensuring human-written content isn't incorrectly flagged.",
    },
    {
      icon: CreditCard,
      title: "Simple Word-Based Pricing",
      description:
        "No complicated credit systems or hidden fees. Pay only for the words you need with transparent monthly or annual billing.",
    },
  ];

  return (
    <>
      <Navbar />
      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-b from-blue-50 to-white dark:from-gray-900 dark:to-gray-950 pt-32 pb-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
                StudentAIDetector vs GPTZero: <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
                  A Clear Comparison
                </span>
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-10 max-w-3xl mx-auto">
                See why educators, students, and content creators choose
                StudentAIDetector for more accurate AI detection and better
                value.
              </p>
              <div className="flex flex-wrap gap-4 justify-center">
                <Link href="#pricing-comparison">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                    Compare Pricing
                  </Button>
                </Link>
                <Link href="#feature-comparison">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950"
                  >
                    Compare Features
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Why StudentAIDetector is Better */}
        <section className="py-16 bg-white dark:bg-gray-950">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
                  Why Choose StudentAIDetector Over GPTZero
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  Our AI detection tools offer significant advantages that set
                  us apart from competitors.
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {advantages.map((advantage, i) => (
                  <div
                    key={i}
                    className="bg-gray-50 dark:bg-gray-900 p-6 rounded-xl border border-gray-100 dark:border-gray-800 hover:shadow-md transition-all"
                  >
                    <div className="flex items-center mb-4">
                      <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/50 mr-3">
                        <advantage.icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {advantage.title}
                      </h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">
                      {advantage.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Feature Comparison */}
        <section
          id="feature-comparison"
          className="py-16 bg-gray-50 dark:bg-gray-900"
        >
          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
                  Feature Comparison
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  See how StudentAIDetector stacks up against GPTZero across key
                  features.
                </p>
              </div>

              <FeatureComparison />
            </div>
          </div>
        </section>

        {/* Pricing Comparison */}
        <section
          id="pricing-comparison"
          className="py-16 bg-white dark:bg-gray-950"
        >
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
                  Pricing Comparison
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-lg max-w-3xl mx-auto">
                  StudentAIDetector offers better value with more words per
                  dollar and the option to add our unique AI Humanizer feature.
                </p>
              </div>

              <PricingComparison />

              <div className="mt-12 text-center">
                <Link href="/pricing">
                  <Button
                    size="lg"
                    className="bg-blue-600 hover:bg-blue-700 px-8"
                  >
                    View Our Full Pricing
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-6">
                Experience the Superior AI Detection Tool
              </h2>
              <p className="text-xl mb-8 opacity-90">
                Join thousands of educators and students who trust
                StudentAIDetector for accurate AI content analysis.
              </p>
              <div className="flex flex-wrap gap-4 justify-center">
                <Link href="/tools/ai-detector">
                  <Button
                    size="lg"
                    className="bg-white text-blue-600 hover:bg-gray-100"
                  >
                    Try For Free
                  </Button>
                </Link>
                <Link href="https://app.studentaidetector.com/signup">
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-white text-white hover:bg-blue-700"
                  >
                    Sign Up Now
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
