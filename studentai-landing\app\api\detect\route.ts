import { NextResponse } from "next/server";
import { headers } from "next/headers";

// Simple in-memory store for rate limiting (would use Redis/DB in production)
const ipRequests = new Map<string, { count: number; timestamp: number }>();

// Rate limit config
const RATE_LIMIT = 2; // requests per day
const RATE_WINDOW = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export async function POST(request: Request) {
  try {
    // Get client IP
    const headersList = headers();
    const forwardedFor = headersList.get("x-forwarded-for") || "";
    const ip = forwardedFor.split(",")[0] || "unknown-ip";

    // Check rate limit
    const now = Date.now();
    const ipData = ipRequests.get(ip) || { count: 0, timestamp: now };

    // Clear count if 24 hours have passed
    if (now - ipData.timestamp > RATE_WINDOW) {
      ipData.count = 0;
      ipData.timestamp = now;
    }

    // Check if rate limit is reached
    if (ipData.count >= RATE_LIMIT) {
      return NextResponse.json(
        {
          error: "Rate limit exceeded",
          message: "You've reached the limit of 2 free analyses per day",
          remainingTime: RATE_WINDOW - (now - ipData.timestamp),
          requiresLogin: true,
        },
        { status: 429 }
      );
    }

    // Extract text from request
    const { text } = await request.json();
    if (!text || text.length < 100) {
      return NextResponse.json(
        { error: "Text must be at least 100 characters" },
        { status: 400 }
      );
    }

    // Call the external AI detection API
    try {
      const apiResponse = await fetch(
        "https://student-ai-detector.uc.r.appspot.com/api/v1/detect",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ text }),
        }
      );

      if (!apiResponse.ok) {
        throw new Error(`External API error: ${apiResponse.status}`);
      }

      const apiData = await apiResponse.json();

      // Increment request count for this IP
      ipData.count++;
      ipRequests.set(ip, ipData);

      // Map the external API response to our expected format
      const result = {
        score: Math.round(apiData.ai_probability * 100),
        isAi: apiData.ai_probability > 0.5,
        patterns: apiData.insights?.slice(0, 2) || [], // Only provide limited insights in free tier
        fullInsights: apiData.insights || [],
        confidence: apiData.model_confidence > 0.65 ? "high" : "medium",
        remainingCredits: RATE_LIMIT - ipData.count,
        likelyModel: apiData.likely_ai_model,
        isPremiumResult: false, // Flag indicating this is a free tier result
      };

      return NextResponse.json({ result });
    } catch (error) {
      console.error("Error from external API:", error);
      throw new Error("Failed to analyze with external service");
    }
  } catch (error) {
    console.error("Error processing AI detection:", error);
    return NextResponse.json(
      { error: "Failed to process text" },
      { status: 500 }
    );
  }
}
