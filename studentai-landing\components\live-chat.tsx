"use client";

import { useState, useEffect, useRef } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Send,
  HelpCircle,
  Minimize2,
  MessageSquare,
  Trash2,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PersonalizedGreeting } from "@/components/personalized-greeting";
import { HelpArticles } from "@/components/help-articles";
import { Badge } from "@/components/ui/badge";

interface LiveChatProps {
  groqApiKey?: string;
}

interface Message {
  role: string;
  content: string;
  timestamp: Date;
  read: boolean;
}

// Storage key for chat history
const CHAT_HISTORY_KEY = "studentAiDetector_chatHistory";
const MAX_STORED_MESSAGES = 50; // Limit the number of messages to store

// Available models on Groq (in order of preference)
// Using the exact model name format provided by the user with updated fallbacks
const AVAILABLE_MODELS = [
  "meta-llama/llama-4-maverick-17b-128e-instruct", // Primary model as specified by user
  "deepseek-r1-distill-llama-70b", // First fallback as requested
  "gemma2-9b-it", // Second fallback as requested
];

// Predefined responses for when API is unavailable
const fallbackResponses = [
  "I'm here to help you with AI detection and content humanization. What would you like to know?",
  "Our AI detector can identify content from various AI models including GPT-4, Claude, and Bard.",
  "The humanization tools can help make AI-generated content appear more natural and personalized.",
  "You can check our pricing page for subscription options and credit packages.",
  "For academic integrity, we offer special tools designed for educators to verify student submissions.",
  "Our detection accuracy is over 95% for most modern AI-generated content.",
  "You can upload documents or paste text directly for analysis.",
  "Results are typically available within seconds for most text submissions.",
];

export function LiveChat({ groqApiKey }: LiveChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [hasSeenProactiveMessage, setHasSeenProactiveMessage] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [userName, setUserName] = useState("User");
  const [userEmail, setUserEmail] = useState("");
  const [isOnline, setIsOnline] = useState(true);
  const [needsUserInfo, setNeedsUserInfo] = useState(true);
  const [isMinimized, setIsMinimized] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [currentModelIndex, setCurrentModelIndex] = useState(0);
  const [showClearButton, setShowClearButton] = useState(false);

  // Load chat history from localStorage on initial render
  useEffect(() => {
    try {
      const storedUser = localStorage.getItem("user");
      if (storedUser) {
        const userData = JSON.parse(storedUser);
        setUserName(userData.name || "User");
        setUserEmail(userData.email || "");
        setNeedsUserInfo(false);

        // Load chat history if user exists
        const storedHistory = localStorage.getItem(CHAT_HISTORY_KEY);
        if (storedHistory) {
          const parsedHistory = JSON.parse(storedHistory);
          // Convert string timestamps back to Date objects
          const restoredMessages = parsedHistory.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
            read: true, // Mark all as read on load
          }));

          if (restoredMessages.length > 0) {
            setMessages(restoredMessages);
            setHasSeenProactiveMessage(true);
            setShowClearButton(true);
            return; // Skip initial greeting if we have history
          }
        }
      }

      // If no user or no history, show initial greeting
      if (!hasSeenProactiveMessage) {
        const initialGreeting = {
          role: "assistant",
          content:
            "Hi there! 👋 Welcome to StudentAIDetector. To personalize your experience, could you please share your name and email address?",
          timestamp: new Date(),
          read: false,
        };
        setMessages([initialGreeting]);
        setHasSeenProactiveMessage(true);
        setUnreadCount((prev) => prev + 1);
      }
    } catch (error) {
      console.error("Error loading chat history:", error);
      // If there's an error loading history, show the initial greeting
      if (!hasSeenProactiveMessage) {
        const initialGreeting = {
          role: "assistant",
          content:
            "Hi there! 👋 Welcome to StudentAIDetector. To personalize your experience, could you please share your name and email address?",
          timestamp: new Date(),
          read: false,
        };
        setMessages([initialGreeting]);
        setHasSeenProactiveMessage(true);
        setUnreadCount((prev) => prev + 1);
      }
    }
  }, [hasSeenProactiveMessage]);

  // Save chat history to localStorage whenever messages change
  useEffect(() => {
    if (messages.length > 0) {
      try {
        // Limit the number of messages to store to prevent localStorage overflow
        const messagesToStore = messages.slice(-MAX_STORED_MESSAGES);
        localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(messagesToStore));
        setShowClearButton(true);
      } catch (error) {
        console.error("Error saving chat history:", error);
      }
    }
  }, [messages]);

  useEffect(() => {
    // Simulate user presence (in a real app, use a WebSocket or similar)
    const presenceInterval = setInterval(() => {
      setIsOnline(Math.random() > 0.3); // Simulate online/offline status
    }, 5000);

    return () => clearInterval(presenceInterval);
  }, []);

  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Mark messages as read when chat is maximized
  useEffect(() => {
    if (!isMinimized && unreadCount > 0) {
      setMessages((prevMessages) =>
        prevMessages.map((msg) => ({ ...msg, read: true }))
      );
      setUnreadCount(0);
    }
  }, [isMinimized, unreadCount]);

  // Get a random fallback response
  const getFallbackResponse = () => {
    const randomIndex = Math.floor(Math.random() * fallbackResponses.length);
    return fallbackResponses[randomIndex];
  };

  // Clear chat history
  const clearChatHistory = () => {
    if (window.confirm("Are you sure you want to clear your chat history?")) {
      setMessages([]);
      localStorage.removeItem(CHAT_HISTORY_KEY);
      setShowClearButton(false);

      // Add a new greeting message
      const newGreeting = {
        role: "assistant",
        content: "Chat history cleared. How can I help you today?",
        timestamp: new Date(),
        read: true,
      };
      setMessages([newGreeting]);
    }
  };

  // Try to call the API with different models if one fails
  const tryApiCall = async (
    userMessage: string,
    modelIndex = 0
  ): Promise<string> => {
    if (modelIndex >= AVAILABLE_MODELS.length) {
      throw new Error("All models failed");
    }

    const model = AVAILABLE_MODELS[modelIndex];

    try {
      console.log(`Attempting API call with model: ${model}`);

      // Simplified message format for API call
      const messagesToSend = [
        {
          role: "system",
          content: `You are a helpful assistant for StudentAIDetector, a tool that helps detect AI-generated content and humanize text. You are trained on the content of the StudentAI Detector website and should provide highly specific and accurate responses related to the services offered, the methodologies used, and the specific features of the AI detection tool. Avoid overgeneralized responses and draw exclusively from the information available on the Student AI Detector website. Be clear, concise, and directly relevant to the user's inquiries.`,
        },
        { role: "user", content: userMessage },
      ];

      // API call to Groq with current model
      const response = await fetch(
        "https://api.groq.com/openai/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${groqApiKey}`,
          },
          body: JSON.stringify({
            model: model,
            messages: messagesToSend,
            max_tokens: 800,
            temperature: 0.7,
          }),
        }
      );

      // Get the full error message if available
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error(`API Error with model ${model}:`, errorData);

        // If model not found, try the next model
        if (
          errorData.error?.code === "model_not_found" ||
          errorData.error?.message?.includes("does not exist") ||
          errorData.error?.message?.includes("do not have access")
        ) {
          console.log(
            `Model ${model} not found or not accessible, trying next model...`
          );
          return tryApiCall(userMessage, modelIndex + 1);
        }

        throw new Error(
          `API responded with status: ${response.status}${
            errorData.error ? ` - ${errorData.error.message}` : ""
          }`
        );
      }

      const data = await response.json();

      // If successful, update the current model index for future calls
      setCurrentModelIndex(modelIndex);
      console.log(`Successfully used model: ${model}`);

      // Return the response content
      if (data.choices && data.choices.length > 0 && data.choices[0].message) {
        return data.choices[0].message.content;
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      // If this is a model-specific error, try the next model
      if (
        error instanceof Error &&
        (error.message.includes("model") ||
          error.message.includes("not found") ||
          error.message.includes("access"))
      ) {
        return tryApiCall(userMessage, modelIndex + 1);
      }
      throw error;
    }
  };

  const sendMessage = async () => {
    if (!input.trim()) return;

    if (needsUserInfo) {
      // Assume the user is providing name and email
      const parts = input.split(",");
      if (parts.length === 2) {
        const name = parts[0].trim();
        const email = parts[1].trim();

        setUserName(name);
        setUserEmail(email);

        // Store user info in local storage
        const newUser = { name: name, email: email, plan: "free", credits: 10 };
        localStorage.setItem("user", JSON.stringify(newUser));
        setNeedsUserInfo(false);

        const confirmationMessage = {
          role: "assistant",
          content: `Thanks, ${name}! How can I help you today?`,
          timestamp: new Date(),
          read: !isMinimized,
        };

        setMessages((prev) => [
          ...prev,
          { role: "user", content: input, timestamp: new Date(), read: true },
          confirmationMessage,
        ]);

        if (isMinimized) {
          setUnreadCount((prev) => prev + 1);
        }

        setInput("");
        return;
      } else {
        alert(
          "Please provide your name and email separated by a comma (e.g., John Doe, <EMAIL>)."
        );
        return;
      }
    }

    const userInput = input;
    const newMessage = {
      role: "user",
      content: userInput,
      timestamp: new Date(),
      read: true,
    };
    setMessages((prev) => [...prev, newMessage]);
    setInput("");
    setIsLoading(true);

    // Check if we have a valid API key
    if (!groqApiKey || !isOnline) {
      // If no API key or offline, use fallback response
      setTimeout(() => {
        const fallbackMessage = {
          role: "assistant",
          content: getFallbackResponse(),
          timestamp: new Date(),
          read: !isMinimized,
        };
        setMessages((prev) => [...prev, fallbackMessage]);

        if (isMinimized) {
          setUnreadCount((prev) => prev + 1);
        }

        setIsLoading(false);
      }, 1000); // Simulate API delay
      return;
    }

    try {
      // Try to get a response using our model fallback system
      const responseContent = await tryApiCall(userInput, currentModelIndex);

      const assistantMessage = {
        role: "assistant",
        content: responseContent,
        timestamp: new Date(),
        read: !isMinimized,
      };

      setMessages((prev) => [...prev, assistantMessage]);

      if (isMinimized) {
        setUnreadCount((prev) => prev + 1);
      }
    } catch (error) {
      console.error("All API attempts failed:", error);

      // Use fallback response instead of error message
      const fallbackMessage = {
        role: "assistant",
        content: getFallbackResponse(),
        timestamp: new Date(),
        read: !isMinimized,
      };
      setMessages((prev) => [...prev, fallbackMessage]);

      if (isMinimized) {
        setUnreadCount((prev) => prev + 1);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const helpArticles = [
    {
      id: "1",
      title: "How to Detect AI Content",
      description: "Learn how to use our AI detection tool effectively.",
      link: "/blog/how-to-tell-if-student-used-chatgpt",
    },
    {
      id: "2",
      title: "Improving Writing Quality",
      description: "Tips for enhancing your writing style and vocabulary.",
      link: "/blog/make-ai-content-sound-human",
    },
  ];

  // Render the minimized chat bubble
  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={toggleMinimize}
          className="bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:bg-primary/90 transition-all duration-200 relative"
          aria-label="Open chat"
        >
          <MessageSquare className="h-6 w-6" />
          {unreadCount > 0 && (
            <Badge
              className="absolute -top-2 -right-2 bg-red-500 text-white"
              variant="destructive"
            >
              {unreadCount}
            </Badge>
          )}
        </button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white rounded-lg shadow-lg overflow-hidden flex flex-col z-50 border border-gray-200">
      <div className="bg-primary text-primary-foreground p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/abstract-ai-icon.png" alt="AI Assistant" />
            <AvatarFallback>AI</AvatarFallback>
          </Avatar>
          <h3 className="text-lg font-medium">AI Assistant</h3>
          {isOnline ? (
            <span className="text-xs text-green-400">(Online)</span>
          ) : (
            <span className="text-xs text-red-400">(Offline)</span>
          )}
        </div>
        <div className="flex items-center space-x-1">
          {showClearButton && (
            <Button
              variant="ghost"
              size="icon"
              onClick={clearChatHistory}
              aria-label="Clear chat history"
              title="Clear chat history"
            >
              <Trash2 className="h-5 w-5" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMinimize}
            aria-label="Minimize chat"
          >
            <Minimize2 className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon">
            <HelpCircle className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1 p-4 max-h-[400px]" ref={chatContainerRef}>
        <div className="space-y-4">
          <PersonalizedGreeting userName={userName} />
          {messages.map((message, index) => (
            <div
              key={index}
              className={`flex items-start gap-2 ${
                message.role === "user" ? "justify-end" : ""
              }`}
            >
              {message.role === "assistant" && (
                <Avatar className="h-6 w-6">
                  <AvatarImage src="/friendly-ai-chat.png" alt="AI Assistant" />
                  <AvatarFallback>AI</AvatarFallback>
                </Avatar>
              )}
              <div
                className={`inline-block p-2 rounded-md ${
                  message.role === "user"
                    ? "bg-blue-100 text-blue-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {message.content}
                <div className="text-xs text-gray-500 mt-1">
                  {message.timestamp.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </div>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="flex items-start gap-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src="/friendly-ai-chat.png" alt="AI Assistant" />
                <AvatarFallback>AI</AvatarFallback>
              </Avatar>
              <div className="inline-block p-2 rounded-md bg-gray-100 text-gray-800">
                <div className="flex space-x-1">
                  <div
                    className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0ms" }}
                  ></div>
                  <div
                    className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "300ms" }}
                  ></div>
                  <div
                    className="h-2 w-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "600ms" }}
                  ></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      <div className="p-4 border-t">
        <div className="flex items-center space-x-2">
          <Input
            type="text"
            placeholder={
              needsUserInfo
                ? "Enter your name and email (e.g., John Doe, <EMAIL>)"
                : "Type your message..."
            }
            className="flex-1 rounded-md px-3 py-2 focus:outline-none shadow-sm"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                sendMessage();
              }
            }}
            disabled={isLoading}
          />
          <Button
            onClick={sendMessage}
            className="bg-blue-500 text-white rounded-md px-4 py-2 hover:bg-blue-600 flex-shrink-0"
            disabled={!input.trim() || isLoading}
          >
            {isLoading ? (
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
            ) : (
              <Send className="h-4 w-4 mr-2" />
            )}
            Send
          </Button>
        </div>
        <HelpArticles articles={helpArticles} />
      </div>
    </div>
  );
}
