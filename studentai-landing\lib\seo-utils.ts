import type { Metadata } from "next"

export const defaultMetadata = {
  title: "StudentAIDetector - AI Text Detection Tool",
  description:
    "Detect AI-generated content in student submissions with our advanced AI detection tool. Preserve academic integrity with StudentAIDetector.",
  keywords: [
    "AI detector",
    "AI content detector",
    "ChatGPT detector",
    "AI text checker",
    "AI humanizer",
    "humanize AI text",
    "academic integrity tool",
    "AI writing detector",
    "bypass AI detection",
    "AI text humanizer",
  ],
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || "https://studentaidetector.com",
  siteName: "StudentAIDetector",
}

interface GenerateMetaTagsProps {
  title?: string
  description?: string
  keywords?: string[]
  canonical?: string
  ogType?: string
  ogImage?: string
  ogImageAlt?: string
}

export function generateMetaTags({
  title = defaultMetadata.title,
  description = defaultMetadata.description,
  keywords = defaultMetadata.keywords,
  canonical,
  ogType = "website",
  ogImage = "/og-image.jpg",
  ogImageAlt = "StudentAIDetector logo and interface preview",
}: GenerateMetaTagsProps): Metadata {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://studentaidetector.com"
  const fullUrl = canonical ? `${siteUrl}${canonical}` : siteUrl
  const fullImageUrl = ogImage.startsWith("http") ? ogImage : `${siteUrl}${ogImage}`

  return {
    title: title,
    description: description,
    keywords: keywords,
    metadataBase: new URL(siteUrl),
    alternates: {
      canonical: fullUrl,
    },
    openGraph: {
      title: title,
      description: description,
      url: fullUrl,
      siteName: defaultMetadata.siteName,
      locale: "en_US",
      type: ogType,
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: ogImageAlt,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: title,
      description: description,
      creator: "@studentaidetector",
      images: [fullImageUrl],
    },
  }
}

export function generateHomePageSchema() {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://studentaidetector.com"

  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": `${siteUrl}/#website`,
    url: siteUrl,
    name: "StudentAIDetector",
    description: "Detect AI-generated content in student submissions with our advanced AI detection tool.",
    potentialAction: [
      {
        "@type": "SearchAction",
        target: {
          "@type": "EntryPoint",
          urlTemplate: `${siteUrl}/search?q={search_term_string}`,
        },
        "query-input": "required name=search_term_string",
      },
    ],
    inLanguage: "en-US",
  }
}

export function generateFaqSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: [
      {
        "@type": "Question",
        name: "How accurate is StudentAIDetector?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Our AI detection algorithms are continuously improved and currently achieve over 90% accuracy in identifying AI-generated content from major models.",
        },
      },
      {
        "@type": "Question",
        name: "Can I use StudentAIDetector for free?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Yes, we offer a free plan that includes basic AI detection features and allows up to 3 analyses per month.",
        },
      },
      {
        "@type": "Question",
        name: "What file formats does StudentAIDetector support?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Our basic and pro plans support .txt, .pdf, .doc, and .docx file formats. The free plan supports text input only.",
        },
      },
      {
        "@type": "Question",
        name: "How does StudentAIDetector detect AI-generated content?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "StudentAIDetector uses a sophisticated multi-layered approach including pattern recognition algorithms, linguistic analysis, contextual evaluation, and comparative benchmarking against known AI models.",
        },
      },
    ],
  }
}

export function generateToolSchema(name: string, description: string, url: string) {
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: name,
    description: description,
    url: url,
    applicationCategory: "EducationalApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      availability: "https://schema.org/Online",
    },
  }
}
