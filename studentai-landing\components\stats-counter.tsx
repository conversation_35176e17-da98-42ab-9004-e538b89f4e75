"use client"

import { useEffect, useState, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"

const stats = [
  { label: "Educational Institutions", value: 500, suffix: "+" },
  { label: "AI Detections Per Day", value: 25000, suffix: "+" },
  { label: "Accuracy Rate", value: 99.4, suffix: "%" },
  { label: "Hours Saved Weekly", value: 5, suffix: "hrs/educator" },
]

export default function StatsCounter() {
  const [isVisible, setIsVisible] = useState(false)
  const [counts, setCounts] = useState(stats.map(() => 0))
  const sectionRef = useRef(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 },
    )

    if (sectionRef.current) {
      observer.observe(sectionRef.current)
    }

    return () => {
      if (sectionRef.current) {
        observer.disconnect()
      }
    }
  }, [])

  useEffect(() => {
    if (!isVisible) return

    const duration = 2000 // ms
    const frameDuration = 1000 / 60 // 60fps
    const totalFrames = Math.round(duration / frameDuration)

    let frame = 0
    const countUp = setInterval(() => {
      frame++
      const progress = frame / totalFrames
      const easedProgress = easeOutQuad(progress)

      setCounts(
        stats.map((stat, index) => {
          return Math.floor(easedProgress * stat.value)
        }),
      )

      if (frame === totalFrames) {
        clearInterval(countUp)
        setCounts(stats.map((stat) => stat.value))
      }
    }, frameDuration)

    return () => clearInterval(countUp)
  }, [isVisible])

  // Easing function for smoother animation
  const easeOutQuad = (x) => {
    return 1 - (1 - x) * (1 - x)
  }

  return (
    <section ref={sectionRef} className="w-full py-12 bg-blue-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
          {stats.map((stat, i) => (
            <Card key={i} className="border-0 shadow-sm">
              <CardContent className="p-6 text-center">
                <div className="text-3xl md:text-4xl font-bold text-blue-600">
                  {counts[i]}
                  {stat.suffix}
                </div>
                <p className="text-sm md:text-base text-gray-500 mt-2">{stat.label}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
