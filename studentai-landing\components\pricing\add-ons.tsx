import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { PlusCircle, Zap, FileText, Users, BookOpen } from "lucide-react"
import Link from "next/link"

export function AddOns() {
  const addOns = [
    {
      name: "Additional Detection Credits",
      description: "Expand your monthly AI detection capacity",
      icon: <Zap className="h-5 w-5" />,
      options: [
        { name: "50 Credits", price: 4.99 },
        { name: "200 Credits", price: 16.99 },
        { name: "500 Credits", price: 34.99 },
      ],
      cta: "Add Credits",
      ctaLink: "/account/add-credits",
    },
    {
      name: "Increased Word Limit",
      description: "Process longer documents in a single analysis",
      icon: <FileText className="h-5 w-5" />,
      options: [
        { name: "+5,000 words", price: 3.99 },
        { name: "+15,000 words", price: 9.99 },
        { name: "+50,000 words", price: 24.99 },
      ],
      cta: "Upgrade Limit",
      ctaLink: "/account/upgrade-limits",
    },
    {
      name: "Additional Users",
      description: "Add more users to your account",
      icon: <Users className="h-5 w-5" />,
      options: [
        { name: "+5 users", price: 9.99 },
        { name: "+15 users", price: 24.99 },
        { name: "+50 users", price: 69.99 },
      ],
      cta: "Add Users",
      ctaLink: "/account/add-users",
    },
    {
      name: "Extended History",
      description: "Keep your analysis history for longer",
      icon: <BookOpen className="h-5 w-5" />,
      options: [
        { name: "6 months", price: 4.99 },
        { name: "1 year", price: 8.99 },
        { name: "Unlimited", price: 19.99 },
      ],
      cta: "Extend History",
      ctaLink: "/account/extend-history",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold">Add-Ons & Upgrades</h2>
        <p className="text-muted-foreground mt-2">Customize your plan with additional features</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {addOns.map((addon) => (
          <Card key={addon.name} className="flex flex-col">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="p-2 rounded-full bg-primary/10 text-primary">{addon.icon}</div>
                <CardTitle className="text-lg">{addon.name}</CardTitle>
              </div>
              <CardDescription>{addon.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-1">
              <div className="space-y-4">
                {addon.options.map((option) => (
                  <div key={option.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <PlusCircle className="h-4 w-4 text-primary" />
                      <span>{option.name}</span>
                    </div>
                    <span className="font-medium">${option.price}/mo</span>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Link href={addon.ctaLink} className="w-full">
                <Button variant="outline" className="w-full">
                  {addon.cta}
                </Button>
              </Link>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Custom Integrations</CardTitle>
          <CardDescription>Connect StudentAIDetector with your existing systems</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="p-4 border rounded-lg">
                <p className="font-medium">LMS Integration</p>
                <p className="text-sm text-muted-foreground mt-1">Connect with Canvas, Blackboard, Moodle, and more</p>
                <p className="text-sm font-medium mt-2">Starting at $99/month</p>
              </div>
              <div className="p-4 border rounded-lg">
                <p className="font-medium">Plagiarism Tool Integration</p>
                <p className="text-sm text-muted-foreground mt-1">Connect with Turnitin, Copyscape, and others</p>
                <p className="text-sm font-medium mt-2">Starting at $149/month</p>
              </div>
              <div className="p-4 border rounded-lg">
                <p className="font-medium">Custom Integration</p>
                <p className="text-sm text-muted-foreground mt-1">Connect with your proprietary systems</p>
                <p className="text-sm font-medium mt-2">Custom pricing</p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Link href="/contact?subject=Custom%20Integration" className="w-full">
            <Button className="w-full">Contact for Custom Integration</Button>
          </Link>
        </CardFooter>
      </Card>
    </div>
  )
}
