import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@/components/ui/accordion"

export function HumanizationFaq() {
  const faqs = [
    {
      question: "How does the AI humanizer work?",
      answer:
        "Our AI humanizer uses advanced natural language processing to rewrite AI-generated text to sound more natural and human-like, while maintaining the original meaning. It adjusts sentence structure, vocabulary, and writing style to create more authentic content that better reflects human writing patterns.",
    },
    {
      question: "Will humanized text pass AI detection tools?",
      answer:
        "Our humanization tools are designed to reduce the likelihood of text being flagged by AI detectors. While we can't guarantee 100% undetectability (as detection tools are also constantly improving), our tools significantly reduce the AI patterns that most detectors look for, making the text much more likely to pass as human-written.",
    },
    {
      question: "Is using the humanizer tool ethical?",
      answer:
        "The humanizer tool is designed to help users improve their writing and develop a more natural style. We encourage ethical use, such as refining AI-assisted drafts to better match your voice or improving content quality. Users should always follow their institution's academic integrity policies and properly cite sources.",
    },
    {
      question: "What's the difference between the paraphraser and other humanization tools?",
      answer:
        "The paraphraser is our basic humanization tool that rewrites text while maintaining meaning. The Style Adjuster focuses specifically on modifying writing style (formal/informal, etc.). The Vocabulary Enhancer improves word choice and expression. The AI Section Fixer targets only the most AI-like sections for focused rewriting.",
    },
    {
      question: "How many words can I humanize at once?",
      answer:
        "The word limit depends on your plan. Free users can humanize up to 1,500 words per use (limited to 3 uses per month), Basic plan users up to 5,000 words (50 uses per month), and Pro plan users up to 15,000 words with unlimited uses.",
    },
    {
      question: "Will humanized text maintain the original meaning?",
      answer:
        "Yes, our humanization tools are designed to preserve the original meaning while changing the structure and style to sound more natural. However, with very technical or specialized content, we recommend reviewing the output to ensure accuracy.",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold tracking-tight mb-2">Frequently Asked Questions</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Find answers to common questions about our AI humanization tools
        </p>
      </div>

      <div className="max-w-3xl mx-auto">
        <Accordion type="single" collapsible className="w-full">
          {faqs.map((faq, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
              <AccordionContent>{faq.answer}</AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  )
}
