"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  Menu,
  ChevronDown,
  ChevronRight,
  FileText,
  Wand2,
  Layers,
  Code,
  GraduationCap,
  Users,
  Pencil,
  BarChart,
  ShieldCheck,
} from "lucide-react"
import { BrandLogo } from "@/components/brand-logo"

interface MobileNavProps {
  isLoggedIn: boolean
}

export function MobileNav({ isLoggedIn }: MobileNavProps) {
  const pathname = usePathname()
  const [open, setOpen] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    tools: false,
    audiences: false,
    resources: false,
  })

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }))
  }

  // Reset expanded sections when sheet closes
  useEffect(() => {
    if (!open) {
      setExpandedSections({
        tools: false,
        audiences: false,
        resources: false,
      })
    }
  }, [open])

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="flex flex-col overflow-y-auto">
        <div className="px-2 py-4">
          <BrandLogo size="sm" />
        </div>
        <nav className="flex flex-col gap-1 mt-4">
          {/* Tools Section */}
          <div>
            <button
              onClick={() => toggleSection("tools")}
              className={`flex items-center justify-between w-full px-2 py-2 text-base font-medium rounded-md transition-colors hover:bg-muted ${
                pathname.startsWith("/tools") ? "text-primary bg-muted" : "text-muted-foreground"
              }`}
            >
              <span>Tools</span>
              {expandedSections.tools ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>
            {expandedSections.tools && (
              <div className="ml-2 flex flex-col gap-1 mt-1">
                <div className="ml-2 mt-2 mb-1">
                  <span className="text-xs font-medium text-muted-foreground">Detection Tools</span>
                </div>
                <Link
                  href="/tools/ai-detector"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <FileText className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">AI Detector</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Identify AI-generated content</p>
                  </div>
                </Link>
                <Link
                  href="/tools/batch-processing"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <Layers className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">Batch Processing</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Analyze multiple documents</p>
                  </div>
                </Link>

                <div className="ml-2 mt-3 mb-1">
                  <span className="text-xs font-medium text-muted-foreground">Enhancement Tools</span>
                </div>
                <Link
                  href="/tools/humanization"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <Wand2 className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">Humanization Tools</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Make AI text sound natural</p>
                  </div>
                </Link>
                <Link
                  href="/tools/api"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <Code className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">API Access</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Integrate with your applications</p>
                  </div>
                </Link>
              </div>
            )}
          </div>

          {/* Audience Pages */}
          <div>
            <button
              onClick={() => toggleSection("audiences")}
              className={`flex items-center justify-between w-full px-2 py-2 text-base font-medium rounded-md transition-colors hover:bg-muted ${
                pathname.startsWith("/for-") ? "text-primary bg-muted" : "text-muted-foreground"
              }`}
            >
              <span>For Users</span>
              {expandedSections.audiences ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>
            {expandedSections.audiences && (
              <div className="ml-2 flex flex-col gap-1 mt-1">
                <div className="ml-2 mt-2 mb-1">
                  <span className="text-xs font-medium text-muted-foreground">Academic</span>
                </div>
                <Link
                  href="/for-educators"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <GraduationCap className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">For Educators</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Verify student work</p>
                  </div>
                </Link>
                <Link
                  href="/for-students"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <Users className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">For Students</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Improve your writing</p>
                  </div>
                </Link>

                <div className="ml-2 mt-3 mb-1">
                  <span className="text-xs font-medium text-muted-foreground">Professional</span>
                </div>
                <Link
                  href="/for-writers"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <Pencil className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">For Writers</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Ensure content authenticity</p>
                  </div>
                </Link>
                <Link
                  href="/for-marketers"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <BarChart className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">For SEO & Marketing</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Create content that ranks</p>
                  </div>
                </Link>
              </div>
            )}
          </div>

          {/* Resources Section */}
          <div>
            <button
              onClick={() => toggleSection("resources")}
              className={`flex items-center justify-between w-full px-2 py-2 text-base font-medium rounded-md transition-colors hover:bg-muted ${
                pathname === "/blog" ||
                pathname.startsWith("/blog/") ||
                pathname === "/resources/academic-integrity-monitor" ||
                pathname === "/roadmap"
                  ? "text-primary bg-muted"
                  : "text-muted-foreground"
              }`}
            >
              <span>Resources</span>
              {expandedSections.resources ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>
            {expandedSections.resources && (
              <div className="ml-2 flex flex-col gap-1 mt-1">
                <Link
                  href="/blog"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <div>
                    <span className="font-medium">Blog</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Articles and insights</p>
                  </div>
                </Link>
                <Link
                  href="/resources/academic-integrity-monitor"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <ShieldCheck className="h-4 w-4 text-primary mt-0.5" />
                  <div>
                    <span className="font-medium">Academic Integrity Monitor</span>
                    <p className="text-xs text-muted-foreground mt-0.5">
                      Monitor submissions for plagiarism and AI use
                    </p>
                  </div>
                </Link>
                <Link
                  href="/about"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <div>
                    <span className="font-medium">About Us</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Our mission and technology</p>
                  </div>
                </Link>
                <Link
                  href="/integrations"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <div>
                    <span className="font-medium">Integrations</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Connect with other tools</p>
                  </div>
                </Link>
                <Link
                  href="/roadmap"
                  className="flex items-start gap-2 ml-2 px-2 py-2 text-sm rounded-md transition-colors hover:bg-muted"
                  onClick={() => setOpen(false)}
                >
                  <div>
                    <span className="font-medium">Roadmap</span>
                    <p className="text-xs text-muted-foreground mt-0.5">Vote on upcoming features</p>
                  </div>
                </Link>
              </div>
            )}
          </div>

          {/* Other Main Links */}
          <Link
            href="/pricing"
            className={`px-2 py-2 text-base font-medium rounded-md transition-colors hover:bg-muted ${
              pathname === "/pricing" ? "text-primary bg-muted" : "text-muted-foreground"
            }`}
            onClick={() => setOpen(false)}
          >
            Pricing
          </Link>
          <Link
            href="/contact"
            className={`px-2 py-2 text-base font-medium rounded-md transition-colors hover:bg-muted ${
              pathname === "/contact" ? "text-primary bg-muted" : "text-muted-foreground"
            }`}
            onClick={() => setOpen(false)}
          >
            Contact
          </Link>
        </nav>
        <div className="mt-auto pt-4 border-t space-y-4">
          {!isLoggedIn ? (
            <>
              <Link href="/login" className="w-full" onClick={() => setOpen(false)}>
                <Button variant="outline" className="w-full">
                  Log in
                </Button>
              </Link>
              <Link href="/signup" className="w-full" onClick={() => setOpen(false)}>
                <Button className="w-full">Sign up</Button>
              </Link>
            </>
          ) : (
            <Link href="/account" className="w-full" onClick={() => setOpen(false)}>
              <Button variant="outline" className="w-full">
                My Account
              </Button>
            </Link>
          )}
        </div>
      </SheetContent>
    </Sheet>
  )
}
