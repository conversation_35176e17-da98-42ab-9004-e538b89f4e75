import type React from "react"
import { Breadcrumb } from "@/components/breadcrumb"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

interface ToolPageLayoutProps {
  children: React.ReactNode
  title: string
  description: string
  relatedTools?: Array<{
    name: string
    href: string
    description: string
  }>
  relatedResources?: Array<{
    name: string
    href: string
    type: "blog" | "guide" | "case-study"
  }>
}

export function ToolPageLayout({
  children,
  title,
  description,
  relatedTools = [],
  relatedResources = [],
}: ToolPageLayoutProps) {
  return (
    <main className="min-h-screen flex flex-col">
      <Navbar />

      <div className="container py-8 md:py-12">
        <Breadcrumb />

        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{title}</h1>
            <p className="text-xl text-muted-foreground">{description}</p>
          </div>

          {/* Main content */}
          <div className="mb-12">{children}</div>

          {/* Related content section */}
          <div className="border-t pt-8">
            <div className="grid gap-8 md:grid-cols-2">
              {/* Related tools */}
              {relatedTools.length > 0 && (
                <div>
                  <h2 className="text-xl font-bold mb-4">Related Tools</h2>
                  <ul className="space-y-4">
                    {relatedTools.map((tool) => (
                      <li key={tool.href} className="border rounded-md p-4">
                        <Link href={tool.href} className="group">
                          <h3 className="font-medium group-hover:text-primary transition-colors">{tool.name}</h3>
                          <p className="text-sm text-muted-foreground mb-2">{tool.description}</p>
                          <div className="flex items-center text-sm text-primary">
                            <span>Learn more</span>
                            <ArrowRight className="ml-1 h-3 w-3" />
                          </div>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Related resources */}
              {relatedResources.length > 0 && (
                <div>
                  <h2 className="text-xl font-bold mb-4">Related Resources</h2>
                  <ul className="space-y-4">
                    {relatedResources.map((resource) => (
                      <li key={resource.href} className="border rounded-md p-4">
                        <Link href={resource.href} className="group">
                          <div className="flex items-center mb-1">
                            <span className="text-xs px-2 py-0.5 bg-muted rounded-full mr-2">
                              {resource.type === "blog" ? "Blog" : resource.type === "guide" ? "Guide" : "Case Study"}
                            </span>
                            <h3 className="font-medium group-hover:text-primary transition-colors">{resource.name}</h3>
                          </div>
                          <div className="flex items-center text-sm text-primary">
                            <span>Read more</span>
                            <ArrowRight className="ml-1 h-3 w-3" />
                          </div>
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* CTA section */}
            <div className="mt-8 pt-8 border-t text-center">
              <h2 className="text-xl font-bold mb-2">Ready to get started?</h2>
              <p className="text-muted-foreground mb-4">Try our tools today or explore our pricing plans</p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg">Try For Free</Button>
                <Link href="/pricing">
                  <Button variant="outline" size="lg">
                    View Pricing
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </main>
  )
}
