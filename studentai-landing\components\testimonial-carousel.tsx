"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Quote } from "lucide-react"

const testimonials = [
  {
    quote:
      "This tool has completely transformed how I evaluate student work. I can now quickly identify AI-generated content and have meaningful conversations with students about academic integrity.",
    name: "Dr. <PERSON>",
    role: "English Professor, Stanford University",
    image: "/images/testimonials/jessica.jpg",
  },
  {
    quote:
      "As a department head, I needed a solution that all faculty could use easily. Student AI Detector has become an essential part of our assessment process, saving us countless hours.",
    name: "Prof. <PERSON>",
    role: "Department Chair, NYU",
    image: "/images/testimonials/michael.jpg",
  },
  {
    quote:
      "The detailed reports help me show students exactly where AI content was detected. It's become a valuable teaching tool for discussing originality and proper citation.",
    name: "<PERSON>, Ph.D.",
    role: "High School Principal",
    image: "/images/testimonials/sarah.jpg",
  },
]

export default function TestimonialCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const next = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length)
  }

  const previous = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length)
  }

  return (
    <section className="w-full py-12 md:py-24 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">Trusted by Educators Worldwide</h2>
            <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Hear from educators who have transformed their approach to academic integrity.
            </p>
          </div>
        </div>
        <div className="mx-auto max-w-4xl py-12">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-0">
              <div className="grid grid-cols-1 md:grid-cols-3">
                <div className="hidden md:block bg-blue-600 p-6 rounded-l-lg">
                  <div className="flex h-full items-center justify-center">
                    <div className="relative h-32 w-32 overflow-hidden rounded-full border-4 border-white">
                      <Image
                        src={testimonials[currentIndex].image || "/placeholder.svg"}
                        alt={testimonials[currentIndex].name}
                        fill
                        className="object-cover"
                      />
                    </div>
                  </div>
                </div>
                <div className="col-span-2 p-6 md:p-8">
                  <Quote className="h-8 w-8 text-blue-600 mb-4" />
                  <blockquote className="text-lg font-medium leading-relaxed mb-6">
                    "{testimonials[currentIndex].quote}"
                  </blockquote>
                  <div className="flex items-center">
                    <div className="md:hidden mr-4 relative h-12 w-12 overflow-hidden rounded-full border-2 border-blue-600">
                      <Image
                        src={testimonials[currentIndex].image || "/placeholder.svg"}
                        alt={testimonials[currentIndex].name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <div className="font-bold">{testimonials[currentIndex].name}</div>
                      <div className="text-sm text-gray-500">{testimonials[currentIndex].role}</div>
                    </div>
                  </div>
                  <div className="flex justify-end gap-2 mt-6">
                    <Button variant="outline" size="icon" onClick={previous} aria-label="Previous testimonial">
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" onClick={next} aria-label="Next testimonial">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
