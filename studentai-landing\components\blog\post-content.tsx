import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Share2 } from "lucide-react"
import type { BlogPost } from "@/lib/blog"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface PostContentProps {
  post: BlogPost
}

export function PostContent({ post }: PostContentProps) {
  // Calculate reading time (rough estimate: 200 words per minute)
  const wordCount = post.content.split(/\s+/).length
  const readingTime = Math.max(1, Math.ceil(wordCount / 200))

  return (
    <article className="prose prose-lg max-w-none dark:prose-invert">
      {/* Featured Image */}
      <div className="mb-8 rounded-lg overflow-hidden">
        <img
          src={post.coverImage || "/placeholder.svg?height=600&width=1200"}
          alt={post.title}
          className="w-full h-auto aspect-video object-cover"
        />
      </div>

      {/* Post Header */}
      <header className="mb-8 not-prose">
        <div className="flex flex-wrap gap-2 mb-4">
          <Link href={`/blog/category/${post.category.toLowerCase().replace(/ /g, "-")}`}>
            <Badge className="text-sm">{post.category}</Badge>
          </Link>
        </div>

        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">{post.title}</h1>

        <p className="text-xl text-muted-foreground mb-6">{post.excerpt}</p>

        <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
          <div className="flex items-center gap-2">
            {post.author.avatar ? (
              <img
                src={post.author.avatar || "/placeholder.svg"}
                alt={post.author.name}
                className="w-10 h-10 rounded-full object-cover"
              />
            ) : (
              <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                <span className="font-bold text-primary">{post.author.name.charAt(0)}</span>
              </div>
            )}
            <div>
              <div className="font-medium">{post.author.name}</div>
              {post.author.title && <div className="text-sm">{post.author.title}</div>}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            <time dateTime={post.date}>
              {new Date(post.date).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </time>
          </div>

          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span>{readingTime} min read</span>
          </div>
        </div>
      </header>

      {/* Post Content */}
      <div className="mb-8">
        {post.content.split("\n\n").map((paragraph, index) => (
          <p key={index}>{paragraph}</p>
        ))}
      </div>

      {/* Tags */}
      <div className="not-prose mt-8 mb-6">
        <h3 className="text-lg font-medium mb-3">Tags:</h3>
        <div className="flex flex-wrap gap-2">
          {post.tags.map((tag) => (
            <Link href={`/blog/tag/${tag.replace(/ /g, "-")}`} key={tag}>
              <Badge variant="secondary" className="hover:bg-secondary/80 cursor-pointer">
                {tag}
              </Badge>
            </Link>
          ))}
        </div>
      </div>

      {/* Share */}
      <Card className="not-prose mt-8">
        <CardContent className="flex flex-col sm:flex-row justify-between items-center gap-4 py-6">
          <div className="text-center sm:text-left">
            <h3 className="font-medium text-lg">Share this article</h3>
            <p className="text-muted-foreground">Help others discover this content</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5"
              >
                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
              </svg>
              <span className="sr-only">Share on Facebook</span>
            </Button>
            <Button variant="outline" size="icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5"
              >
                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
              </svg>
              <span className="sr-only">Share on Twitter</span>
            </Button>
            <Button variant="outline" size="icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-5 w-5"
              >
                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                <rect width="4" height="12" x="2" y="9"></rect>
                <circle cx="4" cy="4" r="2"></circle>
              </svg>
              <span className="sr-only">Share on LinkedIn</span>
            </Button>
            <Button variant="outline" size="icon">
              <Share2 className="h-5 w-5" />
              <span className="sr-only">Copy link</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Author Bio */}
      {post.author.bio && (
        <div className="not-prose mt-12 p-6 bg-muted rounded-lg">
          <div className="flex items-start gap-4">
            {post.author.avatar ? (
              <img
                src={post.author.avatar || "/placeholder.svg"}
                alt={post.author.name}
                className="w-16 h-16 rounded-full object-cover"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center">
                <span className="font-bold text-primary text-xl">{post.author.name.charAt(0)}</span>
              </div>
            )}
            <div>
              <h3 className="text-xl font-bold mb-2">About {post.author.name}</h3>
              <p className="text-muted-foreground mb-4">{post.author.bio}</p>
              {post.author.socialLinks && (
                <div className="flex gap-2">
                  {post.author.socialLinks.twitter && (
                    <Button variant="ghost" size="sm" asChild>
                      <a href={post.author.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                        Twitter
                      </a>
                    </Button>
                  )}
                  {post.author.socialLinks.linkedin && (
                    <Button variant="ghost" size="sm" asChild>
                      <a href={post.author.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                        LinkedIn
                      </a>
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </article>
  )
}
