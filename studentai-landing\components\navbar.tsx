"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ModeToggle } from "@/components/mode-toggle";
import { BrandLogo } from "@/components/brand-logo";
import { UserButton } from "@/components/user-button";
import { MobileNav } from "@/components/mobile-nav";
import { useState, useEffect } from "react";
import {
  FileText,
  Wand2,
  Layers,
  Code,
  GraduationCap,
  Users,
  Pencil,
  BarChart,
  ChevronDown,
  ArrowRight,
  Book,
  Lightbulb,
  Info,
} from "lucide-react";

export function Navbar() {
  const pathname = usePathname();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [megaMenuOpen, setMegaMenuOpen] = useState<string | null>(null);
  const [scrolled, setScrolled] = useState(false);

  // Check if user is logged in
  useEffect(() => {
    const user = localStorage.getItem("user");
    setIsLoggedIn(!!user);
  }, []);

  // Track scroll for navbar style changes
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleMegaMenuToggle = (menu: string) => {
    if (megaMenuOpen === menu) {
      setMegaMenuOpen(null);
    } else {
      setMegaMenuOpen(menu);
    }
  };

  const closeMegaMenu = () => {
    setMegaMenuOpen(null);
  };

  // Close mega menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (
        !target.closest(".mega-menu-container") &&
        !target.closest(".mega-menu-trigger")
      ) {
        setMegaMenuOpen(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Close mega menu when route changes
  useEffect(() => {
    setMegaMenuOpen(null);
  }, [pathname]);

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 w-full backdrop-blur-md transition-all duration-300 ${
        scrolled
          ? "bg-background/95 shadow-sm border-b border-gray-200 dark:border-gray-800"
          : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 md:px-6 flex h-16 items-center justify-between">
        <div className="flex items-center gap-2">
          <MobileNav isLoggedIn={isLoggedIn} />
          <Link href="/" className="hidden sm:flex items-center">
            <BrandLogo />
          </Link>
          <Link href="/" className="sm:hidden">
            <BrandLogo showText={false} />
          </Link>
        </div>

        <nav className="hidden md:flex items-center gap-8">
          {/* Tools Menu */}
          <div className="relative group">
            <button
              onClick={() => handleMegaMenuToggle("tools")}
              className={`mega-menu-trigger flex items-center gap-1.5 text-sm font-medium transition-colors group-hover:text-blue-600 dark:group-hover:text-blue-400 ${
                pathname.startsWith("/tools")
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300"
              }`}
              aria-expanded={megaMenuOpen === "tools"}
              aria-controls="tools-mega-menu"
            >
              Tools
              <ChevronDown
                className={`h-4 w-4 transition-transform duration-300 ${
                  megaMenuOpen === "tools" ? "rotate-180" : ""
                } group-hover:text-blue-600 dark:group-hover:text-blue-400`}
              />
            </button>

            {/* Animated underline for hover effect */}
            <span
              className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 transition-all duration-300 ${
                pathname.startsWith("/tools") ? "w-full" : "group-hover:w-full"
              }`}
            ></span>

            {/* Tools Mega Menu */}
            {megaMenuOpen === "tools" && (
              <div
                id="tools-mega-menu"
                className="mega-menu-container absolute left-1/2 transform -translate-x-1/2 top-full z-50 mt-2 w-[550px] rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 p-6 shadow-lg animate-in fade-in-50 slide-in-from-top-5 duration-300"
              >
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="mb-3 text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold">
                      Detection Tools
                    </h3>
                    <ul className="space-y-2">
                      <li>
                        <Link
                          href="/tools/ai-detector"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-blue-50 dark:bg-blue-900/20">
                            <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              AI Detector
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Identify AI-generated content with 99.4% accuracy
                            </p>
                          </div>
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/tools/batch-processing"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-indigo-50 dark:bg-indigo-900/20">
                            <Layers className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              Batch Processing
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Analyze multiple documents simultaneously
                            </p>
                          </div>
                        </Link>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="mb-3 text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold">
                      Enhancement Tools
                    </h3>
                    <ul className="space-y-2">
                      <li>
                        <Link
                          href="/tools/humanizer"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-purple-50 dark:bg-purple-900/20">
                            <Wand2 className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              AI Humanizer
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Transform AI-generated text to bypass detection
                            </p>
                          </div>
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/tools/api"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-amber-50 dark:bg-amber-900/20">
                            <Code className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              API Access
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Integrate our detection capabilities into your
                              apps
                            </p>
                          </div>
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
                <div className="mt-6 pt-4 border-t border-gray-100 dark:border-gray-800">
                  <Link
                    href="/tools"
                    className="inline-flex items-center text-sm text-blue-600 dark:text-blue-400 hover:underline"
                    onClick={closeMegaMenu}
                  >
                    View all tools
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* Solutions Menu */}
          <div className="relative group">
            <button
              onClick={() => handleMegaMenuToggle("audiences")}
              className={`mega-menu-trigger flex items-center gap-1.5 text-sm font-medium transition-colors group-hover:text-blue-600 dark:group-hover:text-blue-400 ${
                pathname.startsWith("/for-")
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300"
              }`}
              aria-expanded={megaMenuOpen === "audiences"}
              aria-controls="audiences-mega-menu"
            >
              Solutions
              <ChevronDown
                className={`h-4 w-4 transition-transform duration-300 ${
                  megaMenuOpen === "audiences" ? "rotate-180" : ""
                } group-hover:text-blue-600 dark:group-hover:text-blue-400`}
              />
            </button>

            {/* Animated underline for hover effect */}
            <span
              className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 transition-all duration-300 ${
                pathname.startsWith("/for-") ? "w-full" : "group-hover:w-full"
              }`}
            ></span>

            {/* Audiences Mega Menu */}
            {megaMenuOpen === "audiences" && (
              <div
                id="audiences-mega-menu"
                className="mega-menu-container absolute left-0 top-full z-50 mt-2 w-[550px] rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 p-6 shadow-lg animate-in fade-in-50 slide-in-from-top-5 duration-300"
              >
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="mb-3 text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold">
                      Academic
                    </h3>
                    <ul className="space-y-2">
                      <li>
                        <Link
                          href="/for-educators"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-blue-50 dark:bg-blue-900/20">
                            <GraduationCap className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              For Educators
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Tools to verify student work and maintain
                              integrity
                            </p>
                          </div>
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/for-students"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-indigo-50 dark:bg-indigo-900/20">
                            <Users className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              For Students
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Improve your writing and meet academic standards
                            </p>
                          </div>
                        </Link>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="mb-3 text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold">
                      Compare & Use Cases
                    </h3>
                    <ul className="space-y-2">
                      <li>
                        <Link
                          href="/compare"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-green-50 dark:bg-green-900/20">
                            <BarChart className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              Compare Tools
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              See how we stack up against competitors
                            </p>
                          </div>
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/use-cases"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-amber-50 dark:bg-amber-900/20">
                            <Lightbulb className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              Use Cases
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Explore how our tools help in different scenarios
                            </p>
                          </div>
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/for-marketers"
                          className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                          onClick={closeMegaMenu}
                        >
                          <div className="p-1.5 rounded-md bg-amber-50 dark:bg-amber-900/20">
                            <BarChart className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                          </div>
                          <div>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              For SEO & Marketing
                            </span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              Create content that ranks well and passes
                              detection
                            </p>
                          </div>
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Resources Menu */}
          <div className="relative group">
            <button
              onClick={() => handleMegaMenuToggle("resources")}
              className={`mega-menu-trigger flex items-center gap-1.5 text-sm font-medium transition-colors group-hover:text-blue-600 dark:group-hover:text-blue-400 ${
                pathname === "/blog" ||
                pathname.startsWith("/blog/") ||
                pathname.startsWith("/resources")
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300"
              }`}
              aria-expanded={megaMenuOpen === "resources"}
              aria-controls="resources-mega-menu"
            >
              Resources
              <ChevronDown
                className={`h-4 w-4 transition-transform duration-300 ${
                  megaMenuOpen === "resources" ? "rotate-180" : ""
                } group-hover:text-blue-600 dark:group-hover:text-blue-400`}
              />
            </button>

            {/* Animated underline for hover effect */}
            <span
              className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 transition-all duration-300 ${
                pathname === "/blog" ||
                pathname.startsWith("/blog/") ||
                pathname.startsWith("/resources")
                  ? "w-full"
                  : "group-hover:w-full"
              }`}
            ></span>

            {/* Resources Mega Menu */}
            {megaMenuOpen === "resources" && (
              <div
                id="resources-mega-menu"
                className="mega-menu-container absolute right-0 top-full z-50 mt-2 w-[400px] rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 p-6 shadow-lg animate-in fade-in-50 slide-in-from-top-5 duration-300"
              >
                <ul className="space-y-2">
                   <li>
                    <Link
                      href="/resources/academic-integrity-monitor"
                      className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                      onClick={closeMegaMenu}
                    >
                      <div className="p-1.5 rounded-md bg-green-50 dark:bg-green-900/20">
                        <Lightbulb className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          Academic Integrity
                        </span>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Monitor and analyze student submissions
                        </p>
                      </div>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/about"
                      className="flex items-start gap-3 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200"
                      onClick={closeMegaMenu}
                    >
                      <div className="p-1.5 rounded-md bg-amber-50 dark:bg-amber-900/20">
                        <Info className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                      </div>
                      <div>
                        <span className="font-medium text-gray-900 dark:text-gray-100">
                          About Us
                        </span>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Learn about our mission and technology
                        </p>
                      </div>
                    </Link>
                  </li>
                </ul>
              </div>
            )}
          </div>

          {/* Regular Links with hover effects */}
          <div className="relative group">
            <Link
              href="/pricing"
              className={`text-sm font-medium transition-colors group-hover:text-blue-600 dark:group-hover:text-blue-400 ${
                pathname === "/pricing"
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300"
              }`}
            >
              Pricing
            </Link>
            <span
              className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 transition-all duration-300 ${
                pathname === "/pricing" ? "w-full" : "group-hover:w-full"
              }`}
            ></span>
          </div>

          <div className="relative group">
            <Link
              href="/contact"
              className={`text-sm font-medium transition-colors group-hover:text-blue-600 dark:group-hover:text-blue-400 ${
                pathname === "/contact"
                  ? "text-blue-600 dark:text-blue-400"
                  : "text-gray-700 dark:text-gray-300"
              }`}
            >
              Contact
            </Link>
            <span
              className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-blue-600 dark:bg-blue-400 transition-all duration-300 ${
                pathname === "/contact" ? "w-full" : "group-hover:w-full"
              }`}
            ></span>
          </div>
        </nav>

        <div className="flex items-center gap-3">
          <ModeToggle />
          {isLoggedIn ? (
            <UserButton />
          ) : (
            <div className="hidden md:flex items-center gap-3">
              <Link
                href="https://app.studentaidetector.com/login"
                target="_blank"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white transition-colors"
                >
                  Log in
                </Button>
              </Link>
              <Link
                href="https://app.studentaidetector.com/signup"
                target="_blank"
              >
                <Button
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white shadow transition-all hover:-translate-y-0.5"
                >
                  Sign up
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
